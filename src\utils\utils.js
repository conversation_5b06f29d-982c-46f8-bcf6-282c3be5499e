import self from "./utils";
export default {
  router: [
    {
      name: "Topo管理",
      id: "topoManager",
      key: "topoManager",
      children: [
        { name: "Topo编辑", id: "topoEdit", url: "/topoEdit", key: "" },
        {
          name: "对象管理",
          id: "objectManager",
          url: "/objectManager",
          key: "",
        },
      ],
    },
  ],
  httpHead: document.location.protocol === "https:" ? "https" : "http",

  /**
   * json结构转换为FormData对象
   * @param json
   * @returns {FormData}
   */
  json2formData: (json) => {
    const formData = new FormData();
    for (let key in json) {
      formData.append(key, json[key]);
    }
    return formData;
  },

  /**
   * FormData对象转换为json
   * @param fd
   */
  formData2json: (fd) => {
    let json = {};
    fd.forEach((val, key) => {
      json[key] = val;
    });
    return json;
  },
  /**
   *用于生成uuid
   */

  guid: function () {
    function S4() {
      //	return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);

      return ((1 + Math.random()) * 10000 + "").substring(0, 4);
    }

    return S4() + S4();
    //return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
  },
  /**
   *短版uuid
   */
  guidSmall: function () {
    function S4() {
      //		return (((1 + Math.random()) * 10000) | 0).toString(16).substring(1);
      return ((1 + Math.random()) * 10000 + "").substring(0, 4);
    }

    return S4() + S4();
    //return 'COMP' + (S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4());
  },
  /**
   *短版uuid
   */
  uuid: function () {
    function S4() {
      //		return (((1 + Math.random()) * 10000) | 0).toString(16).substring(1);
      return ((1 + Math.random()) * 10000 + "").substring(0, 4);
    }

    return S4() + S4() + S4();
    //return 'COMP' + (S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4());
  },

  /**
   * Date转化为指定格式的String
   * 月(M)，日(d)，12小时(h)，24小时(H)，分(m)，秒(s)，星期(E)，季度(q)，年(y)，毫秒(S)
   * 其中E可以用1~3个占位符，表示二/周二/星期二
   * @param date, fmt
   * @returns {*}
   */
  formatDate: function (date, fmt) {
    let o = {
      "M+": date.getMonth() + 1,
      "d+": date.getDate(),
      "h+": date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
      "H+": date.getHours(),
      "m+": date.getMinutes(),
      "s+": date.getSeconds(),
      "q+": Math.floor((date.getMonth() + 3) / 3),
      S: date.getMilliseconds(),
    };
    let week = {
      0: "/u65e5",
      1: "/u4e00",
      2: "/u4e8c",
      3: "/u4e09",
      4: "/u56db",
      5: "/u4e94",
      6: "/u516d",
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        (date.getFullYear() + "").substr(4 - RegExp.$1.length)
      );
    }
    if (/(E+)/.test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        (RegExp.$1.length > 1
          ? RegExp.$1.length > 2
            ? "/u661f/u671f"
            : "/u5468"
          : "") + week[date.getDay() + ""]
      );
    }
    for (let k in o) {
      if (new RegExp("(" + k + ")").test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1
            ? o[k]
            : ("00" + o[k]).substr(("" + o[k]).length)
        );
      }
    }
    return fmt;
  },
  /**
   * /下载文件
   * @param { fileName,content} [varname] [description]  文件名称  下载内容
   */

  downloadFile: function (fileName, content) {
    let aLink = document.createElement("a");
    let blob = new Blob([content], { type: "text/plain;charset=utf-8" });
    aLink.download = fileName;
    aLink.href = URL.createObjectURL(blob);
    aLink.click();
  },
  /**
   * 文件路径
   * @param {[name]} [varname] [description] 文件名称
   */

  scriptPath: function (name) {
    let scripts = document.getElementsByTagName("SCRIPT");
    let path = "";
    let pattern = new RegExp(name + ".js$");
    let pattern2 = new RegExp("(.*)" + name + ".js$");
    if (scripts && scripts.length > 0) {
      for (let i in scripts) {
        if (scripts[i].src && scripts[i].src.match(pattern)) {
          path = scripts[i].src.replace(pattern2, "$1");
        }
      }
    }
    return path;
  },

  /**
   * 格式化日期
   * @param {Date} data 日期
   * @return {String} 格式化日期
   */
  formatDateTime: function (date) {
    let y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? "0" + m : m;
    let d = date.getDate();
    d = d < 10 ? "0" + d : d;
    let h = date.getHours();
    let minute = date.getMinutes();
    let second = date.getSeconds();
    minute = minute < 10 ? "0" + minute : minute;
    return y + "-" + m + "-" + d + " " + h + ":" + minute + ":" + second;
  },

  /**
   * /深拷贝方法
   * @param  {[type]} initalObj [description] json对象
   * @return {[type]}           [description] 返回的对象
   */
  deepClone: function (data) {
    let obj = {};
    let originQueue = [data];
    let copyQueue = [obj];
    //以下两个队列用来保存复制过程中访问过的对象，以此来避免对象环的问题（对象的某个属性值是对象本身）
    let visitQueue = [];
    let copyVisitQueue = [];
    while (originQueue.length > 0) {
      let _data = originQueue.shift();
      let _obj = copyQueue.shift();
      visitQueue.push(_data);
      copyVisitQueue.push(_obj);
      for (let key in _data) {
        let _value = _data[key];
        if (typeof _value !== "object") {
          _obj[key] = _value;
        } else {
          //使用indexOf可以发现数组中是否存在相同的对象(实现indexOf的难点就在于对象比较)
          let index = visitQueue.indexOf(_value);
          if (index >= 0) {
            // 出现环的情况不需要再取出遍历
            _obj[key] = copyVisitQueue[index];
          } else {
            originQueue.push(_value);
            _obj[key] = {};
            copyQueue.push(_obj[key]);
          }
        }
      }
    }
    return obj;
  },
  /**
   * /判断参数是否为空
   * @param  {[obj]} 传入对象
   * @return 返回结果
   */
  isEmpty: function (obj) {
    if (!obj && obj !== 0 && obj !== "") {
      return true;
    }
    // eslint-disable-next-line no-prototype-builtins
    if (Array.prototype.isPrototypeOf(obj) && obj.length === 0) {
      return true;
    }
    // eslint-disable-next-line no-prototype-builtins
    if (Object.prototype.isPrototypeOf(obj) && Object.keys(obj).length === 0) {
      return true;
    }
    return false;
  },

  //判断object是否为空
  isEmptyObject: function (obj) {
    for (let key in obj) {
      return false;
    }
    return true;
  },

  /**
   * 导入txt或者json为后缀的文件
   * @param {String} classNameOfInput 导入input按钮的ID
   * @param {Function} requestDataCallback 请求接口的回调函数
   */
  importFile: function (IDOfInput, requestDataCallback) {
    const inputDom = document.getElementById(IDOfInput);
    const filePath = inputDom.value;
    if (filePath.indexOf("txt") !== -1 || filePath.indexOf("json") !== -1) {
      const oFile = inputDom.files[0];
      // 文件读取操作
      const fileReader = new FileReader();
      fileReader.onload = (oFREvent) => {
        requestDataCallback(oFREvent.target.result);
      };
      fileReader.readAsText(oFile, "GB2312");
    }
  },
  //保证组件编码唯一
  compCode(classify, type, len) {
    if (len > 9999) {
      return;
    }
    return `${classify}${type}${len}`;
  },
  senceCode() {},
  getRepeat(arr, crr) {
    arr.reduce((brr, va) => {
      if (!brr.includes(va)) {
        brr.push(va);
      } else {
        crr.push(va);
      }
      return brr;
    }, []);
  },
  /**
   * 获取组件所有属性值，并返回处组件属性列表
   * @param opts  组件内部propertyDictionary  fatherDes上一层displayName  name一层name  optList组件属性数组列表
   */
  getPropList(opts, fatherDes = "", fatherName = "", optList) {
    opts.forEach((item) => {
      if (item.children) {
        let name = fatherName == "" ? item.name : `${fatherName}.${item.name}`;
        let displayName =
          fatherDes == ""
            ? item.displayName
            : `${fatherDes}_${item.displayName}`;
        self.getPropList(item.children, displayName, name, optList);
      } else {
        if (item.editable && item.show) {
          let name =
            fatherName == "" ? item.name : `${fatherName}.${item.name}`;
          let displayName =
            fatherDes == ""
              ? item.displayName
              : `${fatherDes}_${item.displayName}`;
          let obj = {
            displayName: displayName,
            name: name,
            type: item.type,
          };
          optList.push(obj);
        }
      }
    });
  },
};
