<template>
  <Row class="topo-obj-manger-layout">
    <div class="topo-obj-manger-left-view">
      <div
        style="
          text-align: left;
          font-size: 16px;
          font-weight: 700;
          margin-bottom: 10px;
        "
      >
        对象列表
      </div>
      <div class="topo-obj-manger-left-tree">
        <Tree
          :data="objList"
          @on-select-change="handleTreeChange"
          @on-contextmenu="handleContextMenu"
        >
          <template slot="contextMenu">
            <DropdownItem @click.native="handleContextMenuEdit"
              >编辑</DropdownItem
            >
            <DropdownItem
              @click.native="handleContextMenuDelete"
              style="color: #ed4014"
              >删除</DropdownItem
            >
          </template>
        </Tree>
      </div>
    </div>
    <div class="topo-obj-manger-right-view">
      <div class="topo-obj-table-header">
        <span>对象</span>
        <div>
          <Button
            type="primary"
            @click="$refs.addCompositionModal.show()"
            style="margin-right: 10px"
            >新增分组</Button
          >
          <Button type="primary" @click="$refs.addObjectModal.show()"
            >新增对象</Button
          >
        </div>
      </div>
      <Table border :columns="columns" :data="tableList">
        <template slot-scope="{ row }" slot="objImg">
          <img
            :src="getImageUrl(row.objImg)"
            width="30"
            :height="30 / row.imgScale"
          />
        </template>
        <template slot-scope="{ row }" slot="action">
          <Button
            type="primary"
            size="small"
            icon="md-create"
            style="margin-right: 10px"
            @click="edit(row)"
          ></Button>
          <Button
            type="primary"
            size="small"
            icon="md-trash"
            @click="delObj(row)"
          ></Button>
        </template>
      </Table>
      <div class="topo-obj-manger-page">
        <Page
          :total="tableData.length"
          :current="current"
          :page-size="pageSize"
          @on-change="handlePageChange"
          show-total
          show-elevator
        />
      </div>
    </div>
    <AddCompositionModal
      ref="addCompositionModal"
      @addSuccess="initData"
    ></AddCompositionModal>
    <AddObjectModal
      ref="addObjectModal"
      @addSuccess="initData"
      :groupList="groupList"
    ></AddObjectModal>
  </Row>
</template>

<script>
import _ from "lodash";
import AddCompositionModal from "@/components/Topo/Modal/AddCompositionModal.vue";
import AddObjectModal from "@/components/Topo/Modal/AddObjectModal.vue";
import { getImageUrl } from "@/components/Topo/utils";

export default {
  name: "Objectmanger",
  components: {
    AddCompositionModal,
    AddObjectModal,
  },
  data() {
    return {
      value: "",
      columns: [
        {
          title: "名称",
          key: "objName",
        },
        {
          title: "分组",
          key: "groupName",
        },
        {
          title: "关联组件",
          key: "compClass",
        },
        {
          title: "图标",
          slot: "objImg",
        },
        {
          title: "图标比例",
          key: "imgScale",
        },
        {
          title: "更新人",
          key: "updatedBy",
        },
        {
          title: "更新时间",
          key: "updatedTime",
        },
        {
          title: "Action",
          slot: "action",
          width: 150,
          align: "center",
        },
      ],
      tableData: [],
      objList: [],
      groupList: [],
      selectedGroupKey: "",
      current: 1,
      pageSize: 15,
    };
  },
  mounted() {
    this.initData();
  },
  computed: {
    tableList() {
      return this.tableData.slice(
        (this.current - 1) * this.pageSize,
        this.current * this.pageSize
      );
    },
  },
  methods: {
    getImageUrl,
    initData() {
      this.$get(`/topoEdit/getGroupList`).then(({ data }) => {
        if (data.code == "0000") {
          this.objList = [];
          this.generateObjList(_.cloneDeep(data.data));
          this.getGroupAndObjList(_.cloneDeep(data.data));
        }
      });
    },
    // 递归获取所有的目录以及子目录 ，生成tree
    generateObjList(data) {
      const getList = (list, menu) => {
        list.forEach((ele, index) => {
          if (ele.objName) {
            menu.push({
              title: ele.objName,
              key: ele.objType,
              isMap: true,
            });
          } else {
            menu.push({
              title: ele.groupName,
              key: ele.groupId,
              contextmenu: true,
            });
          }

          if (ele.objList && ele.objList.length) {
            if (!menu[index].children) {
              menu[index].children = [];
            }
            getList(ele.objList, menu[index].children);
          }
        });
      };
      getList(data, this.objList);
    },
    // 所有的对象列表和分组列表
    getGroupAndObjList(data) {
      let arr = [];
      for (let i = 0; i < data.length; i++) {
        const { objList, groupName } = data[i];
        if (objList && objList.length) {
          const res = objList.map((ele) => ({
            ...ele,
            groupName,
          }));
          arr = arr.concat(...res);
        }
      }
      this.tableData = arr;

      this.groupList = data.map((ele) => {
        const { groupId, groupName } = ele;
        return { value: groupId, label: groupName };
      });
    },
    // 点击树，获取当前目录下的所有图层
    handleTreeChange() {},
    edit(val) {
      this.$refs.addObjectModal.show(val);
    },
    handleContextMenu({ key }) {
      this.selectedGroupKey = key;
    },
    handleContextMenuEdit() {
      // this.$Message.info("Click edit of" + this.contextData.title);
    },
    handleContextMenuDelete() {
      this.delGroup();
    },
    delObj({ objType }) {
      this.$Modal.confirm({
        title: "警告",
        content: "是否要删除该对象",
        onOk: () => {
          this.$post(`/topoEdit/deleteObj`, { objType }).then((data) => {
            if (data.code == "0000") {
              this.$Message.success("删除成功");
              this.initData();
            }
          });
        },
        onCancel: () => {},
      });
    },
    delGroup() {
      this.$Modal.confirm({
        title: "警告",
        content: "是否要删除该分组",
        onOk: () => {
          this.$post(`/topoEdit/deleteGroup`, {
            groupId: this.selectedGroupKey,
          }).then((data) => {
            if (data.code == "0000") {
              this.$Message.success("删除成功");
              this.initData();
              this.selectedGroupKey = "";
            }
          });
        },
        onCancel: () => {},
      });
    },
    handlePageChange(val) {
      this.current = val;
    },
  },
};
</script>

<style lang="scss" scoped>
.topo-obj-manger-layout {
  padding: 0 10px;
  height: 100%;
  background-color: $cardBg;
  overflow: auto;
}
.topo-obj-manger-left-view {
  position: fixed;
  left: 0;
  width: 300px;
  height: 100%;
  overflow: auto;
  border-right: 1px solid #ccc;
  padding: 10px;
}
.topo-obj-manger-right-view {
  padding: 10px;
  margin-left: 300px;
  width: calc(100% - 300px);
}
.topo-obj-manger-left-tree {
  text-align: left;
}
.topo-obj-manger-page {
  text-align: right;
  margin-top: 10px;
}
.topo-obj-table-header {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 10px;
  display: flex;
  width: 100%;
  justify-content: space-between;
}
</style>
