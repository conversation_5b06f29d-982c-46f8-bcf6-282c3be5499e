<template>
  <Modal
    v-model="isModelVisible"
    draggable
    scrollable
    :closable="false"
    :mask="false"
    :footer-hide="true"
    title="模型数据"
    width="350"
    :z-index="6"
    :styles="{ marginRight: '20px', top: '60px' }"
    class-name="base-modal"
  >
    <div class="model-container">
      <div class="model-seach-save">
        <Input
          v-model="searchName"
          search
          enter-button
          @on-enter="searchModelList"
          @on-search="searchModelList"
          size="small"
          class="model-input"
          placeholder="模型查询"
        />
        <Button type="primary" size="small" @click="saveModel" class="mr-5"
          >保存</Button
        >
        <Button type="primary" size="small" @click="saveModel('multi')"
          >批量</Button
        >
      </div>

      <div class="model-seach-save" style="width: 195px">
        <Input
          v-model="filterName"
          size="small"
          class="model-input"
          placeholder="关键字筛选"
          clearable
        />
      </div>
      <div class="mt-5">
        <ul v-show="modelCategoryList.length" class="model-list">
          <Collapse simple>
            <Panel v-for="(item, index) in modelCategoryList" :key="index">
              <span v-show="item">{{ item.title }}</span>
              <div slot="content">
                <ul>
                  <li
                    v-for="(itemCild, itemIndex) in item.list"
                    :key="`${index}-${itemIndex}`"
                    :class="[
                      {
                        'model-item-selected':
                          activeKey === `${index}-${itemIndex}`,
                      },
                      'model-item',
                    ]"
                    @click="handleModelClick(itemCild, `${index}-${itemIndex}`)"
                  >
                    <span> {{ itemCild.name }}</span>
                    <Icon
                      type="md-alert"
                      size="20"
                      @click.stop="showModelDetail(itemCild)"
                    />
                  </li>
                </ul>
              </div>
            </Panel>
          </Collapse>
        </ul>
        <span v-show="!modelCategoryList.length" class="ml-5">暂无数据</span>
      </div>
    </div>
    <Modal v-model="isVisible" title="详情">
      <div class="model-detail-list">
        <div
          v-for="(item, index) in modelDetail"
          :key="index"
          class="model-detail-item"
        >
          <span class="mr-5">{{ item.key }}</span>
          <span>{{ item.value }}</span>
        </div>
      </div>
      <div slot="footer">
        <Button type="text" @click="isVisible = false">取消</Button>
      </div>
    </Modal>
    <UpdateMetaDataModal ref="updateMetaDataModalRef"></UpdateMetaDataModal>
  </Modal>
</template>

<script>
import { mapState, mapActions } from "vuex";
import UpdateMetaDataModal from "@/components/Topo/Modal/Multi/UpdateMetaDataModal.vue";

export default {
  name: "ModelBind",
  props: {},
  components: { UpdateMetaDataModal },
  data() {
    return {
      searchName: "",
      filterName: "",
      modelSelected: {},
      activeKey: "",
      nodeLinkSelected: [],
      modelDetail: [],
      isVisible: false,
      isModelVisible: false,
    };
  },

  mounted() {
    this.initEvent();
  },
  beforeDestroy() {
    this.$bus.off("onNodeLinkSelected");
  },
  computed: {
    ...mapState("topoStore", {
      modelData: "modelData",
    }),
    isEmpty() {
      let num = 0;
      for (const key in this.modelData) {
        const ele = this.modelData[key];

        num += ele ? ele.length : 0;
      }
      return !num;
    },
    modelCategoryList() {
      const list = [];
      const modelType = {
        acLineEndList: "线端",
        acLineList: "线路",
        substationList: "变电站",
        converSubstationList: "换流站",
        plantList: "电厂",
        areaList: "分区",
        sectionList: "断面",
        tNodeList: "T节点",
        loadList: "负荷线路",
        loadEndList: "负荷线端",
        breakerList: "开关",
      };
      for (const key in this.modelData) {
        list.push({
          title: modelType[key],
          list: this.modelData[key]
            .filter((model) => model.name.includes(this.filterName))
            .sort((a, b) => a.name.localeCompare(b.name)),
        });
      }
      return list.filter((item) => item.list.length);
    },
  },
  methods: {
    ...mapActions("topoStore", ["getModelList"]),
    initEvent() {
      this.$bus.on("handleSingleNodeSelected", (val) => {
        this.nodeLinkSelected = val;
      });
    },
    handleModelClick(val, key) {
      if (this.activeKey === key) {
        this.modelSelected = {};
        this.activeKey = "";
      } else {
        this.modelSelected = val;
        this.activeKey = key;
      }
    },
    // 定义比较函数
    compare(a, b) {
      // 提取英文部分
      const aStr = a.key.replace(/[0-9]/g, "");
      const bStr = b.key.replace(/[0-9]/g, "");

      // 比较英文部分
      if (aStr < bStr) {
        return -1;
      }
      if (aStr > bStr) {
        return 1;
      }
      let aNum = a.key.replace(/[^0-9]/g, "");
      let bNum = b.key.replace(/[^0-9]/g, "");
      if (aNum === "" || aNum === null || aNum === undefined) {
        aNum = -1;
      }
      if (bNum === "" || aNum === null || aNum === undefined) {
        bNum = -1;
      }
      aNum = parseInt(aNum);
      bNum = parseInt(bNum);

      // 比较数字部分
      if (aNum < bNum) {
        return -1;
      }
      if (aNum > bNum) {
        return 1;
      }
    },
    generateModelList(metaData) {
      let list = [];
      for (const key in metaData) {
        list.push({
          key,
          value: metaData[key],
        });
      }
      this.modelDetail = list.sort(this.compare);
    },
    showModelDetail(val) {
      this.isVisible = true;
      this.generateModelList(val);
    },
    searchModelList() {
      if (!this.searchName.trim()) {
        return this.$Message.warning("输入不能为空");
      }
      this.modelSelected = {};
      this.activeKey = "";
      this.getModelList(this.searchName);
    },
    saveModel(type) {
      //   if (!this.modelSelected.name) {
      //     return this.$Message.warning("请选择模型数据");
      //   }
      //   if (!this.nodeLinkSelected.mapId) {
      //     return this.$Message.warning("请选择元素");
      //   }

      const linkList = [];
      const nodeList = [];
      const { id, layerId } = this.nodeLinkSelected.metaData || {};
      this.nodeLinkSelected.metaData = Object.assign(this.modelSelected, {
        id,
        layerId,
      });

      // 批量更新
      if (type === "multi") {
        this.$refs.updateMetaDataModalRef.show(this.nodeLinkSelected);
        return;
      }

      if (this.nodeLinkSelected.nodeId) {
        nodeList.push(this.nodeLinkSelected);
      } else {
        linkList.push(this.nodeLinkSelected);
      }

      let p1,
        p2 = null;
      if (nodeList.length) {
        p1 = this.$post(`/topoEdit/updateNode`, { nodeList });
      }
      if (linkList.length) {
        p2 = this.$post(`/topoEdit/updateLink`, { linkList });
      }
      Promise.all([p1, p2])
        .then(() => {
          this.$Message.success("绑定成功");
        })
        .catch(() => {
          this.$Message.error("绑定失败");
        })
        .finally(() => {
          this.$Spin.hide();
        });
    },
    showModelModal() {
      this.isModelVisible = !this.isModelVisible;
    },
  },
};
</script>

<style lang="scss">
.ivu-collapse-header {
  color: $textColor !important;
}

.model-container {
  height: 70vh;
  padding-top: 10px;
}
.model-seach-save {
  display: flex;
}
.model-input {
  margin: 0 10px 10px 10px;
}
.model-title {
  display: block;
  width: fit-content;
  padding: 3px 5px;
  margin-bottom: 5px;
  background-color: #70c0e8;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  font-size: 12px;
}
.model-item {
  padding: 5px 10px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:hover {
    background-color: #95a1ad73;
  }
}
.model-item-selected {
  background-color: #95a1ad;
  &:hover {
    background-color: #95a1ad;
  }
}
.model-list {
  height: calc(70vh - 90px);
  overflow: auto;
}
.model-detail-list {
  max-height: 300px;
  overflow-y: auto;
}
.model-detail-item {
  width: 95%;
  display: flex;
  justify-content: space-between;
  padding: 5px;
  span {
    flex: 1;
    word-break: break-all;
  }
}
</style>
