<template>
  <Modal
    v-model="isVisible"
    title="数据录入"
    width="1000"
    draggable
    scrollable
    :mask="false"
    :closable="false"
  >
    <div>
      <div style="display: flex; justify-content: space-between">
        <DatePicker
          v-model="dateValue"
          type="month"
          placeholder="选择时间"
          style="width: 200px"
          @on-change="handleDateChange"
        ></DatePicker>

        <Button
          style="margin-left: 20px"
          type="primary"
          @click="showInnerModal()"
          >新增</Button
        >
      </div>

      <Divider />

      <Table border :columns="columns" :data="tableData" :height="500">
        <template #type="{ row }">
          {{ row.type === 1 ? "基建" : "检修" }}
        </template>
        <template #action="{ row, index }">
          <Button type="primary" size="small" @click="showInnerModal(row)"
            >查看</Button
          >
          <Button
            type="error"
            size="small"
            @click="deleteData(row, index)"
            style="margin-left: 10px"
            >删除</Button
          >
        </template>
      </Table>
    </div>
    <div slot="footer">
      <Button type="text" @click="hide">取消</Button>
    </div>

    <Modal
      v-model="isInnerVisible"
      title="数据编辑"
      draggable
      scrollable
      width="700"
      :mask="false"
      :closable="false"
      class="data-input-inner-modal"
    >
      <Form
        ref="form"
        :model="formVal"
        :label-width="80"
        style="
          width: 100%;
          height: 370px;
          padding-right: 20px;
          overflow: hidden;
        "
      >
        <Row>
          <Col span="24"
            ><FormItem label="设备ID">
              <Input
                type="textarea"
                v-model="formVal.deviceId"
                :autosize="{ minRows: 2, maxRows: 3 }"
                placeholder="设备id，多个id逗号分隔"
              ></Input> </FormItem
          ></Col>
        </Row>
        <Row>
          <Col span="12">
            <FormItem label="开始时间">
              <DatePicker
                v-model="formVal.startDate"
                placeholder="选择时间"
                style="width: 100%"
              ></DatePicker> </FormItem
          ></Col>
          <Col span="12">
            <FormItem label="结束时间">
              <DatePicker
                v-model="formVal.endDate"
                placeholder="选择时间"
                style="width: 100%"
              ></DatePicker></FormItem
          ></Col>
        </Row>
        <Row>
          <Col span="24">
            <FormItem label="类型">
              <Select v-model="formVal.type" style="width: 100%">
                <Option :value="1">基建 </Option>
                <Option :value="2">检修 </Option>
              </Select>
            </FormItem></Col
          >
        </Row>
        <Row>
          <Col span="24">
            <FormItem label="内容">
              <Input
                v-model="formVal.content"
                type="textarea"
                style="width: 100%"
                :autosize="{ minRows: 5, maxRows: 5 }"
                placeholder="输入内容"
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div slot="footer">
        <Button type="text" @click="isInnerVisible = false">取消</Button>
        <Button type="primary" @click="saveDate">{{
          formVal.id === null ? "保存" : "更新"
        }}</Button>
      </div>
    </Modal>
  </Modal>
</template>

<script setup>
import { ref } from "vue";
import { post, get } from "@/utils/http";
import dayjs from "dayjs";
import _ from "lodash";

const columns = [
  {
    title: "序号",
    key: "no",
    width: 70,
  },
  {
    title: "设备ID",
    key: "deviceId",
    width: 200,
  },
  {
    title: "开始时间",
    width: 120,
    key: "startDate",
  },
  {
    title: "结束时间",
    width: 120,
    key: "endDate",
  },
  {
    title: "类型",
    width: 65,
    slot: "type",
  },
  {
    title: "内容",
    key: "content",
  },
  {
    title: "操作",
    slot: "action",
    width: 140,
  },
];

const isVisible = ref(false);
const isInnerVisible = ref(false);
const dateValue = ref(new Date());
const formVal = ref({
  id: null,
  deviceId: "",
  startDate: "",
  endDate: "",
  type: 1,
  content: "",
});

const tableData = ref([]);

const getRiskInfoList = () => {
  const date = dayjs(dateValue.value).format("YYYY-MM");
  get("/topoEdit/getRiskInfoList", { date }).then(({ data }) => {
    tableData.value = data.data.map((ele, index) => {
      return {
        ...ele,
        no: index + 1,
      };
    });
  });
};

const addRiskInfo = (val) => {
  post("/topoEdit/insertRiskInfo", val).then((res) => {
    if (res.code === "0000") {
      window.$Message.success("新增成功");
      getRiskInfoList();
      isInnerVisible.value = false;
    }
  });
};

const updateRiskInfo = (val) => {
  post("/topoEdit/updateRiskInfo", val).then((res) => {
    if (res.code === "0000") {
      window.$Message.success("更新成功");
      getRiskInfoList();
      isInnerVisible.value = false;
    }
  });
};

const handleDateChange = () => {
  getRiskInfoList();
};

const resetForm = () => {
  formVal.value = {
    id: null,
    deviceId: "",
    startDate: "",
    endDate: "",
    type: 1,
    content: "",
  };
};

// 保存
const saveDate = () => {
  const params = {
    ...formVal.value,
    startDate: dayjs(formVal.value.startDate).format("YYYY-MM-DD"),
    endDate: dayjs(formVal.value.endDate).format("YYYY-MM-DD"),
  };
  if (formVal.value.id === null) {
    addRiskInfo({
      ...params,
      id: undefined,
    });
  } else {
    updateRiskInfo(params);
  }
};

//删除
const deleteData = (item, index) => {
  window.$Modal.confirm({
    title: "警告",
    content: "是否要删除该条数据",
    onOk: async () => {
      post("/topoEdit/deleteRiskInfoById", { id: item.id }).then((res) => {
        if (res.code === "0000") {
          window.$Message.success("删除成功");
          getRiskInfoList();
        }
      });
    },
    onCancel: () => {},
  });
};

const showInnerModal = (item) => {
  item ? (formVal.value = _.cloneDeep(item)) : resetForm();
  isInnerVisible.value = true;
};

const show = () => {
  isVisible.value = true;
  getRiskInfoList();
};

const hide = () => {
  isVisible.value = false;
};
const submit = () => {
  console.log("formDynamic", formDynamic.value);
};

defineExpose({
  show,
});
</script>

<style lang="scss">
.data-input-inner-modal .ivu-modal-content {
  background: #343434 !important;
}
</style>
