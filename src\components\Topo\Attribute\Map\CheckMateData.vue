<template>
  <Row type="flex" align="middle" class="topo-node-property-item">
    <Col span="8" style="text-align: right">过滤：</Col>
    <Col span="16" style="text-align: left">
      <CheckboxGroup v-model="filterVal" @on-change="handleFilterChange">
        <Checkbox label="1">有功值</Checkbox>
        <Checkbox label="2">电厂/站</Checkbox>
        <Checkbox label="3">T节点</Checkbox>
        <Checkbox label="4">线路</Checkbox>
      </CheckboxGroup></Col
    >
  </Row>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import { formatColor, isColorInGreenRange } from "@/utils/tools/format";
import {
  highlightNode,
  highlightLink,
  clearHighlight,
} from "@/utils/svg/highlight.js";

export default {
  data() {
    return {
      filterVal: [],
    };
  },
  mounted() {},
  beforeDestroy() {},
  watch: {
    nodeLinkBysyblayerList: {
      handler() {
        this.handleFilterChange();
      },
      deep: true,
    },
  },
  computed: {
    ...mapState("topoStore", {
      nodeLinkBysyblayerList: "nodeLinkBysyblayerList",
    }),
  },
  methods: {
    ...mapMutations("topoStore", []),
    initEventBus() {},
    handleFilterChange(val = this.filterVal) {
      clearHighlight();

      const nodes = [];
      let links = [];
      if (val.includes("1") || val.includes("2") || val.includes("3")) {
        const nodeList = this.nodeLinkBysyblayerList.filter(
          (item) => item.nodeId
        );
        if (val.includes("1")) {
          const list = nodeList.filter(
            (item) =>
              item.nodeType === "text" &&
              isColorInGreenRange(item.fontColor) &&
              (!item.metaData || (item.metaData && !item.metaData.rtKeyId))
          );
          nodes.push(...list);
        }
        if (val.includes("2")) {
          const list = nodeList.filter(
            (item) =>
              item.nodeType !== "text" &&
              item.nodeType !== "t" &&
              (!item.metaData || (item.metaData && !item.metaData.rtKeyId0))
          );
          nodes.push(...list);
        }
        if (val.includes("3")) {
          const list = nodeList.filter(
            (item) =>
              item.nodeType === "t" &&
              (!item.metaData || (item.metaData && !item.metaData.rtKeyId))
          );
          nodes.push(...list);
        }
      }

      if (val.includes("4")) {
        links = this.nodeLinkBysyblayerList.filter(
          (item) =>
            item.linkId &&
            (!item.metaData || (item.metaData && !item.metaData.rtKeyId0))
        );
      }

      highlightNode(nodes);
      highlightLink(links);
    },
  },
};
</script>
<style lang="scss"></style>
