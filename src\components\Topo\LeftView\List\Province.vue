<template>
  <div class="topo-el-list">
    <input
      type="file"
      ref="formUpload"
      accept=".json,.g"
      style="display: none"
      action="/topoEdit/importMap"
      @change="importFile"
    />
    <Tree
      :data="mapSearchMapInput ? mapSearchMapList : menuList"
      @on-select-change="handleMenuTreeClick"
      @on-contextmenu="handleMenuContextMenu"
    >
      <template slot="contextMenu">
        <DropdownItem @click.native="handleMenuContextEdit">编辑</DropdownItem>
        <DropdownItem @click.native="handleExportImport">
          {{ selectedMenu.isMap ? "导出" : "导入" }}
        </DropdownItem>
        <DropdownItem
          @click.native="handleMenuContextDelete"
          style="color: #ed4014"
          >删除</DropdownItem
        >
      </template>
    </Tree>

    <AddMenuModal
      ref="addMenuModal"
      :menuData="cascaderData"
      @addSuccess="fetchMenuList()"
      @updateSuccess="fetchMenuList()"
    />
    <AddMapModal
      ref="addMapModal"
      :menuData="cascaderData"
      @addSuccess="fetchMenuList()"
      @updateSuccess="handleUpdateMap"
    />
    <Modal
      title="导出选项"
      v-model="isExportModalVisible"
      :mask-closable="false"
      :closable="false"
    >
      <p v-if="isExportAllData">注意：导出所有数据</p>
      <p v-else>注意：导出所选子图层数据</p>
      <Checkbox v-model="isExportAllData" style="margin-top: 10px"
        >导出全部</Checkbox
      >
      <div slot="footer">
        <Button @click="exportSvgData" type="primary"> 确定 </Button>
        <Button @click="isExportModalVisible = false"> 取消 </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import AddMapModal from "../../Modal/Menu/AddMapModal.vue";
import AddMenuModal from "../../Modal/Menu/AddMenuModal.vue";

export default {
  components: { AddMapModal, AddMenuModal },

  data() {
    return {
      menuList: [],
      mapSearchMapInput: "",
      mapSearchNodeInput: "",
      mapSearchMapList: [],
      cascaderData: [],
      selectedMenu: {},
      selectedNode: {},
      selectedMap: {},
      isExportModalVisible: false,
      isExportAllData: true,
    };
  },
  mounted() {
    this.initEventBus();
    this.initData();
  },
  beforeDestroy() {
    this.$bus.off("TopoAddNode");
    this.setVersionSelected();
  },

  computed: {
    ...mapState("topoStore", {
      menuData: "menuData",
      mapList: "mapList",
      isMapFileVisible: "isMapFileVisible",
      versionSelected: "versionSelected",
      mapInfo: "mapInfo",
      nodeLinkBysyblayerList: "nodeLinkBysyblayerList",
      mapInfoByScale: "mapInfo",
    }),
  },
  methods: {
    ...mapMutations("topoStore", [
      "setMenuData",
      "setMapList",
      "setVersionSelected",
      "setMapType",
      "setMenuMapList",
    ]),
    ...mapActions("topoStore", ["getMapInfo", "getVersionList"]),
    // 获取初始化数据
    initData() {
      this.fetchMenuList();
    },
    initEventBus() {
      this.$bus.on("onVersionSlected", () => {
        this.fetchMapInfo(this.mapInfo.mapId);
      });
    },
    handleNodeTreeClick(keys, val) {
      this.$bus.emit("handleTreeNodeSelected", val);
    },
    // 获取图层列表
    fetchMenuList() {
      this.$get(`/topoEdit/getMenuList`).then(({ data }) => {
        if (data.code == "0000") {
          this.menuList = [];
          this.cascaderData = [];
          this.setMenuData(data.data);
          this.generateMenuList(data.data);
        }
      });
    },
    // 递归获取所有的目录以及子目录 ，生成tree
    generateMenuList(d) {
      let mapList = [];
      const getList = (list, menu, tree) => {
        list.forEach((ele, index) => {
          if (!ele.menuId) return;
          menu.push({
            title: ele.menuName || "空",
            key: ele.menuId,
            menuParId: ele.menuParId,
            contextmenu: true,
            isMap: !!ele.mapId,
            render: (h, { root, node, data }) => {
              return h(
                "span",
                {
                  style: {
                    display: "inline-block",
                    width: "100%",
                  },
                },
                [
                  h("span", [
                    h("Icon", {
                      props: {
                        type: "ios-folder-outline",
                      },
                      style: {
                        marginRight: "8px",
                      },
                    }),
                    h("span", data.title),
                  ]),
                ]
              );
            },
          });
          tree.push({ value: ele.menuId, label: ele.menuName });

          if (ele.children && ele.children.length) {
            if (!menu[index].children) {
              menu[index].children = [];
            }
            if (!tree[index].children) {
              tree[index].children = [];
            }
            getList(ele.children, menu[index].children, tree[index].children);
          }

          if (ele.maps && ele.maps.length) {
            if (!menu[index].children) {
              menu[index].children = [];
            }
            ele.maps.forEach((eleMap) => {
              mapList.push(eleMap);
              if (ele.menuId === eleMap.menuId) {
                menu[index].children.push({
                  title: eleMap.mapName,
                  key: eleMap.mapId,
                  isMap: true,
                  contextmenu: true,
                  render: (h, { root, node, data }) => {
                    return h(
                      "span",
                      {
                        style: {
                          display: "inline-block",
                          width: "100%",
                        },
                      },
                      [
                        h("span", [
                          h("Icon", {
                            props: {
                              type: "ios-paper-outline",
                            },
                            style: {
                              marginRight: "8px",
                            },
                          }),
                          h("span", data.title),
                        ]),
                      ]
                    );
                  },
                });
              }
            });
          }
        });
      };
      getList(d, this.menuList, this.cascaderData);
      console.log("🚀 ~ generateMenuList ~ this.menuList:", this.menuList);

      this.setMapList(mapList);
      this.setMenuMapList(this.menuList);
    },
    fetchMapInfo(mapId, isRefresh) {
      this.getMapInfo(mapId).then((val) => {
        this.$bus.emit("onMapSlected", { ...val, isRefresh });
      });
    },
    handleMenuTreeClick(keys, item) {
      if (item.isMap) {
        this.setMapType("province");
        this.fetchMapInfo(item.key);
        this.setVersionSelected();
        this.getVersionList(item.key);
      }
    },
    // 更新图层后
    handleUpdateMap(mapInfo) {
      this.$bus.emit("onMapSlected", mapInfo);
      this.fetchMenuList();
    },
    addMenu() {
      this.$refs.addMenuModal.show();
    },
    addMap() {
      this.$refs.addMapModal.show();
    },
    handleMenuContextMenu(val) {
      // const {isMap, key} = val
      this.selectedMenu = val;
      // this.selectedMenuKey = key;
    },
    handleMenuContextEdit() {
      const { isMap, key } = this.selectedMenu;
      if (isMap) {
        let mapInfo = this.mapList.find((ele) => ele.mapId === key);

        if (key === this.mapInfoByScale.mapId) {
          mapInfo = this.mapInfoByScale;
        }

        this.$refs.addMapModal.show(mapInfo);
      } else {
        // 修改分组
        this.$refs.addMenuModal.show(this.selectedMenu);
      }
    },
    // 获取节点和连线的列表
    getNodeLinkList(mapId) {
      return new Promise((resolve, reject) => {
        // 导出全部
        if (this.isExportAllData) {
          this.$get("/topoEdit/getNodeLinkListByMapId", {
            mapId,
          })
            .then(({ data }) => {
              if (data.code == "0000") {
                resolve(data.data);
              } else {
                reject();
              }
            })
            .catch((err) => {
              reject();
            });
        } else {
          const nodes = [];
          const links = [];
          this.nodeLinkBysyblayerList.forEach((ele) => {
            if (ele.nodeId) {
              nodes.push(ele);
            } else {
              links.push(ele);
            }
          });
          resolve({ nodes, links });
        }
      });
    },
    getSublayerList(mapId) {
      return new Promise((resolve, reject) => {
        this.$get(`/topoEdit/getExistSublayerList`, {
          mapId,
          versionId: this.versionSelected.versionId,
        })
          .then(({ data }) => {
            if (data.code == "0000") {
              resolve(data.data);
            } else {
              reject();
            }
          })
          .catch(() => {
            reject();
          });
      });
    },
    downloadFile(content, filename) {
      // 创建隐藏的可下载链接
      var eleLink = document.createElement("a");
      eleLink.download = filename;
      eleLink.style.display = "none";
      // 字符内容转变成blob地址
      var blob = new Blob([content]);
      eleLink.href = URL.createObjectURL(blob);
      // 触发点击
      document.body.appendChild(eleLink);
      eleLink.click();
      // 然后移除
      document.body.removeChild(eleLink);
    },
    //  导入
    handleSvgImport() {
      //   const { key } = this.selectedMenu;
      //   this.mapInfo = this.mapList.find((ele) => ele.mapId === key);
      //   svg数据（json）导入，弹窗，可配置是否选择某个图层导入
      //   if (this.selectedMenu.isMap) {
      //     this.$refs.formPartUpload.click();
      //   } else {
      // 全部数据导入
      this.$refs.formUpload.click();
      //   }
    },
    // 导入
    importFile(e) {
      const input = e.target;
      if (!input.files.length) return;
      let file = input.files[0];
      this.insertData(file);
    },
    insertData(file) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("menuId", this.selectedMenu.key);

      let config = "headers: {Content-Type: multipart/form-data}";
      this.$post("/topoEdit/importMap", formData, config).then((res) => {
        if (res.code === "0000") {
          this.$Message.success("导入图层成功");
        }
        this.initData();
      });
      this.$refs.formUpload.value = "";
    },
    addNode(params, type) {
      const url = type === "path" ? "insertLink" : "insertNode";
      this.$post(`/topoEdit/${url}`, params)
        .then((result) => {})
        .catch((err) => {
          this.$Message.error("导入图层失败", err);
        })
        .finally(() => {
          this.$refs.formUpload.value = "";
          this.$Spin.hide();
        });
    },
    getHotspotList(mapId) {
      return new Promise((resolve, reject) => {
        this.$get(`/topoEdit/getHotspotsList`, {
          mapId,
          versionId: this.versionSelected.versionId,
        })
          .then(({ data }) => {
            if (data.code === "0000") {
              resolve(data.data);
            } else {
              reject();
            }
          })
          .catch(() => {
            reject();
          });
      });
    },
    // 导出
    exportSvgData() {
      let { key, title } = this.selectedMenu;
      title += this.isExportAllData ? "（全部）" : "（子图层）";
      const mapInfo = this.mapList.find((ele) => ele.mapId === key);
      const p1 = this.getSublayerList(key);
      const p2 = this.getNodeLinkList(key);
      const p3 = this.getHotspotList(key);
      if (!mapInfo || !mapInfo.mapId) {
        this.$Message.error("图层信息为空，导出失败");
        return;
      }
      Promise.all([p1, p2, p3])
        .then(([sublayers, nodeLinkList, hotspots]) => {
          this.downloadFile(
            JSON.stringify({
              mapInfo,
              mapContent: nodeLinkList,
              sublayers,
              hotspots,
            }),
            title + ".g"
          );
        })
        .catch(() => {
          this.$Message.error("导出失败");
        })
        .finally(() => {
          this.isExportModalVisible = false;
          this.isExportAllData = true;
        });
    },
    //  menu 导入, map 导出
    handleExportImport() {
      const { isMap, key, title } = this.selectedMenu;
      const mapInfo = this.mapList.find((ele) => ele.mapId === key);
      if (isMap) {
        this.isExportModalVisible = true;
      } else {
        // 导入
        this.$refs.formUpload.click();
      }
    },
    handleMenuContextDelete() {
      this.delMenu();
    },
    // 左侧树状图删除节点
    handleContextNodeDelete() {
      // this.selectedNode
      this.$bus.emit("handleTreeNodeDelete", this.selectedNode);
      this.selectedNode = {};
    },
    delMenu() {
      const { isMap, key } = this.selectedMenu;
      this.$Modal.confirm({
        title: "警告",
        content: `是否要删除该${isMap ? "图层" : "目录"}`,
        onOk: () => {
          const url = isMap ? "deleteMap" : "deleteMenu";
          this.$post(`/topoEdit/${url}`, {
            [isMap ? "mapId" : "menuId"]: key,
          }).then((data) => {
            if (data.code == "0000") {
              if (isMap) {
                this.$bus.emit("onMapSlected", {});
              }
              this.$Message.success("删除成功");
              this.initData();
              this.selectedMap = {};
            }
          });
          this.selectedMenu = {};
        },
        onCancel: () => {},
      });
    },
    // 查找图层
    search(val) {
      this.mapSearchMapInput = val;
      let list = [];
      if (val) {
        list = this.mapList.filter((ele) => ele.mapName.includes(val));
      } else {
        // TODO 清空搜索条件后 需要做回显
        list = [];
        // selectedMap
      }
      this.mapSearchMapList = list.map((ele) => {
        return {
          title: ele.mapName,
          key: ele.mapId,
          isMap: true,
          contextmenu: true,
        };
      });
    },
  },
};
</script>
<style lang="scss"></style>
