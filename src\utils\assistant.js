import _ from "lodash";
import { generatePathD } from "@/components/Topo/utils";

export const getImageUrl = (type) => {
  const typeList = ["jianxiu", "ji"];
  if (typeList.includes(type)) {
    return require(`../assets/images/legend/${type}.svg`);
  } else {
    return "";
  }
};

export const getGFile = (nodeLinkList, mapInfo, compScale) => {
  const links = [];
  const nodes = [];
  _.cloneDeep(nodeLinkList).forEach((ele) => {
    if (ele.linkId) {
      ele.pathPoints = ele.pathPoints.map((elePoint) => {
        elePoint.x = elePoint.x * compScale;
        elePoint.y = elePoint.y * compScale;
        return elePoint;
      });
      const { linkStyles } = ele;
      const styles = linkStyles ? JSON.parse(linkStyles) : {};

      ele.linkPath = generatePathD(ele, styles.isBlock);
      ele.linkWidth = parseFloat((ele.linkWidth * compScale).toFixed(1));
      links.push(ele);
    } else if (ele.nodeId) {
      const { w, h, x, y, fontSize } = ele;
      ele.w = w * compScale;
      ele.h = h * compScale;
      ele.x = x * compScale;
      ele.y = y * compScale;
      ele.nodePosition = `${ele.x},${ele.y}`;
      ele.nodeSize = `${ele.w}*${ele.h}`;
      ele.fontSize = ~~((+fontSize || 16) * compScale) + "";
      nodes.push(ele);
    }
  });

  const [w, h] = mapInfo.mapSize.split("*").map((ele) => +ele);

  const hByScale = ~~(h * compScale);
  const wByScale = ~~(w * compScale);
  return {
    links,
    nodes,
    map: {
      ...mapInfo,
      mapSize: `${wByScale}*${hByScale}`,
    },
  };
};

export const downloadFile = (content, filename) => {
  // 创建隐藏的可下载链接
  var eleLink = document.createElement("a");
  eleLink.download = filename;
  eleLink.style.display = "none";
  // 字符内容转变成blob地址
  var blob = new Blob([content]);
  eleLink.href = URL.createObjectURL(blob);
  // 触发点击
  document.body.appendChild(eleLink);
  eleLink.click();
  // 然后移除
  document.body.removeChild(eleLink);
};
