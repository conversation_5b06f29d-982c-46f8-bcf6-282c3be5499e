import { SVG } from "@svgdotjs/svg.js";

export const getPathBySvg = (svgText) => {
  const o = document.createElement("div");
  o.innerHTML = svgText;
  const svgEL = o.querySelector("svg");
  const svg = SVG(svgEL);
  //   const g = svg.findOne("#隧道");
  //   const pathList = g.find("line");

  //   return pathList.map((ele) => {
  //     return {
  //       path: `M ${ele.attr("x1")} ${ele.attr("y1")} L ${ele.attr(
  //         "x2"
  //       )} ${ele.attr("y2")}`,
  //       styles: ele.css(),
  //       type: "隧道",
  //     };
  //   });

  //   const g = svg.findOne("#立交X高架");
  //   const pathList = g.find("line");
  //   return pathList
  //     .map((ele) => {
  //       const style = ele.css();
  //       if (style["stroke-width"] === "20px") {
  //         return {
  //           path: `M ${ele.attr("x1")} ${ele.attr("y1")} L ${ele.attr(
  //             "x2"
  //           )} ${ele.attr("y2")}`,
  //           styles: ele.css(),
  //           type: "高架",
  //         };
  //       }
  //     })
  //     .filter((ele) => !!ele);

  /*************************开始 */

  const getPath = (data, options = {}) => {
    const { name, index } = options;
    const style = data.css();
    let path;
    if (["rgb(35, 24, 21)", "rgb(14, 14, 14)"].includes(style.stroke)) return;
    if (data.type === "line") {
      path = {
        path: `M ${data.attr("x1")} ${data.attr("y1")} L ${data.attr(
          "x2"
        )} ${data.attr("y2")}`,
        styles: style,
        metaData: {
          name,
          index,
        },
      };
    } else if (data.type === "path") {
      if (!data.attr("d")) return;
      path = {
        path: data.attr("d"),
        styles: style,
        metaData: {
          name,
          index,
        },
      };
    } else if (data.type === "circle") {
      const cx = data.attr("cx");
      const cy = data.attr("cy");
      const r = data.attr("r");
      const style = data.css();
      path = {
        path: data.attr("d"),
        styles: style,
      };
      if (!["rgb(17, 21, 1)"].includes(style.fill)) {
        path = {
          x: cx - r,
          y: cy - r,
          w: r * 2,
          h: r * 2,
          type: "circle",
          styles: style,
          metaData: {
            name,
          },
        };
      }
    }

    return path;
  };

  const res = [];

  const sd = svg.findOne("#隧道");

  sd &&
    sd.children().each(function (item, index) {
      const path = getPath(item, { index });
      path && res.push(path);
    });

  const gaojia = svg.findOne("#立交X高架");

  gaojia &&
    gaojia.children().each(function (item, index) {
      index += 400;
      if (item.type === "g") {
        const name = item.attr("id");
        item.children().each(function () {
          const path = getPath(this, { name, index });
          path && res.push(path);
        });
      } else {
        if (item.type === "text") {
          const { translateX, translateY } = item.transform();
          const style = item.css();

          res.push({
            x: translateX,
            y: translateY,
            w: 170,
            h: 50,
            type: "text",
            styles: style,
            text: item.text(),
          });
        } else {
          const path = getPath(item, { index });
          path && res.push(path);
        }
      }
    });

  const nameEl = svg.findOne("#道路名X隧道名");

  nameEl &&
    nameEl.children().each(function (item, index) {
      index += 600;
      if (item.type === "g") {
        const name = item.attr("id");
        item.children().each(function () {
          const path = getPath(this, { name, index });
          path && res.push(path);
        });
      } else {
        if (item.type === "text") {
          const { translateX, translateY } = item.transform();
          const style = item.css();

          res.push({
            x: translateX,
            y: translateY,
            w: 170,
            h: 50,
            type: "text",
            styles: style,
            text: item.text(),
          });
        } else {
          const path = getPath(item, { index });
          path && res.push(path);
        }
      }
    });

  /*************************** */
  //   const lineList = g
  //     .find("line")
  //     .map((ele) => {
  //       const style = ele.css();
  //       if (!["rgb(35, 24, 21)", "rgb(14, 14, 14)"].includes(style.stroke)) {
  //         return {
  //           path: `M ${ele.attr("x1")} ${ele.attr("y1")} L ${ele.attr(
  //             "x2"
  //           )} ${ele.attr("y2")}`,
  //           styles: style,
  //           type: "隧道",
  //         };
  //       }
  //     })
  //     .filter((ele) => !!ele);

  //   const circleList = g
  //     .find("circle")
  //     .map((ele) => {
  //       const cx = ele.attr("cx");
  //       const cy = ele.attr("cy");
  //       const r = ele.attr("r");
  //       const style = ele.css();

  //       if (!["rgb(17, 21, 1)"].includes(style.fill)) {
  //         return {
  //           x: cx - r,
  //           y: cy - r,
  //           w: r * 2,
  //           h: r * 2,
  //           type: "circle",
  //           styles: style,
  //         };
  //       }
  //     })
  //     .filter((ele) => !!ele);

  /******************** video*/
  const video = svg.findOne("#Video");
  let lineList = [];
  let imageList = [];

  if (video) {
    lineList = video
      .find("line")
      .map((ele, index) => {
        const style = ele.css();
        if (!["rgb(35, 24, 21)", "rgb(14, 14, 14)"].includes(style.stroke)) {
          return {
            path: `M ${ele.attr("x1")} ${ele.attr("y1")} L ${ele.attr(
              "x2"
            )} ${ele.attr("y2")}`,
            styles: style,
            metaData: {
              index: 0,
            },
          };
        }
      })
      .filter((ele) => !!ele);

    imageList = svg.find("use").map((ele) => {
      const { translateX, translateY } = ele.transform();

      return {
        x: translateX,
        y: translateY,
        w: 170,
        h: 96,
        type: "video",
      };
    });
  }

  return [...res, ...lineList, ...imageList];

  //   return [...pathList, ...lineList, ...circleList];
};
