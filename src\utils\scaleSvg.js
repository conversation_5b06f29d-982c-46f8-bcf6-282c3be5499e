import {
  distanceOfPointAndLine,
  getTransformPosition,
  checkPointLeftOrRight,
  getNodeLinkList,
} from "../components/Topo/utils.js";

// import listError from "../utils/errorTextlist.json";

// const textRightText = [];

const getTransform = (isRight, line, distance) => {
  let { angle, tx, ty } = getTransformPosition(line, distance);
  if (
    (angle === -90 && isRight) ||
    (angle === 90 && !isRight) ||
    (angle === 0 && isRight) ||
    (angle === 180 && isRight) ||
    (angle < 0 && angle > -180 && isRight) ||
    (angle > 0 && angle < 90 && isRight) ||
    (angle > 90 && angle < 180 && !isRight)
  ) {
    tx = -tx;
    ty = -ty;
  }

  return {
    tx,
    ty,
  };
};

// 移动文字
const moveText = (textList, link) => {
  textList.forEach((text) => {
    if (text.bindLink === link.linkId) {
      // 计算绑定线段的索引
      let bindLinkIndexs = +text.bindSubLink - 1;
      let p1;
      let p2;
      p1 = link.pathPoints[bindLinkIndexs];
      p2 = link.pathPoints[bindLinkIndexs + 1];
      if (!p1 || !p2) {
        link.fill = "#ffb300";
      }

      if (!p1 || !p2) {
        p1 = link.pathPoints[0];
        p2 = link.pathPoints[1];
      }

      // 设置文字角度
      let angelBase = (Math.atan2(p2.y - p1.y, p2.x - p1.x) * 180) / Math.PI;
      angelBase = +angelBase.toFixed();

      const isPositiveNum = +text.rotate >= 0;
      let rotate = 0;
      if (angelBase === 90 || angelBase === -90) {
        rotate = 90;
      } else if (angelBase > 90) {
        rotate = angelBase - 180;
      } else if (angelBase < -90) {
        rotate = 180 + angelBase;
      } else {
        rotate = angelBase;
      }

      text.rotate = isPositiveNum ? Math.abs(rotate) : -Math.abs(rotate);

      // 设置文字跟连线的距离
      const recordLine = [
        { x: p1.x, y: p1.recordY },
        { x: p2.x, y: p2.recordY },
      ];
      // 获取文字左上角到线段的距离
      let distance = distanceOfPointAndLine(text.middleRotatePoint, recordLine);

      const isRight = checkPointLeftOrRight(text.middleRotatePoint, recordLine);

      // 设置文字移动距离
      const recordCenterPointY = (p1.recordY + p2.recordY) / 2;
      const centerPointY = (p1.y + p2.y) / 2;

      const diffY = centerPointY - recordCenterPointY;
      text.y += diffY;
      text.middleRotatePoint.y = text.y + text.h / 2;

      // 移动后 重新计算文字与连线的距离
      let currentDistance = distanceOfPointAndLine(text.middleRotatePoint, [
        p1,
        p2,
      ]);

      const isCurrentRight = checkPointLeftOrRight(text.middleRotatePoint, [
        p1,
        p2,
      ]);

      currentDistance =
        isRight === isCurrentRight ? currentDistance : -currentDistance;

      distance = distance - currentDistance;

      //   if (listError.includes(text.nodeId)) {
      //     textRightText.push(text);
      //   }
      if (distance === 0) return;

      const { tx, ty } = getTransform(isRight, [p1, p2], distance);

      text.x += tx;
      text.y += ty;
      text.middleRotatePoint.y = text.y + text.h / 2;
      text.middleRotatePoint.x = text.x + text.w / 2;
    }
  });
};

function getLengthByPoint(p1, p2) {
  return Math.abs(
    Math.sqrt(Math.pow(p2.y - p1.y, 2) + Math.pow(p2.x - p1.x, 2))
  );
}

// 移动连线
const moveLink = (nodeExcludeTextList, linkList, textList, scale) => {
  linkList.map((link) => {
    const { fromObj, endObj } = link;
    let nodeFrom = { moveY: 0 };
    let nodeEnd = { moveY: 0 };

    if (fromObj || endObj) {
      for (let i = 0; i < nodeExcludeTextList.length; i++) {
        const { nodeId } = nodeExcludeTextList[i];
        if (fromObj === nodeId) {
          nodeFrom = nodeExcludeTextList[i];
        }
        if (endObj === nodeId) {
          nodeEnd = nodeExcludeTextList[i];
        }
      }

      if (nodeFrom.nodeId && !nodeEnd.nodeId) {
        nodeEnd = nodeFrom;
      }
      if (!nodeFrom.nodeId && nodeEnd.nodeId) {
        nodeFrom = nodeEnd;
      }
      const len = link.pathPoints.length;
      const averageY = (nodeFrom.moveY + nodeEnd.moveY) / 2;

      //  处理连接点最后两个点是横向的
      if (
        len > 2 &&
        Math.round(link.pathPoints[len - 1].y) ===
          Math.round(link.pathPoints[len - 2].y)
      ) {
        if (
          link.pathPoints[len - 1].isChange ||
          link.pathPoints[len - 2].isChange
        )
          return;

        const { y } = link.pathPoints[len - 1];

        link.pathPoints[len - 1].y = Math.round(
          link.pathPoints[len - 1].y + nodeEnd.moveY
        );
        link.pathPoints[len - 2].y = link.pathPoints[len - 1].y;
        link.pathPoints[len - 1].isChange = true;
        link.pathPoints[len - 2].isChange = true;

        // 记录之前的位置
        link.pathPoints[len - 1].recordY = y;
        link.pathPoints[len - 2].recordY = y;
      }

      link.pathPoints.forEach((point, index) => {
        if (point.isChange) return;
        // 记录之前的位置
        const { y } = point;
        point.recordY = y;

        if (point.y < 70 && link.linkStyles && link.linkStyles.includes("500"))
          return;

        if (index === 0) {
          if (
            len > 2 &&
            Math.round(point.y) === Math.round(link.pathPoints[1].y)
          ) {
            link.pathPoints[1].recordY = y;
            link.pathPoints[1].y = point.y + nodeFrom.moveY;
            link.pathPoints[1].isChange = true;
          }
          point.y += nodeFrom.moveY;
        } else if (index === len - 1) {
          point.y += nodeEnd.moveY;
        } else {
          if (point.isChange) return;

          const fromPoint = {
            x: nodeFrom.x,
            y: nodeFrom.y - nodeFrom.moveY,
          };
          const endPoint = {
            x: nodeEnd.x,
            y: nodeEnd.y - nodeEnd.moveY,
          };
          //   const distanceFrom = getLengthByPoint(point, fromPoint);
          //   const distanceEnd = getLengthByPoint(point, endPoint);

          const distanceFrom = Math.abs(point.y - fromPoint.y);
          const distanceEnd = Math.abs(point.y - endPoint.y);

          // 如果点与开始元素的距离更近，用开始元素的移动距离
          if (distanceEnd / distanceFrom >= 6) {
            point.y += nodeFrom.moveY;
          } else if (distanceFrom / distanceEnd > 6) {
            point.y += nodeEnd.moveY;
          } else {
            point.y += averageY;
          }
        }
        if (point.y <= 0) {
          point.y = 70;
        }
        // if (point.y >= 3150) {
        //   point.y = 3080;
        // }
      });
    } else {
      // TODO 线段没有绑定关系，则直接把线放大倍数
      link.pathPoints.forEach((point) => {
        point.recordY = point.y;
        point.y = parseInt(point.y * scale);
      });
    }

    moveText(textList, link);
  });
};

const moveTextName = (nodeExcludeTextList, textNameList, scale) => {
  nodeExcludeTextList.forEach((node) => {
    textNameList.forEach((text, index) => {
      if (!text.bindLink) {
        text.y = text.y * scale;
      } else if (node.nodeId === text.bindLink) {
        text.y += node.moveY;
        // textNameList.splice(index, 1);
      }
    });
  });
};

const scaleSvgY = (linkList = [], nodeList = [], scale = 2) => {
  //  排除文字的元素
  const nodeExcludeTextList = [];
  const textList = [];
  //  电厂或者电站的名称
  const textNameList = [];

  nodeList.forEach((node) => {
    if (
      (node.nodeId && node.nodeType !== "text") ||
      (node.nodeType === "text" && !node.bindLink)
    ) {
      const { y, middleRotatePoint } = node;
      node.y = parseInt(y * scale);

      node.moveY = node.y - y;

      //   元素移动的距离

      node.middleRotatePoint.y = node.y + node.h / 2;

      nodeExcludeTextList.push(node);
    } else {
      // 电厂或者电站的名称
      if (node.bindLink && +node.bindSubLink === 0) {
        textNameList.push(node);
      } else {
        textList.push(node);
      }
    }
  });

  moveLink(nodeExcludeTextList, linkList, textList, scale);
  moveTextName(nodeExcludeTextList, textNameList);

  const res = getNodeLinkList([...linkList, ...nodeList]);
  return [...res.linkList, ...res.nodeList];
};

export { scaleSvgY };
