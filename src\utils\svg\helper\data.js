// 移动后对应的点也要进行变换
const updatePath = (point, tx, ty) => {
  let res = {};
  if (point.a) {
    res.a = point.a;
  } else if (point.c) {
    res.c = point.c.map((ele) => {
      return {
        x: ele.x + tx,
        y: ele.y + ty,
      };
    });
  } else if (point.q) {
    const { x, y } = point.q;
    res.q = {
      x: x + tx,
      y: y + ty,
    };
  }
  return res;
};
export const updateDataPosition = (nodeLinkList) => {
  let linkBind = [];
  let groupMoveList = {};
  const list = [];
  nodeLinkList.forEach((ele, index) => {
    if (ele.selected) {
      if (ele.type === "CustomPath") {
        const { tx, ty, pathPoints, groupId } = ele;
        const points = pathPoints.map((ele) => {
          const { x, y } = ele;
          return {
            ...updatePath(ele, tx, ty),
            x: +x + tx,
            y: +y + ty,
          };
        });
        if (groupId && !groupMoveList[groupId]) {
          groupMoveList[groupId] = { groupId, tx, ty };
        }
        this.$set(nodeLinkList[index], "pathPoints", points);
        nodeLinkList[index].tx = 0;
        nodeLinkList[index].ty = 0;
      } else {
        const { w, h, x, y, tx, ty, groupId } = ele;
        const xMove = +x + tx;
        const yMove = +y + ty;
        nodeLinkList[index].x = xMove;
        nodeLinkList[index].y = yMove;
        nodeLinkList[index].tx = 0;
        nodeLinkList[index].ty = 0;
        nodeLinkList[index].middleRotatePoint = {
          x: xMove + w / 2,
          y: yMove + h / 2,
        };
        nodeLinkList[index].nodePosition = `${xMove},${yMove}`;
        // 获取组合框的位移
        if (groupId && !groupMoveList[groupId]) {
          groupMoveList[groupId] = { groupId, tx, ty };
        }
        // 拖拽结束后，更新关联的线段
        linkBind.push(...this.getBindLink(ele));
      }
      list.push(ele);
    }
  });
  return nodeLinkList;
};
