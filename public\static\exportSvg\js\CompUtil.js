/*
 * @Author: LuXiaof<PERSON>
 * @Date: 2020-06-10 13:38:42
 * @LastEditors: LuXiaofu
 * @LastEditTime: 2020-07-14 13:46:41
 * @Description: file content
 */
/**
 * <AUTHOR>
 * @description
 * @date 2020/5/18
 */
(function () {
  /**
   * 设置渐变色
   * @param svg {Object} 组件容器的svg标签
   * @param compId {String} 组件id(无效参数)
   * @param gradient {Object} 渐变色对象 {type:0线性，1环形,direction:0上到下，1左到右,stops:[{offset:0到1,color:颜色}]}
   * @returns {string} 渐变色url路径
   */
  let setGradient = function (svg, compId, gradient) {
    let defs = null;
    // let id = "linear_" + WisUtil.guid().substring(0, 16);
    let id = 'linear_' + getRadomA();
    if (svg.select('defs').empty()) {
      defs = svg.append('defs');
    } else {
      defs = svg.select('defs');
    }
    let gradientDom = defs
      .append('linearGradient')
      .attr('id', `${id}_gradient`)
      .attr('gradientUnits', 'objectBoundingBox')
      .attr('x1', '0%')
      .attr('y1', '0%')
      .attr('x2', gradient.direction === 0 ? '0%' : '100%')
      .attr('y2', gradient.direction === 0 ? '100%' : '0%');

    gradient.stops.forEach((stop) => {
      gradientDom.append('stop').attr('offset', stop.offset).style('stop-color', stop.color);
    });

    return `url(#${id}_gradient)`;
  };

  let getRadomA = function () {
    var returnStr = '',
      range = 13,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    for (var i = 0; i < range; i++) {
      var index = Math.round(Math.random() * (arr.length - 1));
      returnStr += arr[index];
    }
    return returnStr;
  };

  /**
   * 通过属性名称获取属性字典列表中的属性对象
   * @param optionName {string} 属性名称
   * @param optionDic {json} 属性字典
   * @returns {null}
   */
  let findPropertyDictionary = (optionName, optionDic) => {
    let nameList = optionName.split('.');
    let res = null;
    for (let i = 0; i < nameList.length; i++) {
      if (i === 0) {
        res = findPropertyDictionaryByName(optionDic, nameList[i]);
      } else {
        res = findPropertyDictionaryByName(res.children, nameList[i]);
      }
    }
    return res;
  };

  let findPropertyDictionaryByName = (list, name) => {
    for (let i = 0; i < list.length; i++) {
      if (list[i].name === name) {
        return list[i];
      }
    }
  };

  /**
   * 获取svg形状的path路径
   * @param type {number} 形状类型 0：无 1： 圆 2：三角 3：矩形 4：大头针
   * @returns {string}
   */
  let getSymbol = (type) => {
    switch (type) {
      case 'circle':
        //圆
        return 'M 1 0 A 1 1 0 1 0 1 0.0001';
      case 'triangle':
        //三角
        return 'M 0 -1 L 1 1 L -1 1 Z';
      case 'rect':
        //矩形
        return 'M -1 -1 L 1 -1 L 1 1 L -1 1 L -1 -1 Z';
      case 'pin':
        //大头针
        return 'M -0.5421 -0.8857 A 0.6 0.6 0 1 1 0.5421 -0.8857 C 0.3878 -0.5605 0 -0.42 0 0 C 0 -0.42 -0.3878 -0.5605 -0.5421 -0.8857 Z';
      default:
        //默认：无
        return '';
    }
  };

  /**
   *
   * @param {json} json 从ws上收到的数据
   * @param {array} keys 需要截取的字段数组
   * @param {array} cutNums 需要截取的位数数组
   */
  let cutDataDemical = (json, keys, cutNums) => {
    let data = JSON.parse(JSON.parse(json.body).data);
    let body = JSON.parse(json.body);
    for (let i = 0; i < keys.length; i++) {
      data.map((d) => (d[keys[i]] = Number(d[keys[i]]).toFixed(cutNums[i])));
    }
    body.data = JSON.stringify(data);
    json.body = JSON.stringify(body);
    return json;
  };

  /**
   * json数组转树形结构
   * @param {Array} arr json数组
   * @example [{
        name1: 'a',
        name2: 'b',
        name3: 'c',
        value: 1,
      },{
        name1: 'a',
        name2: 'b',
        name3: 'd',
        value: 2,
      },{
        name1: 'e',
        name2: 'f',
        name3: 'g',
        value: 3,
      },{
        name1: 'e',
        name2: 'f',
        name3: 'h',
        value: 4,
      }]
   * @returns 树形结构
   * @example {
        name: 'root',
        children: [{
          name: 'a',
          children: [{
            name: 'b',
            children: [{
              name: 'c',
              value: 1
            },{
              name: 'd',
              value: 2
            }]
          }]
        },{
          name: 'e',
          children: [{
            name: 'f',
            children: [{
              name: 'g',
              value: 3
            },{
              name: 'h',
              value: 4
            }]
          }]
        }]
      }
   */
  let jsonToTree = (arr) => {
    const obj = {};
    const res = [];
    const keyList = arr.map((d) => Object.keys(d).length - 1);
    for (let i = 0; i < arr.length; i++) {
      for (let j = 0; j <= keyList[i]; j++) {
        const item = arr[i][`name${j + 1}`];
        if (!obj[item]) {
          obj[item] = {
            name: item,
            children: [],
          };
        }
        if (j > 0) {
          const parent = obj[arr[i][`name${j}`]];
          if (parent) {
            if (parent.children === undefined) debugger;
            if (parent.children.indexOf(obj[item]) < 0) {
              if (item === undefined) {
                parent.value = arr[i].value;
              } else {
                parent.children.push(obj[item]);
              }
            }
          }
        } else {
          if (res.indexOf(obj[item]) < 0) {
            res.push(obj[item]);
          }
        }
      }
    }
    return {
      name: 'root',
      children: res,
    };
  };

  let WisCompUtil = {};
  WisCompUtil.setGradient = setGradient;
  WisCompUtil.findPropertyDictionary = findPropertyDictionary;
  WisCompUtil.getSymbol = getSymbol;
  WisCompUtil.cutDataDemical = cutDataDemical;
  WisCompUtil.jsonToTree = jsonToTree;

  window.WisCompUtil = WisCompUtil;
})();
