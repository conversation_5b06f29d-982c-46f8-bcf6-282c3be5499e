/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/2.
 */
(function () {
    var scriptPath = function (name) {
        //将className的首字母大写转为小写
        name = name.replace(name[0], name[0].toLowerCase());
        let scripts = document.getElementsByTagName('SCRIPT');
        let path = '';
        let pattern = new RegExp(name + "(\.js|\.min.js)$");
        let pattern2 = new RegExp("(.*)/v(.*)" + name + "(\.js|\.min.js)$");
        if (scripts && scripts.length > 0) {
            for (let i in scripts) {
                let scriptSrc = scripts[i].src;
                if (scriptSrc) {
                    if (scriptSrc.indexOf("?") !== -1) {
                        scriptSrc = scriptSrc.split("?")[0];
                    }
                    if (scriptSrc.match(pattern)) {
                        path = scriptSrc.replace(pattern2, '$1');
                    }
                }
            }
        }
        return path;
    };

    var wrapText = function (text, width, mode = 0) {
        text.each(function () {
            let targetDy = text.attr("dy");
            if (targetDy == null) {
                targetDy = 1;
            } else {
                targetDy = parseFloat(targetDy);
            }
            var words = splitText(text.text(), mode).reverse(),
                word,
                line = [],
                lineNumber = 0,
                lineHeight = 1.2, // ems
                y = text.attr("y"),
                x = text.attr("x"),
                dy = targetDy,
                tspan = text.text(null).append("tspan")
                    .attr("x", x).attr("y", y).attr("dy", dy + "em");

            while (word = words.pop()) {
                line.push(word);
                tspan.text(joinLine(line, mode));
                if (tspan.node().getComputedTextLength() > width) {
                    line.pop();
                    tspan.text(joinLine(line, mode));
                    line = [word];
                    tspan = text.append("tspan").attr("x", x).attr("y", y).attr("dy", ++lineNumber * lineHeight + dy + "em").text(word);
                }
            }
        });
    };

    var joinLine = function (line, mode) {
        let result = "";
        let length = line.length;
        line.forEach(function (word, index) {
            result += word;
            if (mode == 1 && index < length) {
                result += (isChinese(word) ? "" : " ");
            }
        });
        return result;
    };
    var splitText = function (str, mode) {
        let result = [];
        let splitMode = "";

        if (mode == 1) {
            splitMode = /\s+/;
        }
        let split = str.split(splitMode);
        split.forEach(function (word) {
            let res = isChinese(word);
            if (res) {
                for (let index = 0; index < word.length; index++) {
                    let char = word.charAt(index);
                    result.push(char);
                }
            } else {
                result.push(word);
            }
        });

        return result;
    };
    var isChinese = function (str) {
        let regularExp = /[\u4E00-\u9FA5]/g;
        return regularExp.test(str);
    };

    var deepCopy = function (object) {
        return JSON.parse(JSON.stringify(object));
    };

    var urlParser = function (us) {
        let urlString = us || window.location.href;
        let url = new URL(urlString);
        let panelId = url.searchParams.get("p") || "31";
        let touch = url.searchParams.get("t") || "false";
        let mode = url.searchParams.get("m") || "prod";
        let scale = url.searchParams.get("s") || 1;
        return { pId: panelId, mode: mode, touch: JSON.parse(touch), scale: scale };
    };

    var getUrlParameters = (name) => {
        let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        let result = window.location.search.substr(1).match(reg);
        if (result) {
            return decodeURIComponent(result[2]);
        }
        return null;
    };

    //用于生成uuid
    var S4 = function () {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    };
    var guid = function () {
        return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
    };

    /**
     * Date转化为指定格式的String
     * 月(M)，日(d)，12小时(h)，24小时(H)，分(m)，秒(s)，星期(E)，季度(q)，年(y)，毫秒(S)
     * 其中E可以用1~3个占位符，表示二/周二/星期二
     * @param date, fmt
     * @returns {*}
     */
    var formatDate = function (date, fmt) {
        let o = {
            'M+': date.getMonth() + 1,
            'd+': date.getDate(),
            'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
            'H+': date.getHours(),
            'm+': date.getMinutes(),
            's+': date.getSeconds(),
            'q+': Math.floor((date.getMonth() + 3) / 3),
            'S': date.getMilliseconds()
        };
        let week = {
            '0': '/u65e5',
            '1': '/u4e00',
            '2': '/u4e8c',
            '3': '/u4e09',
            '4': '/u56db',
            '5': '/u4e94',
            '6': '/u516d'
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
        }
        if (/(E+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, ((RegExp.$1.length > 1) ? (RegExp.$1.length > 2 ? '/u661f/u671f' : '/u5468') : '') + week[date.getDay() + '']);
        }
        for (let k in o) {
            if (new RegExp('(' + k + ')').test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)));
            }
        }
        return fmt;
    };

    /**
     * 判断浏览器内核
     * @returns {*}
     */
    var judagePlatform = function () {
        var userAgent = navigator.userAgent;
        if(userAgent.match(/Chrome/i) == 'Chrome') {
            return "Chrome";
        } else if(userAgent.match(/Opera/i) == 'Opera') {       
            return "Opera";
        } else if(userAgent.match(/Firefox/i) == 'Firefox') {        
            return "Firefox";
        } else if(userAgent.match(/Safari/i) == 'Safari') {        
            return "Safari";
        }
    };

    /**
     * 获取主窗口配置项
     */
    let getPcConfig = (configUrl) => {
        return new Promise((resolve, reject) => {
            d3.text(configUrl, res => {
                let parameters = res && res.replace(/[ ]/g, "").split(/[\n]+/g) || [];
                let pcConfig = {};
                parameters.forEach(parameter => {
                    if (parameter && parameter.indexOf("#") != 0) {
                        let options = parameter.split("=");
                        pcConfig[options[0]] = options[1].trim();
                    }
                });
                pcConfig.mainFrame = pcConfig.mainFrame.replace('[', "").replace(']', "").split(",");
                pcConfig.secondaryFrame = pcConfig.secondaryFrame.replace('[', "").replace(']', "").split(",");
                resolve(pcConfig);
            });
        });
    };


    /**
     * 重组参数
     * @param {*} paramO JSON or JSONString
     */
    let setFetchParam = paramO => {
        if (typeof paramO === "string") paramO = JSON.parse(paramO);
        let param = '';
        for (var key in paramO) {
            param += `${key}=${paramO[key]}&`;
        }
        param = param.substring(0, param.length - 1);
        return param;
    }

    var WisUtil = {};
    WisUtil.scriptPath = scriptPath;
    WisUtil.wrapText = wrapText;
    WisUtil.deepCopy = deepCopy;
    WisUtil.urlParser = urlParser;
    WisUtil.getUrlParameters = getUrlParameters;
    WisUtil.guid = guid;
    WisUtil.formatDate = formatDate;
    WisUtil.judagePlatform = judagePlatform;
    WisUtil.getPcConfig = getPcConfig;
    WisUtil.setFetchParam = setFetchParam;

    window.WisUtil = WisUtil;
})();




