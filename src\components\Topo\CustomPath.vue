<template>
  <g
    ref="pathGroup"
    :id="`group-${formatId(data.linkId)}`"
    :transform="`translate(${data.tx}, ${data.ty})`"
    v-show="linkStyles.volt === '220' ? is220kvShow : true"
  >
    <path
      :class="{ 'topo-line-block': linkStyles.isBlock }"
      :d="generatePath"
      pointer-events="stroke"
      :style="getStyle()"
      class="custom-path"
    ></path>
    <path
      v-if="linkStyles.arrowPath"
      :d="linkStyles.arrowPath"
      :fill="linkStyles.color"
      :stroke="linkStyles.color"
      stroke-width="2"
    >
    </path>

    <!-- 👇这个路径是为了方便鼠标选择，不显示，比要显示的路径要宽一点 -->
    <path
      :d="generatePath"
      fill="none"
      stroke="white"
      :pointer-events="linkStyles.isDrag"
      visibility="hidden"
      :stroke-width="data.linkWidth + 3"
      :id="formatId(data.linkId)"
      @mousedown="handlePathMousedown"
      @mouseup="handlePathMouseup"
      class="custom-path-move"
    ></path>
    <g v-if="!linkStyles.isBlock">
      <path
        v-for="(item, index) in arrowList"
        class="topo-link-arrow"
        :d="item"
        fill="none"
        :key="data.linkId + '-' + index"
        stroke="#00a8ff"
        :stroke-width="1 / scale"
      >
      </path>
    </g>
  </g>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import { formatId } from "@/utils/tools/format";

const voltColors = {
  //   _110KV: "#9dd0ed",
  _110KV: "#000000",
  _220KV: "#000000",
  _500KV: "red",
  _1000KV: "#8d06f2",
};
export default {
  name: "CustomPath",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    adsorbPosition: {
      type: Object,
      default: () => {},
    },
    scale: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      style: {},
      points: [],
      closePath: false,
      arrowList: [],
    };
  },
  computed: {
    ...mapState("topoStore", {
      is220kvShow: "is220kvShow",
      isDrawBlock: "isDrawBlock",
    }),
    linkStyles() {
      const colorList = {
        green: "green",
        pink: "#ff269c",
        orange: "#ff4400",
        blue: "#3360fb",
      };
      const { linkStyles, metaData = {} } = this.data;
      let { points } = this;

      const styles = JSON.parse(linkStyles);
      let color = styles.color;
      let isDrag = "stroke";
      let fill = styles.isBlock ? styles.fill : "none";
      if (styles.dynamicColor) {
        color = colorList[styles.dynamicColor];
      } else {
        color = styles.color;
        if (metaData.lineType === "直流") {
          color = "#3360fb";
        } else {
          if (metaData.volt === "_500KV" || metaData.volt === "_1000KV") {
            color = voltColors[metaData.volt];
          } else {
            color = "#000000";
          }
        }
      }

      if (!this.isDrawBlock) {
        isDrag = styles.isBlock ? "none" : "stroke";
      } else {
        isDrag = styles.isBlock ? "all" : "none";
      }
      this.closePath = styles.isBlock;

      let arrowPath = "";
      if (styles.isArrow) {
        // 箭头在起点
        if (styles.arrowDirection === "start") {
          arrowPath = this.drawSigleArrow(points[1], points[0], true);
        } else {
          // 箭头在终点 默认
          arrowPath = this.drawSigleArrow(
            points[points.length - 2],
            points[points.length - 1],
            true
          );
        }
      }

      return {
        ...styles,
        color,
        fill,
        volt: styles.volt || "",
        isDrag,
        arrowPath,
      };
    },
    generatePath() {
      let { points, closePath } = this;
      if (points && !points.length) return;
      let d = "";
      points.forEach((p, i) => {
        if (i === 0) {
          d += "M";
        } else if (p.q) {
          // quadratic
          d += `Q ${p.q.x} ${p.q.y} `;
        } else if (p.c) {
          // cubic
          d += `C ${p.c[0].x} ${p.c[0].y} ${p.c[1].x} ${p.c[1].y} `;
        } else if (p.a) {
          // arc
          d += `A ${p.a.rx} ${p.a.ry} ${p.a.rot} ${p.a.laf} ${p.a.sf} `;
        } else {
          d += "L ";
        }
        d += `${p.x} ${p.y} `;
      });
      if (closePath) d += "Z";
      return d;
    },
  },
  watch: {
    data: {
      handler(val) {
        this.points = val.pathPoints || [];
        this.drawLineArrow();
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    ...mapMutations("topoStore", ["setPointSelectedInfo"]),
    formatId,
    handlePathMousedown(e) {
      this.$emit("cusmousedown", e);
    },
    handlePathMouseup(e) {
      this.$emit("cusmouseup", e);
      this.$bus.emit("handlePointSelected", {});
    },
    drawLineArrow() {
      let { points } = this;
      const len = points.length;
      this.arrowList = points
        .map((ele, index) => {
          if (index !== len - 1) {
            return this.drawSigleArrow(ele, points[index + 1]);
          }
        })
        .filter((ele) => !!ele);
    },
    drawSigleArrow(prev, next, isDirectionArrow) {
      const { x: x1, y: y1 } = prev;
      const { x: x2, y: y2 } = next;

      let path = "";
      let slopy, cosy, siny;
      let Par = isDirectionArrow ? 30 : 10;
      let skewing = isDirectionArrow ? 5.5 : 2;
      let x3, y3;
      slopy = Math.atan2(y1 - y2, x1 - x2);
      cosy = Math.cos(slopy);
      siny = Math.sin(slopy);

      if (isDirectionArrow) {
        x3 = next.x;
        y3 = next.y;
      } else {
        x3 = (Number(x1) + Number(x2)) / 2;
        y3 = (Number(y1) + Number(y2)) / 2;
      }

      path += "M" + x3 + "," + y3;

      path +=
        " L" +
        (Number(x3) + Number(Par * cosy - (Par / skewing) * siny)) +
        "," +
        (Number(y3) + Number(Par * siny + (Par / skewing) * cosy));

      path += isDirectionArrow ? " L" : " M";
      path +=
        Number(x3) +
        Number(Par * cosy + (Par / skewing) * siny) +
        "," +
        (Number(y3) - Number((Par / skewing) * cosy - Par * siny));
      path += " L" + x3 + "," + y3;

      return path;
    },
    getStyle() {
      const { extraMetaData = {} } = this.data;
      const status = +extraMetaData.status;
      const style = {
        stroke: this.linkStyles.color,
        strokeWidth: this.data.linkWidth,
        fill: this.linkStyles.fill,
        filter: +this.linkStyles.dropShadowBlurRadius
          ? `drop-shadow(0 0 ${this.linkStyles.dropShadowBlurRadius}px ${this.linkStyles.dropShadowColor}) drop-shadow(0 0 ${this.linkStyles.dropShadowBlurRadius}px ${this.linkStyles.dropShadowColor})`
          : "none",
      };
      if (this.data.isBindError) {
        // style.filter = "url(#drop-shadow)";
        style.stroke = "#fcd337";
        style.strokeWidth = this.data.linkWidth * 2;
      }

      if (status === 6 || this.data.linkType === "虚线") {
        style.strokeDasharray = this.data.dashedLink || "10,10";
      } else {
        style.strokeDasharray = "none";
      }

      if (this.linkStyles.isBlock) {
        style.stroke = "transparent";
        style.strokeWidth = 0;
      }
      return style;
    },
  },
};
</script>

<style lang="scss" scoped></style>
