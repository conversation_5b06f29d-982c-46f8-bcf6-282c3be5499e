import setLang from "../lang";

const lang = {
  i: {
    locale: "tr-TR",
    select: {
      placeholder: "Se<PERSON>",
      noMatch: "<PERSON><PERSON><PERSON>şen veri yok",
      loading: "yükleme",
    },
    table: {
      noDataText: "<PERSON>eri Yok",
      noFilteredDataText: "<PERSON><PERSON><PERSON><PERSON>len veri yok",
      confirmFilter: "<PERSON><PERSON><PERSON>",
      resetFilter: "<PERSON>ı<PERSON><PERSON>rl<PERSON>",
      clearFilter: "Heps<PERSON>",
      sumText: "Sum",
    },
    datepicker: {
      selectDate: "Tarih seç",
      selectTime: "<PERSON>aman seç",
      startTime: "<PERSON>şlangıç",
      endTime: "<PERSON>işe",
      clear: "Temizle",
      ok: "Tamam",
      datePanelLabel: "[mmmm] [yyyy]",
      month: "",
      month1: "Ocak",
      month2: "Şubat",
      month3: "Mart",
      month4: "Nisan",
      month5: "Mayıs",
      month6: "<PERSON><PERSON><PERSON>",
      month7: "<PERSON>mmuz",
      month8: "<PERSON><PERSON><PERSON><PERSON>",
      month9: "<PERSON><PERSON><PERSON><PERSON>",
      month10: "Ekim",
      month11: "Kasım",
      month12: "Aralı<PERSON>",
      year: "",
      weekStartDay: "0",
      weeks: {
        sun: "Paz",
        mon: "Pzt",
        tue: "Sal",
        wed: "Çar",
        thu: "Per",
        fri: "Cum",
        sat: "Cmt",
      },
      months: {
        m1: "Oca",
        m2: "Şub",
        m3: "Mar",
        m4: "Nis",
        m5: "May",
        m6: "Haz",
        m7: "Tem",
        m8: "Ağu",
        m9: "Eyl",
        m10: "Ekm",
        m11: "Kas",
        m12: "Ara",
      },
    },
    transfer: {
      titles: {
        source: "Kaynak",
        target: "Hedef",
      },
      filterPlaceholder: "Arama yapın",
      notFoundText: "Bulunamadı",
    },
    modal: {
      okText: "Tamam",
      cancelText: "İptal",
    },
    poptip: {
      okText: "Tamam",
      cancelText: "İptal",
    },
    page: {
      prev: "Önceki",
      next: "Sonraki",
      total: "Toplam",
      item: "öğe",
      items: "öğeler",
      prev5: "Önceki 5 Sayfa",
      next5: "Sonraki 5 Sayfa",
      page: "/sayfa",
      goto: "Git",
      p: "",
    },
    rate: {
      star: "Yıldız",
      stars: "Yıldız",
    },
    time: {
      before: " önce",
      after: " sonra",
      just: "hemen şimdi",
      seconds: " saniye",
      minutes: " dakika",
      hours: " saat",
      days: " gün",
    },
    tree: {
      emptyText: "Veri Yok",
    },
  },
};

setLang(lang);

export default lang;
