import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import { setupUI } from "./utils/setup-ui";
import VueBus from "vue-bus";
import { get, post } from "@/utils/http";
import "@/assets/iconfont/iconfont.css";

Vue.config.productionTip = false;
setupUI(Vue);
Vue.prototype.$get = get;
Vue.prototype.$post = post;

Vue.use(VueBus);
const vue = new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
console.log("打包时间：2025年4月10日14:57:42");

export default vue;
