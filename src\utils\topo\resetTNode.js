/**
 * nodeSize: 25*25
 * nodePosition: 0,0
 * TNode的默认大小为25
 * 先讲大小按中心点缩放到scale倍数
 * 然后再按照中心点使用translate scale倍数
 * @param {*} nodeList
 */
export const resetTNode = (scale = 1, nodeList = []) => {
  console.log("🚀 ~ resetTNode ~ nodeList:", nodeList);
  if (scale === 1 || !nodeList.length) return [];
  const list = [];
  nodeList.forEach((node) => {
    if (node.nodeId && node.nodeType === "t") {
      const [w, h] = node.nodeSize.split("*");
      const style = node.nodeStyles ? JSON.parse(node.nodeStyles) : {};
      console.log("修改前", node.nodeSize, node.nodePosition);

      node.w = w * scale;
      node.h = h * scale;
      node.x = node.x - (node.w - w) / 2;
      node.y = node.y - (node.h - h) / 2;
      node.nodeSize = `${node.w}*${node.h}`;
      node.nodePosition = `${node.x},${node.y}`;
      node.middleRotatePoint = {
        x: node.x + node.w / 2,
        y: node.y + node.h / 2,
      };
      style.scale = w / node.w;
      node.nodeStyles = JSON.stringify(style);
      console.log("修改后", node.nodeSize, node.nodePosition);
      list.push(node);
    }
  });

  return list;
};
