<template>
  <Row type="flex" align="middle" class="topo-node-property-item">
    <Col span="8" style="text-align: right">TNode：</Col>
    <Col span="16">
      <InputNumber
        v-model="scale"
        :min="0"
        size="small"
        class="mr-5"
      ></InputNumber>
      <Button type="primary" size="small" @click="onClick"> 设置 </Button>
    </Col>
  </Row>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";

import { resetTNode } from "@/utils/topo/resetTNode";
import { updateNodes } from "@/utils/http/node";
export default {
  data() {
    return {
      scale: 1,
    };
  },
  mounted() {},
  beforeDestroy() {},
  computed: {
    ...mapState("topoStore", {
      allNodeLinkList: "allNodeLinkList",
    }),
  },
  methods: {
    ...mapMutations("topoStore", []),
    initEventBus() {},
    async onClick() {
      const list = resetTNode(this.scale, this.allNodeLinkList);
      if (list.length === 0) {
        this.$Message.error("没有找到T节点");
        return;
      }
      await updateNodes(list);
      this.$Message.success("设置成功");
    },
  },
};
</script>
<style lang="scss"></style>
