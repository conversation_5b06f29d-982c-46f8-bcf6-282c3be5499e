<template>
  <div class="in-coder-panel" style="background: #fff">
    <textarea
      ref="textarea"
      id="mytext"
      placeholder="点击Ctrl进行代码提示"
    ></textarea>
    <!--    <Select class="code-mode-select" v-if="show" v-model="mode" @change="changeMode">-->
    <!--      <Option v-for="mode in modes" :key="mode.value" :label="mode.label" :value="mode.value"></Option>-->
    <!--    </Select>-->
  </div>
</template>

<script type="text/ecmascript-6">
// 引入全局实例
import _CodeMirror from "codemirror";
// 核心样式
// import "codemirror/lib/codemirror.css";
// 引入主题后还需要在 options 中指定主题才会生效
// import "codemirror/theme/cobalt.css";

// import "codemirror/addon/hint/show-hint.css";
// import 'codemirror/theme/cobalt.css'
// 需要引入具体的语法高亮库才会有对应的语法高亮效果
// codemirror 官方其实支持通过 /addon/mode/loadmode.js 和 /mode/meta.js 来实现动态加载对应语法高亮库
// 但 vue 貌似没有无法在实例初始化后再动态加载对应 JS ，所以此处才把对应的 JS 提前引入






// import  "codemirror/addon/edit/matchbrackets"
// import "codemirror/addon/selection/active-line"


//import "codemirror/mode/javascript/javascript.js";
//import "codemirror/mode/css/css.js";
//import "codemirror/mode/xml/xml.js";
//import "codemirror/mode/clike/clike.js";
//import "codemirror/mode/markdown/markdown.js";
//import "codemirror/mode/python/python.js";
// import "codemirror/mode/r/r.js";
// import "codemirror/mode/shell/shell.js";
// import "codemirror/mode/sql/sql.js";
// import "codemirror/mode/swift/swift.js";
// import "codemirror/mode/vue/vue.js";
// import "codemirror/addon/hint/show-hint";
// import "codemirror/addon/hint/sql-hint";

// 尝试获取全局实例
const CodeMirror = window.CodeMirror || _CodeMirror;

export default {
  name: "in-coder",
  props: {
    // 外部传入的内容，用于实现双向绑定
    value: String,
    // 外部传入的语法类型
    language: {
      type: String,
      default: 'x-sql'
    },
    // 是否显示select
    show: Boolean,
    theme: String // 主题
  },
  data() {
    return {
      // 内部真实的内容
      code: "",
      // 默认的语法类型
      mode: "sql",
      // 编辑器实例
      coder: null,
      // 默认配置
      options: {
        // 缩进格式
        tabSize: 2,
        // 主题，对应主题库 JS 需要提前引入
        // theme: "cobalt",
        theme: this.theme,
        indentWithTabs:true,//搜航缩进
        // 显示行号
        lineNumbers: false,
        matchBrackets:true,
        styleActiveLine: true, // 当前行背景高亮
        hintOptions:{
          // 自定义提示选项
          tables: {

          }
        },
        extraKeys: {'Ctrl': 'autocomplete'},//自定义快捷键
        line: true,
        input: this.inputText,

      },
      // 支持切换的语法高亮类型，对应 JS 已经提前引入
      // 使用的是 MIME-TYPE ，不过作为前缀的 text/ 在后面指定时写死了
      modes: [
        {
          value: "css",
          label: "CSS"
        },
        {
          value: "javascript",
          label: "Javascript"
        },
        {
          value: "html",
          label: "XML/HTML"
        },
        {
          value: "x-java",
          label: "Java"
        },
        {
          value: "x-objectivec",
          label: "Objective-C"
        },
        {
          value: "x-python",
          label: "Python"
        },
        {
          value: "x-rsrc",
          label: "R"
        },
        {
          value: "x-sh",
          label: "Shell"
        },
        {
          value: "x-sql",
          label: "SQL"
        },
        {
          value: "x-swift",
          label: "Swift"
        },
        {
          value: "x-vue",
          label: "Vue"
        },
        {
          value: "markdown",
          label: "Markdown"
        }
      ]
    };
  },
  mounted() {
    // 初始化
    this._initialize();
  },
  methods: {
    hintOptionsTbale(value = ''){
      if(value){
        this.options.hintOptions.tables = value
      }
    },
    trValue(param = ''){
      this.code = param;

      this._initialize()
    },
    // 初始化
    _initialize() {
      // this.value = this.value||param
      // 初始化编辑器实例，传入需要被实例化的文本域对象和默认配置
      this.coder = CodeMirror.fromTextArea(this.$refs.textarea, this.options);
      this.coder.setSize('100%', '100%'); // 设置编辑器宽高为自适应容器宽高
      // 编辑器赋值
      this.coder.setValue(this.value || this.code);

      // 支持双向绑定
      this.coder.on("change", coder => {
        this.code = coder.getValue();

        if (this.$emit) {
          this.$emit("input", this.code);
        }
      });

      // 尝试从父容器获取语法类型
      if (this.language) {
        // 获取具体的语法类型对象
        let modeObj = this._getLanguage(this.language);

        // 判断父容器传入的语法是否被支持
        if (modeObj) {
          this.mode = modeObj.label;
        }
      }
      this.coder.setOption("mode", `text/x-sql`);
    },
    emitValue(e) {
      this.$emit("changeValue", e, this.code);
    },
    // 获取当前语法类型
    _getLanguage(language) {
      // 在支持的语法类型列表中寻找传入的语法类型
      return this.modes.find(mode => {
        // 所有的值都忽略大小写，方便比较
        let currentLanguage = language.toLowerCase();
        let currentLabel = mode.label.toLowerCase();
        let currentValue = mode.value.toLowerCase();

        // 由于真实值可能不规范，例如 java 的真实值是 x-java ，所以讲 value 和 label 同时和传入语法进行比较
        return (
          currentLabel === currentLanguage || currentValue === currentLanguage
        );
      });
    },
    // 更改模式
    changeMode(val) {
      // 修改编辑器的语法配置
      // 获取修改后的语法
      let label = this._getLanguage(val).label.toLowerCase();
      // 允许父容器通过以下函数监听当前的语法值
      this.$emit("language-change", label);
    }
  },
  updated() {
    this._initialize();
    // $('.CodeMirror')[1].hide()
  },
  watch: {
    value() {
      this._initialize();
    }
  }
};
</script>

<style lang="scss">
.in-coder-panel {
  width: 100%;
  height: 100%;
  .CodeMirror {
    height: 100% !important;
    border: 1px solid #eee;
  }
}

.cm-string {
  color: #3ad900;
}
.cm-comment {
  color: #08f;
}
.cm-atom {
  color: #845dc4;
}
.cm-attribute {
  color: #ff80e1;
}
.cm-keyword {
  color: #25cabb;
}
.cm-number {
  color: #ff80e1;
}
.cm-meta {
  color: #ff9d00;
}
.cm-bracket {
  color: #d8d8d8;
}
.cm-special {
  color: #ff9e59;
}
.cm-builtin {
  color: #ff9e59;
}
.cm-link {
  color: #845dc4;
}
.cm-error {
  color: #9d1e15;
}
.cm-operator {
  color: #808695;
}

//.cm-s-cobalt span.cm-comment { color: #08f; }
//.cm-s-cobalt span.cm-atom { color: #845dc4; }
//.cm-s-cobalt span.cm-number, .cm-s-cobalt span.cm-attribute { color: #ff80e1; }
//.cm-s-cobalt span.cm-keyword { color: #ffee80; }
//.cm-s-cobalt span.cm-string { color: #3ad900; }
//.cm-s-cobalt span.cm-meta { color: #ff9d00; }
//.cm-s-cobalt span.cm-variable-2, .cm-s-cobalt span.cm-tag { color: #9effff; }
//.cm-s-cobalt span.cm-variable-3, .cm-s-cobalt span.cm-def, .cm-s-cobalt .cm-type { color: white; }
//.cm-s-cobalt span.cm-bracket { color: #d8d8d8; }
//.cm-s-cobalt span.cm-builtin, .cm-s-cobalt span.cm-special { color: #ff9e59; }
//.cm-s-cobalt span.cm-link { color: #845dc4; }
//.cm-s-cobalt span.cm-error { color: #9d1e15; }
</style>
