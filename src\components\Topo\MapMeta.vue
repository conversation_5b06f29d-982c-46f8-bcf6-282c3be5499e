<template>
  <Modal
    v-model="ismapMetaVisible"
    draggable
    scrollable
    :closable="false"
    :mask="false"
    :footer-hide="true"
    width="310"
    :z-index="6"
    :styles="{ marginLeft: '0', top: '60px' }"
    class-name="base-modal"
  >
    <div slot="header" class="topo-map-meta-header">
      <Icon type="ios-apps" />
      <span style="font-size: 14px; font-weight: 700; margin-left: 5px">
        图元
      </span>
    </div>
    <div style="height: 580px; overflow: auto; padding: 0 10px">
      <div v-for="item in groupList" :key="item.groupId">
        <p style="font-weight: 700">{{ item.groupName }}</p>
        <ul class="topo-base-lib-list">
          <li
            class="topo-base-lib-item"
            v-for="obj in item.objList"
            :key="obj.objType"
            draggable
            @dragstart="handleDragStart($event, obj.objType)"
          >
            <img
              :src="
                obj.groupId === 'base' ? obj.objImg : getImageUrl(obj.objImg)
              "
              alt=""
              srcset=""
              width="30"
              height="30"
              :draggable="false"
              referrerPolicy="no-referrer"
            />
            <div>{{ obj.objName }}</div>
          </li>
        </ul>
      </div>
    </div>
  </Modal>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import { getImageUrl } from "./utils";
import { mapDataBase, mapDataPower } from "@/utils/topo/map-data";
import { Draggable } from "@/utils/directive/drag";

export default {
  name: "mapMeta",
  directives: { Draggable },

  data() {
    return {
      groupList: [],
      draggableWithHandler: { handle: undefined },
    };
  },

  mounted() {
    this.fetchGroupList();
    this.initEvent();
  },
  computed: {
    ...mapState("topoStore", {
      ismapMetaVisible: "ismapMetaVisible",
    }),
  },
  methods: {
    ...mapMutations("topoStore", ["setPointObjectList"]),
    getImageUrl,
    initEvent() {
      this.draggableWithHandler.handle = this.$refs.mapMetaHeader;
    },
    // 获取分组列表
    // TODO 数据解析
    fetchGroupList() {
      this.$get(`/topoEdit/getGroupList`).then(({ data }) => {
        if (data.code == "0000") {
          //   this.getGroupList(data.data);
          this.groupList = data.data;
          this.groupList.unshift(mapDataBase, mapDataPower);
          this.getObjList(data.data);
        }
      });
    },
    getObjList(data) {
      let arr = [];
      for (let i = 0; i < data.length; i++) {
        const { objList, groupName } = data[i];
        if (objList && objList.length) {
          const res = objList.map((ele) => ({
            ...ele,
            groupName,
          }));
          arr = arr.concat(...res);
        }
      }
      this.setPointObjectList(arr);
    },
    handleDragStart(e, type) {
      e.dataTransfer.setData("type", type);
    },
  },
};
</script>

<style lang="scss" scoped>
.topo-map-meta-layout {
  top: 60px;
  left: 0px;
  position: fixed;
  width: 300px;
  height: 70vh;
  z-index: 999;
  padding: 0 5px;
  border: $border;
  background-color: $cardBg;
}
.topo-map-meta-header {
  display: flex;
  align-items: center;
  font-size: 20px;
}
.topo-base-lib-list {
  display: flex;
  flex-wrap: wrap;
}
.topo-base-lib-item {
  list-style: none;
  width: 80px;
  height: 80px;
  margin: 5px;
  padding: 10px 0 5px 0;
  color: $textColor;
  background-color: $listItemBg;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
  cursor: grab;
  img {
    width: 30px;
    height: 30px;
    object-fit: contain;
    margin-bottom: 5px;
  }
  div {
    font-size: 12px;
    word-break: break-all;
    word-wrap: break-word;
    line-height: 1.2;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.topo-base-lib-item:hover {
  opacity: 0.85;
}
</style>
@/utils/topo/map-data
