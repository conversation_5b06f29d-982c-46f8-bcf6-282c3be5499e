<template>
  <Modal
    v-model="isVisible"
    title="版本注释"
    width="800"
    :closable="false"
    :mask-closable="false"
  >
    <div class="mb-10">
      <span>选择位置：</span>
      <RadioGroup
        v-model="descriptionType"
        @on-change="handleDescriptionTypeChange"
      >
        <Radio label="江北"></Radio>
        <Radio label="江南"></Radio>
      </RadioGroup>
    </div>
    <div v-if="isVisible">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editor"
        :defaultConfig="toolbarConfig"
        :mode="mode"
      />
      <Editor
        style="height: 500px; overflow-y: hidden"
        v-model="html"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onChange="onChange"
        @onCreated="onCreated"
      />
    </div>
    <div slot="footer">
      <Button type="text" @click="hide">取消</Button>
      <Button type="primary" @click="submitMapInfo">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState, mapActions } from "vuex";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { fontSizeList } from "../constant";

const descriptionMap = { 江北: "north", 江南: "south" };

export default {
  components: { Editor, Toolbar },
  data() {
    return {
      isVisible: false,
      descriptionType: "江北",
      editor: null,
      html: "",
      toolbarConfig: {
        toolbarKeys: [
          "headerSelect",
          "blockquote",
          "|",
          "bold",
          "underline",
          "italic",
          "color",
          "bgColor",
          "|",
          "fontSize",
          "fontFamily",
          "lineHeight",
          "|",
          "bulletedList",
          "numberedList",
          {
            key: "group-justify",
            title: "对齐",
            iconSvg:
              '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',
            menuKeys: [
              "justifyLeft",
              "justifyRight",
              "justifyCenter",
              "justifyJustify",
            ],
          },
          {
            key: "group-indent",
            title: "缩进",
            iconSvg:
              '<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z"></path></svg>',
            menuKeys: ["indent", "delIndent"],
          },
          "|",
          "insertLink",
          "divider",
          "|",
          "undo",
          "redo",
          "|",
          "fullScreen",
        ],
      },
      editorConfig: {
        placeholder: "请输入内容...",
        MENU_CONF: {
          fontSize: {
            fontSizeList,
          },
        },
      },
      mode: "default",
      description: {
        north: "",
        south: "",
      },
      mapInfo: {},
    };
  },
  mounted() {
    this.initBusEvent();
  },
  beforeDestroy() {
    this.$bus.off("onMapSlected");
  },
  computed: {
    ...mapState("topoStore", {}),
  },
  methods: {
    ...mapActions("topoStore", ["getMapInfo", "getMapInfo"]),
    initBusEvent() {
      this.$bus.on("onMapSlected", (val) => {
        this.mapInfo = val;
      });
    },
    initDescription() {
      this.getMapInfo(this.mapInfo.mapId).then((val) => {
        this.description = {
          north: "",
          south: "",
        };
        this.mapInfo = val;
        this.description = val.description || {
          north: "",
          south: "",
        };
        this.html = this.description[descriptionMap[this.descriptionType]];
      });
    },
    show() {
      this.initDescription();
      this.isVisible = true;
    },
    hide() {
      this.isVisible = false;
      this.descriptionType = "江北";
      this.html = "";
      this.editor = null;
    },
    onChange(editor) {
      this.description[descriptionMap[this.descriptionType]] = editor.getHtml();
    },
    onCreated(editor) {
      this.editor = Object.seal(editor);
    },
    handleDescriptionTypeChange(val) {
      this.html = this.description[descriptionMap[val]];
      this.descriptionType = val;
    },
    submitMapInfo() {
      this.$post(`/topoEdit/updateMap`, {
        ...this.mapInfo,
        description: this.description,
      }).then(() => {
        this.$Message.success("修改成功");
        this.getMapInfo(this.mapInfo.mapId);
        this.hide();
      });
    },
  },
};
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
