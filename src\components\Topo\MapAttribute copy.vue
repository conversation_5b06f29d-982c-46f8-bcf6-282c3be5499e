<template>
  <div>
    <Tabs value="name1">
      <TabPane label="图层" name="name1">
        <div v-show="mapInfo.mapId" class="topo-node-property">
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">图层名称：</Col>
            <Col span="16" style="text-align: left"
              ><span class="topo-text-select">{{ mapInfo.mapName }}</span>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">图层ID：</Col>
            <Col span="16" style="text-align: left"
              ><span class="topo-text-select">{{ mapInfo.mapId }}</span>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">检查：</Col>
            <Col span="16">
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="checkTopoRelationship"
              >
                关系
              </Button>
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="checkModel('model')"
              >
                模型
              </Button>
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="checkModel"
              >
                节点
              </Button>
              <!-- <Button type="primary" size="small" @click="resetTopoRelationship">
                重置
              </Button> -->
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">G文件：</Col>
            <Col span="16">
              <Button
                type="primary"
                size="small"
                @click="isWriteVisible = true"
                class="mr-5"
              >
                写入
              </Button>
              <Button type="primary" size="small" @click="isVisible = true">
                发布
              </Button>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">底图开关：</Col>
            <Col span="16"
              ><i-switch @on-change="handleTopoSvgBgChange" size="small"
            /></Col>
          </Row>
          <!-- https://pic.imgdb.cn/item/5d36e1a3451253d1789d8dc3.jpg -->
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">底图：</Col>
            <Col span="16"
              ><Input
                @on-change="handleTopoSvgBg"
                size="small"
                :value="svgBgData.path"
              ></Input>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right"></Col>
            <Col span="16" class="topo-attribute-uoload">
              <a>
                选择文件
                <input type="file" accept="image/*,.svg" ref="topoBgUpload" />
              </a>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">透明度：</Col>
            <Col span="16">
              <InputNumber
                :max="1"
                :min="0.1"
                :step="0.1"
                v-model="svgBgData.opacity"
                size="small"
                @on-change="handleTopoSvgBgOpacity"
              ></InputNumber>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">viewBox：</Col>
            <Col span="6">
              <InputNumber
                v-model="viewBoxScale"
                :max="1"
                :min="0.1"
                :step="0.1"
                style="width: 60px"
                size="small"
              ></InputNumber>
            </Col>
            <Col span="5">
              <Button
                class="ml-5"
                type="primary"
                size="small"
                @click="handleViewBoxChange()"
              >
                预览
              </Button>
            </Col>
            <Col span="5">
              <Button
                type="primary"
                size="small"
                @click="handleViewBoxChange('reset')"
              >
                重置
              </Button>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">线框图：</Col>
            <Col span="16"
              ><i-switch
                id="switchShowImageLine"
                :value="isShowImageLine"
                @on-change="handleImageLineChange"
                size="small"
            /></Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">网格线：</Col>
            <Col span="16"
              ><i-switch
                id="switchGridBg"
                :value="isGridBgShow"
                @on-change="handleGridBgChange"
                size="small"
            /></Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">显示220kv：</Col>
            <Col span="16"
              ><i-switch
                id="switch220kv"
                :value="is220kvShow"
                @on-change="handle220kvChange"
                size="small"
            /></Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">计划分排：</Col>
            <Col span="16"
              ><i-switch
                id="switchPlanDivider"
                :value="isPlanDividerShow"
                @on-change="handlePlanDividerChange"
                size="small"
            /></Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">文字框：</Col>
            <Col span="16">
              <Button
                type="primary"
                size="small"
                @click="handleFormatTextWidth"
              >
                刷新
              </Button>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">文字：</Col>
            <Col span="16">
              <CheckboxGroup
                :value="textColorList"
                @on-change="handleTextColorChange"
              >
                <Checkbox label="rgb(255,0,0)">红</Checkbox>
                <Checkbox label="rgb(0,255,0)">绿</Checkbox>
                <Checkbox label="rgb(255,255,255)">白</Checkbox>
                <Checkbox label="rgb(51,96,251)">蓝</Checkbox>
              </CheckboxGroup>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-nsode-property-item">
            <Col span="8" style="text-align: right">选中颜色：</Col>
            <Col span="16">
              <Col span="12" class="text-left">
                <ColorPicker
                  :value="colorSelected"
                  :colors="colors"
                  size="small"
                  @on-change="handleColorChange"
                />
              </Col>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-nsode-property-item">
            <Col span="8" style="text-align: right">绘制色块：</Col>
            <Col span="16"
              ><i-switch
                id="switchDrawBlock"
                :value="isDrawBlock"
                @on-change="handleDrawBlockChange"
                size="small"
            /></Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">
              <Tooltip content="找寻画布外的节点">位置修复： </Tooltip>
            </Col>
            <Col span="16" class="topo-attribute-uoload">
              <Button type="primary" size="small" @click="handleFindClick">
                设置
              </Button>
            </Col>
          </Row>
          <!-- <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right"> 批量设置： </Col>
            <Col span="16" class="topo-attribute-uoload">
              <Button
                type="primary"
                size="small"
                @click="handleMultiClick"
                class="mr-5"
              >
                预览
              </Button>
              <Button
                type="primary"
                size="small"
                @click="handleMultiClick('save')"
              >
                设置
              </Button>
            </Col>
          </Row> -->
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="8" style="text-align: right">整体缩放：</Col>
            <Col span="16">
              <Row type="flex">
                <Col span="9" class="text-left">
                  <InputNumber
                    style="width: 60px"
                    size="small"
                    :value="compScale"
                    :step="0.01"
                    @on-change="handleScaleChange"
                  ></InputNumber>
                </Col>
                <Col span="10" class="text-left ml-5">
                  <Button type="primary" size="small" @click="handleScaleClick">
                    设置
                  </Button>
                </Col>
              </Row>
            </Col>
          </Row>
          <Row type="flex" class="topo-node-property-item">
            <Col span="8" style="text-align: right">缩放：</Col>
            <Col span="16">
              <Row type="flex">
                <Col span="24" class="text-left mb-10">
                  <Select
                    style="width: 60px"
                    size="small"
                    v-model="directionByScale"
                  >
                    <Option value="vertical">纵</Option>
                    <Option value="horizontal">横</Option>
                  </Select>

                  <InputNumber
                    style="width: 60px; margin-left: 5px"
                    size="small"
                    v-model="svgVerticalScale"
                  ></InputNumber>
                </Col>

                <Col span="24" class="text-left">
                  <Button
                    type="primary"
                    size="small"
                    @click="handleVerticalScaleClick()"
                  >
                    预览
                  </Button>
                  <Button
                    class="ml-5"
                    type="primary"
                    size="small"
                    @click="handleVerticalScaleClick(true)"
                  >
                    设置
                  </Button>
                </Col>
              </Row>
            </Col>
          </Row>
          <Row>
            <Col span="8" style="text-align: right">清空：</Col>
            <Col span="16">
              <Button
                style="margin-left: 5px"
                type="primary"
                size="small"
                @click="handleClearClick(true)"
              >
                清空画布内容
              </Button></Col
            >
          </Row>
          <Row class="mb-5">
            <Col span="8" style="text-align: right">子图层：</Col>
            <Col span="16" style="user-select: none">
              <CheckboxGroup
                v-model="sublayerSelectedList"
                @on-change="handleSublayerSelect"
              >
                <Checkbox label="other">其他</Checkbox>
                <Row v-for="item in sublayerList" :key="item.sublayerId">
                  <Col span="16">
                    <Checkbox :label="item.sublayerId">{{
                      item.sublayerName
                    }}</Checkbox>
                  </Col>
                  <Col span="8">
                    <Icon
                      style="cursor: pointer; margin-right: 5px"
                      type="md-create"
                      @click="handleEditSublayerClick(item)"
                    />
                    <Icon
                      style="cursor: pointer"
                      type="md-remove"
                      @click="handleDelSublayerClick(item.sublayerId)"
                    />
                  </Col>
                </Row>
              </CheckboxGroup>
            </Col>
          </Row></div
      ></TabPane>
      <TabPane label="热区" name="name2">
        <ul class="hotspot-layout">
          <li
            v-for="item in hotspotList"
            :key="item.hotspotId"
            class="hotspot-item"
          >
            <div
              :class="{
                'hotspot-item-active':
                  hotspotSelected.hotspotId === item.hotspotId,
              }"
              @click="handleHotspotItemClick(item)"
              class="hotspot-item-name"
            >
              {{ item.hotspotName }}
            </div>
            <div>
              <Button
                type="primary"
                size="small"
                @click.stop="handleHotspotEdit(item)"
                style="margin-right: 5px"
              >
                编辑
              </Button>
              <Button
                type="primary"
                size="small"
                @click.stop="handleHotspotDelete(item)"
              >
                删除
              </Button>
            </div>
          </li>
        </ul>
        <Divider />
        <div class="hotspot-btn-group">
          <Button type="primary" size="small" @click="handleAdd"> 新增 </Button>

          <Button type="primary" size="small" @click="handleCancelSelect">
            取消
          </Button>
          <Button type="primary" size="small" @click="handleSaveHotspot">
            保存
          </Button>
        </div>
      </TabPane>
      <TabPane label="筛选" name="name3">
        <MultNodeModifyConfig></MultNodeModifyConfig>
      </TabPane>
      <TabPane label="隐藏图层文字" name="name4">
        <div style="padding: 10px 20px">
          <div class="mb-10">
            <Checkbox
              :value="isHideTextBySublayer"
              @on-change="setHideTextBySublayer"
              >是否跟据图层隐藏</Checkbox
            >
          </div>

          <CheckboxGroup
            :value="textHideBySublayerList"
            @on-change="handleHideTextChange"
          >
            <Checkbox label="other">其他</Checkbox>
            <Checkbox
              v-for="item in sublayerList"
              :key="item.sublayerId"
              :label="item.sublayerId"
              >{{ item.sublayerName }}</Checkbox
            >
          </CheckboxGroup>
        </div>
      </TabPane>
    </Tabs>

    <Modal
      v-model="isRemoveSublayerVisible"
      title="移除子节点"
      @on-ok="handleRemoveSublayer"
      @on-cancel="closeSublayerNodeRemoveModal"
    >
      <span>选择移除的选项：</span>
      <Checkbox v-model="isSelectLink">连线</Checkbox>
      <Checkbox v-model="isSelectNode">节点</Checkbox>
    </Modal>
    <Modal v-model="isHotspotVisible" title="热点区域">
      <Form :model="formMap" label-position="right" :label-width="100">
        <FormItem label="名称">
          <Input v-model="formMap.hotspotName"></Input>
        </FormItem>
        <FormItem label="状态">
          <Input v-model="formMap.status"></Input>
        </FormItem>
        <FormItem label="顺序">
          <Input v-model="formMap.listOrder"></Input>
        </FormItem>
        <FormItem label="尺寸">
          <Row>
            <Col span="11">
              <Input v-model="formMap.width" placeholder="宽"></Input
            ></Col>
            <Col span="11" offset="2">
              <Input v-model="formMap.height" placeholder="高"></Input>
            </Col>
          </Row>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="closeEditHotspotModal">取消</Button>
        <Button type="primary" @click="handleSubmmitHotspot">确定</Button>
      </div>
    </Modal>
    <SublayerEditModal
      :mapId="mapInfo.mapId"
      ref="sublayerEditModal"
    ></SublayerEditModal>
    <Modal v-model="isVisible" title="发布">
      版本ID：
      <Input v-model="versionId" style="width: 400px" :maxlength="20"></Input>
      <div slot="footer">
        <Button type="primary" @click="publishGFile">确定</Button>
      </div>
    </Modal>
    <Modal v-model="isWriteVisible" title="发布">
      <Form label-position="right" :label-width="100">
        <FormItem label="文件类型">
          <Select v-model="typeByWriteGFile">
            <Option value="0">新一代</Option>
            <Option value="1">大屏</Option>
            <Option value="2">DTS演练系统</Option>
            <Option value="3">深化应用</Option>
            <Option value="4">新一代其他G文件</Option>
          </Select>
        </FormItem>
        <FormItem label="文件名称">
          <Input v-model="gFileName" placeholder="文件名称"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="writeGFile">确定</Button>
      </div>
    </Modal>
    <CheckModelModal ref="checkModelModal"></CheckModelModal>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import SublayerEditModal from "./Modal/SublayerEditModal.vue";
import CheckModelModal from "./Modal/CheckModelModal.vue";
import MultNodeModifyConfig from "./MultNodeModifyConfig.vue";
import _ from "lodash";
import { generatePathD } from "./utils";

export default {
  props: {
    sublayerList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    SublayerEditModal,
    MultNodeModifyConfig,
    CheckModelModal,
  },
  data() {
    return {
      svgBgData: {
        path: "",
        isOpen: false,
        opacity: 0.6,
      },
      isRemoveSublayerVisible: false,
      isSelectLink: false,
      isSelectNode: false,
      sublayerId: "",
      colors: ["#00ff2a", "#00a8ff", "#EBFF00", "#00FDFF"],
      activeIndex: 1,
      isHotspotVisible: false,
      hotspotSelected: {},
      hotspotList: [],
      formMap: {
        hotspotName: "",
        status: 1,
        listOrder: 1,
        width: 0,
        height: 0,
      },
      svgVerticalScale: 1,
      svgVerticalBackupScale: 1,
      sublayerSelectedList: [],
      mapInfo: {},
      nodeLinkList: [],
      versionId: "",
      isVisible: false,
      directionByScale: "vertical",
      isWriteVisible: false,
      typeByWriteGFile: "0",
      gFileName: "",
      viewBoxScale: 1,
    };
  },
  mounted() {
    this.initUpload();
    this.initEventBus();
  },
  beforeDestroy() {
    this.$bus.off("onMapSlected");
    this.$bus.off("onNodeLinkSublayerListChange");
    this.$bus.off("updateNodeSelectedList");

    this.setSvgBgData({
      path: "",
      isOpen: false,
    });
    this.resetForm();
  },
  watch: {},
  computed: {
    ...mapState("topoStore", {
      isShowImageLine: "isShowImageLine",
      isGridBgShow: "isGridBgShow",
      is220kvShow: "is220kvShow",
      isPlanDividerShow: "isPlanDividerShow",
      textColorList: "textColorList",
      isDrawBlock: "isDrawBlock",
      compScale: "compScale",
      colorSelected: "colorSelected",
      hotspotDragInfo: "hotspotDragInfo",
      versionSelected: "versionSelected",
      textHideBySublayerList: "textHideBySublayerList",
      isHideTextBySublayer: "isHideTextBySublayer",
      mapInfoByScale: "mapInfo",
    }),
  },
  methods: {
    ...mapMutations("topoStore", [
      "setSvgBgData",
      "setShowImageLine",
      "setGridBgShow",
      "set220kvShow",
      "setPlanDividerShow",
      "setTextColorList",
      "setDrawBlock",
      "setCompScale",
      "setSublayerList",
      "setColorSelected",
      "setHotspot",
      "setSublayerSelectedList",
      "setSpinShow",
      "setTextHideBySublayerList",
      "setHideTextBySublayer",
      "setHotspotSize",
      "setViewBoxScale",
    ]),
    ...mapActions("topoStore", ["checkTopoRelationshipByMapId"]),
    initEventBus() {
      this.$bus.on("onMapSlected", (mapInfo) => {
        this.mapInfo = mapInfo;
        this.resetForm();
        this.hotspotSelected = {};
        this.viewBoxScale = 1;
        this.setHotspot(false);
        this.sublayerSelectedList = [];
        this.getHotspotList();
      });
      this.$bus.on("onNodeLinkSublayerListChange", (list) => {
        this.nodeLinkList = list;
      });
    },
    initUpload() {
      const _this = this;
      this.$refs.topoBgUpload.addEventListener("change", function () {
        if (!this.files.length) return;
        let file = this.files[0];
        const url = URL.createObjectURL(file);
        _this.svgBgData.path = url;
        _this.setSvgBgData(_this.svgBgData);
      });
    },
    testBtn() {
      this.$get(`/data/getDataByMapId`, {
        mapId: this.mapInfo.mapId,
      });
    },
    checkTopoRelationship() {
      this.checkTopoRelationshipByMapId(this.mapInfo.mapId).then((list) => {
        this.nodeLinkList.forEach((ele) => {
          if (!ele.linkId) return;
          const idIndex = list.findIndex((eleCild) => eleCild === ele.linkId);
          if (idIndex >= 0) {
            ele.isBindError = true;
            list.splice(idIndex, 1);
          }
        });
      });
    },
    resetTopoRelationship() {
      this.nodeLinkList.forEach((ele) => {
        if (!ele.linkId) return;
        ele.isBindError = false;
      });
    },
    checkModel(type) {
      this.$refs.checkModelModal.show(this.mapInfo.mapId, type);
    },
    getGFile() {
      const links = [];
      const nodes = [];
      _.cloneDeep(this.nodeLinkList).forEach((ele) => {
        if (ele.linkId) {
          ele.pathPoints = ele.pathPoints.map((elePoint) => {
            elePoint.x = elePoint.x * this.compScale;
            elePoint.y = elePoint.y * this.compScale;
            return elePoint;
          });
          const { linkStyles } = ele;
          const styles = linkStyles ? JSON.parse(linkStyles) : {};

          ele.linkPath = generatePathD(ele, styles.isBlock);
          ele.linkWidth = parseFloat(
            (ele.linkWidth * this.compScale).toFixed(1)
          );
          links.push(ele);
        } else if (ele.nodeId) {
          const { w, h, x, y, fontSize } = ele;
          ele.w = w * this.compScale;
          ele.h = h * this.compScale;
          ele.x = x * this.compScale;
          ele.y = y * this.compScale;
          ele.nodePosition = `${ele.x},${ele.y}`;
          ele.nodeSize = `${ele.w}*${ele.h}`;
          ele.fontSize = ~~((+fontSize || 16) * this.compScale) + "";
          nodes.push(ele);
        }
      });

      const [w, h] = this.mapInfoByScale.mapSize.split("*").map((ele) => +ele);

      const hByScale = ~~(h * this.compScale);
      const wByScale = ~~(w * this.compScale);
      return {
        links,
        nodes,
        map: {
          ...this.mapInfoByScale,
          mapSize: `${wByScale}*${hByScale}`,
        },
      };
    },
    writeGFile() {
      const params = this.getGFile();
      params.type = this.typeByWriteGFile;
      params.gFileName = this.gFileName;
      this.$post(`/data/writeGFileByMapIdFromClient`, params)
        .then((data) => {
          if (data.code == "0000") {
            this.$Message.success("写入成功");
          }
        })
        .finally(() => {
          this.isWriteVisible = false;
          params.type = "0";
          params.gFileName = "";
        });
    },
    publishGFile() {
      if (!this.versionId) {
        return this.$Message.warning("请输入版本ID！");
      }
      this.setSpinShow(true);
      const params = this.getGFile();
      this.$post(`/data/publishToNari`, {
        ...params,
        versionId: this.versionId,
      })
        .then((data) => {
          if (data.code == "0000") {
            this.$Message.success("发布成功");
            this.isVisible = false;
          }
        })
        .finally(() => {
          this.setSpinShow(false);
        });
    },
    handleTopoSvgBg(e) {
      const { value } = e.target;
      this.svgBgData.path = value;
      this.setSvgBgData(this.svgBgData);
    },
    handleTopoSvgBgChange(val) {
      this.svgBgData.isOpen = val;
      this.setSvgBgData(this.svgBgData);
    },
    handleTopoSvgBgOpacity(val) {
      this.svgBgData.opacity = val;
      this.setSvgBgData(this.svgBgData);
    },
    handleViewBoxChange(type) {
      if (type === "reset") {
        this.viewBoxScale = 1;
      }
      this.setViewBoxScale(this.viewBoxScale);
    },
    // image替换为线框图展示
    handleImageLineChange(val) {
      this.setShowImageLine(val);
      this.blurElement("#switchShowImageLine");
    },
    // 背景网格线展示
    handleGridBgChange(val) {
      this.setGridBgShow(val);
      this.blurElement("#switchGridBg");
    },
    handle220kvChange(val) {
      this.set220kvShow(val);
      this.blurElement("#switch220kv");
    },
    handlePlanDividerChange(val) {
      this.setPlanDividerShow(val);
      this.blurElement("#switchPlanDivider");
    },
    handleTextColorChange(val) {
      this.setTextColorList(val);
    },
    handleDrawBlockChange(val) {
      this.setDrawBlock(val);
      this.blurElement("#switchDrawBlock");
    },
    blurElement(id) {
      document.querySelector(id).blur();
    },
    handleDelSublayerClick(sublayerId) {
      this.sublayerId = sublayerId;
      this.isRemoveSublayerVisible = true;
    },
    handleEditSublayerClick(val) {
      this.$refs.sublayerEditModal.show(val);
    },
    handleSublayerSelect(val) {
      let list = [];
      const sublayerList = [...this.sublayerList];
      // 按照排序显示
      // listOrder越小的 放在最上面
      sublayerList
        .sort((a, b) => a.listOrder - b.listOrder)
        .forEach((ele) => {
          if (val.includes(ele.sublayerId)) {
            list.push(ele.sublayerId);
          }
        });
      if (val.includes("other")) {
        !list.includes("other") && list.unshift("other");
      }
      //   let diffs = [];
      //   let type = "";
      //   if (val.length > sublayerBackupList.length) {
      //     diffs = val.filter((ele) => !sublayerBackupList.includes(ele));
      //     type = "add";
      //   } else {
      //     diffs = sublayerBackupList.filter((ele) => !val.includes(ele));
      //     type = "subtract";
      //   }
      //   this.$bus.emit("onSublayerChange", {
      //     id: diffs[0],
      //     type,
      //   });
      this.setSublayerSelectedList(list);

      this.$bus.emit("onSublayerChange");
      //   sublayerBackupList = val;
    },
    closeSublayerNodeRemoveModal() {
      this.isSelectLink = false;
      this.isSelectNode = false;
    },
    handleMultiClick(type) {
      //   this.$bus.emit("onMultiChange", type);
    },
    handleFindClick() {
      const { mapSize } = this.mapInfo;
      const [mapWidth, mapHeight] = mapSize.split("*").map((ele) => +ele);
      const outOfRangeList = [];
      let index = 0;
      this.nodeLinkList.forEach((ele) => {
        if (ele.nodeId) {
          const { x, y, w, h } = ele;
          if (x + w <= 0 || y + h <= 0 || x >= mapWidth || y >= mapHeight) {
            index++;
            ele.x = mapWidth - (w + index * (w + 5));
            ele.y = mapHeight - h;
            ele.nodePosition = `${ele.x},${ele.y}`;
            ele.middleRotatePoint = {
              x: ele.x + w / 2,
              y: ele.y + h / 2,
            };
            outOfRangeList.push(ele);
          }
        }
      });
      this.$bus.emit("updateNodeSelectedList", outOfRangeList);
    },
    handleScaleChange(val) {
      this.setCompScale(val);
      // this.$bus.emit("onCompScaleChange", val);
    },
    handleVerticalScaleClick(isSave) {
      //   if (this.svgVerticalScale === this.svgVerticalBackupScale && !isSave)
      //     return;
      this.$bus.emit("onSvgVerticalScaleChange", {
        svgVerticalScale: this.svgVerticalScale,
        isSave,
        directionByScale: this.directionByScale,
      });
      this.svgVerticalBackupScale = this.svgVerticalScale;
    },
    handleColorChange(val) {
      this.setColorSelected(val);
    },
    handleFormatTextWidth() {
      this.$bus.emit("onFormatTextWidth");
    },
    handleScaleClick() {
      this.$bus.emit("onCompScaleClick");
      // this.scale = 1
      this.setCompScale(1);
    },
    // 删除子图层
    handleRemoveSublayer() {
      const nodeIdList = [];

      // 全选或者全不选都删除整个子图层
      if (
        (this.isSelectNode && this.isSelectLink) ||
        (!this.isSelectNode && !this.isSelectLink)
      ) {
        const params = {
          mapId: this.mapInfo.mapId,
          sublayerId: this.sublayerId,
        };
        this.$post(`/topoEdit/deleteTopoSublayer`, params).then(() => {
          this.$Message.success("删除成功");
          this.$emit("onDelete");
          this.closeSublayerNodeRemoveModal();
          this.$bus.emit("deleteSublayerSuccess");
        });
      } else {
        if (this.isSelectNode) {
          nodeIdList[0] = this.nodeLinkList
            .filter((ele) => {
              if (!ele.sublayerList) return false;
              const isBelongToSublayer = ele.sublayerList.some(
                (eleChild) => eleChild.sublayerId === this.sublayerId
              );
              return !!ele.nodeId && isBelongToSublayer;
            })
            .map((ele) => ele.nodeId);
        }
        if (this.isSelectLink) {
          nodeIdList[1] = this.nodeLinkList
            .filter((ele) => {
              if (!ele.sublayerList) return false;
              // 将属于此sublayerId的元素筛选出来
              const isBelongToSublayer = ele.sublayerList.some(
                (eleChild) => eleChild.sublayerId === this.sublayerId
              );
              return !!ele.linkId && isBelongToSublayer;
            })
            .map((ele) => ele.linkId);
        }

        const promiseList = nodeIdList.map((ele, index) => {
          if (!ele.length) return;
          const params = {
            mapId: this.mapInfo.mapId,
            sublayerId: this.sublayerId,
            // !this.isSelectNode && !this.isSelectLink
            //   ? this.sublayerId
            //   : undefined,
          };
          params.objType = index + 1;
          params.objIdList = ele;
          return this.$post(`/topoEdit/deleteTopoSublayer`, params);
        });
        Promise.all(promiseList).then(() => {
          this.$Message.success("删除成功");
          this.$emit("onDelete");
          this.closeSublayerNodeRemoveModal();
          this.$bus.emit("deleteSublayerSuccess");
        });
      }
    },
    getHotspotList() {
      this.$get(`/topoEdit/getHotspotsList`, {
        mapId: this.mapInfo.mapId,
        versionId: this.versionSelected.versionId,
      }).then(({ data }) => {
        if (data.code === "0000") {
          this.hotspotList = data.data;
        }
      });
    },
    handleAdd() {
      // this.$bus.emit("addHotspot");
      this.hotspotSelected = {};
      this.isHotspotVisible = true;
    },
    handleHotspotEdit(val) {
      let width = 0;
      let height = 0;
      if (val.size) {
        const size = val.size.split("*");
        width = size[0];
        height = size[1];
      }
      this.formMap = { ...val, width, height };
      this.isHotspotVisible = true;
    },
    handleChangeColorClick(val) {
      this.linkStyles.fill = val;
      this.updateLinkInfo();
    },
    handleHotspotDelete(val) {
      this.$Modal.confirm({
        title: "警告",
        content: "是否要删除该热点区域",
        onOk: () => {
          this.$post(`/topoEdit/deleteHotspotsById`, {
            hotspotId: val.hotspotId,
          }).then(() => {
            this.$Message.success("删除热点区域成功");
            this.handleCancelSelect();
            this.getHotspotList();
          });
        },
        onCancel: () => {},
      });
    },
    handleSaveHotspot() {
      if (!this.hotspotSelected.hotspotName) {
        return this.$Message.warning("名称不能为空");
      }
      if (this.hotspotSelected.hotspotId) {
        const params = {
          ...this.hotspotSelected,
          ...this.hotspotDragInfo,
        };
        if (this.isHotspotVisible) {
          params.size = `${this.formMap.width}*${this.formMap.height}`;
          this.hotspotSelected.size = params.size;
          this.$bus.emit("hotspotItemClick", params);
        }
        // 更新
        this.$post(`/topoEdit/updateHotspots`, params).then(() => {
          this.$Message.success("修改热点区域成功");
          this.closeEditHotspotModal();
          this.getHotspotList();
        });
      } else {
        // 新增
        const params = {
          ...this.hotspotSelected,
          mapId: this.mapInfo.mapId,
          position: "0,0",
          scale: 3.33,
          status: 1,
        };
        this.$post(`/topoEdit/insertHotspots`, params).then(() => {
          this.$Message.success("新增热点区域成功");
          this.closeEditHotspotModal();
          this.getHotspotList();
        });
      }
    },
    // 列表点击
    handleHotspotItemClick(val) {
      if (val.hotspotId === this.hotspotSelected.hotspotId) {
        this.handleCancelSelect();
        return;
      }
      let width = 0;
      let height = 0;
      if (val.size) {
        let [w, h] = val.size.split("*");
        width = +w;
        height = +h;
      }
      this.setHotspotSize({
        width,
        height,
      });

      this.hotspotSelected = val;
      this.$bus.emit("hotspotItemClick", val);
      this.setHotspot(true);
    },
    handleClearClick() {
      this.$bus.emit("onClearSvg");
    },
    handleCancelSelect() {
      this.setHotspot(false);
      this.hotspotSelected = {};
    },
    handleSubmmitHotspot() {
      this.hotspotSelected = {
        ...this.formMap,
        listOrder: +this.formMap.listOrder,
        status: +this.formMap.status,
      };

      this.setHotspotSize({
        width: +this.formMap.width,
        height: +this.formMap.height,
      });

      this.handleSaveHotspot();
    },
    handleHideTextChange(val) {
      this.setTextHideBySublayerList(val);
    },
    closeEditHotspotModal() {
      this.isHotspotVisible = false;
      this.resetForm();
    },
    resetForm() {
      this.formMap = {
        hotspotName: "",
        status: 1,
        listOrder: 1,
        width: 0,
        height: 0,
      };
    },
  },
};
</script>
<style lang="scss">
.topo-point-index-active {
  background-color: #eee;
}
.hotspot-layout {
  text-align: left;
  height: calc(80vh - 160px);
  padding: 0 10px;
  overflow: auto;
}
.hotspot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
  margin-bottom: 10px;
  list-style: none;
}
.hotspot-item-name {
  display: flex;
  width: 50%;
  background-color: $listItemBg;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding-left: 10px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  &:hover {
    background-color: rgba($color: $listItemBg, $alpha: 0.8);
  }
}
.hotspot-item-active {
  background-color: $primaryColor;
  &:hover {
    background-color: $primaryColor;
  }
}
.hotspot-btn-group {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
