<template>
  <Row class="topo-manger-layout">
    <Col span="5" class="topo-manger-left-view">
      <div
        style="
          text-align: left;
          font-size: 16px;
          font-weight: 700;
          margin-bottom: 10px;
        "
      >
        图层列表
      </div>
      <Input
        v-model="value"
        search
        enter-button
        placeholder="搜索"
        size="small"
      />
      <div class="topo-manger-left-tree">
        <Tree :data="menuList" @on-select-change="handleTreeChange"></Tree>
      </div>
    </Col>
    <Col span="19" style="padding: 10px">
      <div class="map-table-header">
        <div
          style="
            text-align: left;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 10px;
          "
        >
          图层信息
        </div>
        <Button type="primary" @click="toTopoEdit">编辑Topo</Button>
      </div>
      <Table border :columns="columns" :data="tableData">
        <template slot-scope="{ row }" slot="name">
          <strong>{{ row.name }}</strong>
        </template>
        <template slot-scope="{ row }" slot="action">
          <Button
            type="primary"
            size="small"
            icon="md-create"
            style="margin-right: 10px"
            @click="edit(row)"
          ></Button>
          <Button
            type="primary"
            size="small"
            icon="md-trash"
            @click="remove(row)"
          ></Button>
        </template>
      </Table>
      <div class="topo-manger-page">
        <Page :total="100" show-total show-elevator />
      </div>
    </Col>
  </Row>
</template>

<script>
import _ from "lodash";

export default {
  name: "Layermanger",

  data() {
    return {
      value: "",
      columns: [
        {
          title: "名称",
          key: "mapName",
        },
        {
          title: "所属目录",
          key: "mapId",
        },
        {
          title: "宽高",
          key: "mapSize",
        },
        {
          title: "背景图",
          key: "background",
        },
        {
          title: "层级",
          key: "mapIndex",
        },
        {
          title: "更新人",
          key: "updatedBy",
        },
        {
          title: "更新时间",
          key: "updatedTime",
        },
        {
          title: "Action",
          slot: "action",
          width: 150,
          align: "center",
        },
      ],
      tableData: [],
      menuList: [],
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      this.$get(`/topoEdit/getMenuList`).then(({ data }) => {
        if (data.code == "0000") {
          this.tableData = [];
          this.menuList = [];
          this.generateMenuList(_.cloneDeep(data.data));
          this.getMapList(_.cloneDeep(data.data));
        }
      });
    },
    // 递归获取所有的目录以及子目录 ，生成tree
    generateMenuList(data) {
      const getList = (list, menu) => {
        list.forEach((ele, index) => {
          if (ele.mapName) {
            menu.push({
              title: ele.mapName,
              key: ele.mapId,
              isMap: true,
            });
          } else {
            menu.push({
              title: ele.menuName,
              key: ele.menuId,
            });
          }

          if (ele.maps && ele.maps.length) {
            ele.children = ele.maps.filter(
              (eleMap) => ele.menuId === eleMap.menuId
            );
          }
          if (ele.children && ele.children.length) {
            if (!menu[index].children) {
              menu[index].children = [];
            }
            getList(ele.children, menu[index].children);
          }
        });
      };
      getList(data, this.menuList);
    },
    // 将所有的maps拿出来并进行扁平化处理
    getMapList(data) {
      let arr = [];
      for (let i = 0; i < data.length; i++) {
        const { maps } = data[i];
        if (maps && maps.length) {
          arr = arr.concat(...maps);
        }
      }
      this.tableData = arr;
    },
    // 点击树，获取当前目录下的所有图层
    handleTreeChange() {},
    toTopoEdit() {
      this.$router.push({
        path: "/topoEdit",
      });
    },
    edit() {},
    remove({ mapId }) {
      this.$Modal.confirm({
        title: "警告",
        content: "<p>是否要删除该图层</p>",
        onOk: () => {
          this.$post(`/topoEdit/deleteMap`, { mapId }).then((data) => {
            if (data.code == "0000") {
              this.$Message.success("删除成功");
              this.initData();
            }
          });
        },
        onCancel: () => {},
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.topo-manger-layout {
  padding: 0 10px;
  height: 96%;
  background-color: #fff;
}
.topo-manger-left-view {
  border-right: 1px solid #ccc;
  padding: 10px;
  height: 100%;
}
.topo-manger-left-tree {
  text-align: left;
}
.topo-manger-page {
  text-align: right;
  margin-top: 10px;
}
.map-table-header {
  display: flex;
  justify-content: space-between;
}
</style>
