<template>
  <text
    :x="
      textStyles.duration === '竖向'
        ? data.x + textPosition.y
        : data.x + textPosition.x
    "
    :y="
      textStyles.duration === '竖向'
        ? data.y - textPosition.x
        : data.y + textPosition.y
    "
    :font-size="data.fontSize"
    :fill="data.fontColor"
    style="letter-spacing: 2px; user-select: none"
    :style="{
      fontFamily: textStyles.fontFamily,
      filter: `drop-shadow(0 0 ${textStyles.dropShadowBlurRadius || 1}px ${
        textStyles.dropShadowColor
      }) drop-shadow(0 0 ${textStyles.dropShadowBlurRadius || 1}px ${
        textStyles.dropShadowColor
      })`,
    }"
    :rotate="textStyles.duration === '竖向' ? -90 : 0"
    :transform="
      textStyles.duration === '竖向'
        ? `rotate(${textStyles.rotate || 90} ${data.x + textPosition.y} ${
            data.y - textPosition.x
          })`
        : `rotate(${textStyles.rotate || 0} ${data.x + textPosition.x} ${
            data.y + textPosition.y
          })`
    "
  >
    {{ data.nodeText }}
  </text>
</template>

<script>
export default {
  name: "Nodetext",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },

  mounted() {},
  computed: {
    textPosition() {
      const { textPosition } = this.data;
      const [x, y] = textPosition.split(",");
      return { x: +x, y: +y };
    },
    textStyles() {
      const { textStyles } = this.data;
      return textStyles ? JSON.parse(textStyles) : {};
    },
  },
};
</script>

<style lang="scss" scoped></style>
