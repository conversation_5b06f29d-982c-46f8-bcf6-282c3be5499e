<template>
  <div class="topo-attribute-layout" v-show="!!nodeInfo.nodeId">
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">编码</Col>
      <Col span="20" class="text-left"
        ><span class="topo-text-select">{{ nodeInfo.nodeId }}</span>
      </Col>
      <Col span="4" class="text-left">类型</Col>
      <Col span="20" class="text-left"
        ><span>{{ nodeInfo.nodeType }}</span>
      </Col>
      <Col span="4" class="text-left">数据</Col>
      <Col span="20" class="text-left"
        ><Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleMetaEditClick"
          >编辑</Button
        >
      </Col>
      <Divider />
      <Col span="8" class="">
        <Row type="flex" align="middle">
          <Col span="6"> X&nbsp; </Col>
          <Col span="17">
            <InputNumber
              size="small"
              :value="nodeInfo.x"
              :step="-1"
              style="width: 100%"
              type="number"
              @on-change="handleInputChange($event, 'x')"
            ></InputNumber>
          </Col>
        </Row>
      </Col>
      <Col span="8" class="">
        <Row type="flex" align="middle">
          <Col span="6"> Y&nbsp; </Col>
          <Col span="17">
            <InputNumber
              size="small"
              style="width: 100%"
              :value="nodeInfo.y"
              :step="-1"
              type="number"
              @on-change="handleInputChange($event, 'y')"
            ></InputNumber>
          </Col>
        </Row>
      </Col>
      <Col span="8" class="">
        <Row type="flex" align="middle">
          <Col span="8"> R&nbsp; </Col>
          <Col span="16">
            <div style="display: flex">
              <InputNumber
                size="small"
                :value="nodeInfo.rotate"
                :max="360"
                :min="-360"
                :step="-1"
                @on-change="handleInputChange($event, 'rotate')"
              ></InputNumber>
              <span class="pl-5">°</span>
            </div>
          </Col>
        </Row>
      </Col>
      <Row type="flex" class="mt-5">
        <Col span="8" class="">
          <Row type="flex" align="middle">
            <Col span="6"> 宽&nbsp; </Col>
            <Col span="17">
              <InputNumber
                size="small"
                style="width: 100%"
                :value="nodeInfo.w"
                :step="-1"
                type="number"
                @on-change="handleInputChange($event, 'w')"
              ></InputNumber>
            </Col>
          </Row>
        </Col>
        <Col span="8" class="">
          <Row type="flex" align="middle">
            <Col span="6"> 高&nbsp; </Col>
            <Col span="17">
              <InputNumber
                size="small"
                style="width: 100%"
                :value="nodeInfo.h"
                :step="-1"
                type="number"
                @on-change="handleInputChange($event, 'h')"
              ></InputNumber>
            </Col>
          </Row>
        </Col>
      </Row>
    </Row>
    <Collapse v-model="value1">
      <Panel name="1">
        样式
        <div slot="content">
          <Row
            type="flex"
            align="middle"
            v-if="
              nodeInfo.type === 'CustomRect' || nodeInfo.type === 'CustomCircle'
            "
          >
            <Col span="4" class="text-left">填充</Col>
            <Col span="16" style="text-align: left">
              <ColorPicker
                class="node-fill-picker"
                size="small"
                alpha
                :value="nodeStyles.fill || ''"
                @on-change="handlNodeStylesChange($event, 'fill')"
            /></Col>
          </Row>

          <Row type="flex" align="middle">
            <Col span="4" class="text-left mb-5">图片</Col>
            <Col span="20" class="mb-5" style="text-align: left">
              <Input
                size="small"
                :value="nodeStyles.image"
                @on-change="handlNodeStylesChange($event, 'image')"
              ></Input>
            </Col>
            <Col span="4" class="text-left">填充</Col>
            <Col span="20" style="text-align: left">
              <i-switch
                :value="textStyles.isFill"
                @on-change="handlNodeStylesChange($event, 'isFill')"
                size="small"
              />
            </Col>
          </Row>
          <Row type="flex" align="middle" class="mt-5">
            <Col span="4" class="pr-5">变色</Col>
            <Col span="20" style="text-align: left">
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleChangeColorClick('green')"
                >绿</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleChangeColorClick('pink')"
                >粉</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleChangeColorClick('orange')"
                >橙</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleChangeColorClick('blue')"
                >蓝</Button
              >
              <Button
                type="primary"
                size="small"
                @click="handleChangeColorClick('reset')"
                >重置</Button
              >
            </Col>
          </Row>
          <Row type="flex" align="middle" class="mt-5">
            <Col span="4" class="pr-5"> 分排 </Col>
            <Col span="6">
              <Tooltip content="颜色">
                <ColorPicker
                  class="node-fill-picker"
                  size="small"
                  alpha
                  :value="line.fill || '#000000'"
                  @on-change="handlLineStylesChange($event, 'fill')"
                />
              </Tooltip>
            </Col>
            <Col span="6">
              <Tooltip content="长度" class="mr-5">
                <InputNumber
                  size="small"
                  v-model="line.length"
                  :step="-1"
                  style="width: 100%"
                  type="number"
                  :min="0"
                  @on-change="handlLineStylesChange($event, 'length')"
                ></InputNumber>
              </Tooltip>
            </Col>
            <Col span="6" class="mr-5">
              <Tooltip content="角度">
                <InputNumber
                  size="small"
                  v-model="line.rotate"
                  :step="-1"
                  style="width: 100%"
                  type="number"
                  :max="360"
                  :min="-360"
                  @on-change="handlLineStylesChange($event, 'rotate')"
                ></InputNumber>
              </Tooltip>
            </Col>
            <!-- <Col span="5">
              <Button
                type="primary"
                size="small"
                @click="handleLineStylesClick()"
                >设置</Button
              >
            </Col> -->
          </Row>
          <Divider />
          <Row type="flex">
            <Col span="6" class="text-left">自定义</Col>
            <Col span="6" offset="8" class="text-right">
              <Icon
                type="md-add"
                class="click"
                @click="$refs.customNodeStyle.show()"
            /></Col>
          </Row>
        </div>
      </Panel>
      <Panel name="2">
        文本
        <div slot="content">
          <Row type="flex" align="middle">
            <Col span="10" class="text-left">
              <Select
                :value="textStyles.fontFamily"
                size="small"
                style="text-align: left"
                @on-change="handlFontStylesChange($event, 'fontFamily')"
              >
                <Option
                  v-for="item in fontFamilyOptions"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</Option
                >
              </Select></Col
            >
            <Col span="6" offset="2" class="text-left">
              <Select
                :value="nodeInfo.fontSize"
                size="small"
                style="text-align: left"
                filterable
                allow-create
                @on-create="handleFontSizeCreate"
                @on-change="handlFontStylesChange($event, 'fontSize')"
              >
                <Option
                  v-for="item in fontSizeOptions"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</Option
                >
              </Select></Col
            >
            <Col span="5" offset="1" style="text-align: left" class="">
              <ColorPicker
                class="node-font-color-picker"
                size="small"
                alpha
                :value="nodeInfo.fontColor || ''"
                @on-change="handlFontStylesChange($event, 'fontColor')"
            /></Col>
            <Col
              span="10"
              class="mr-10 mt-5"
              style="
                font-size: 20px;
                display: flex;
                justify-content: space-between;
              "
            >
              <img
                src="../../assets/images/topo/left.png"
                class="topo-attribute-icon"
                @click="alignText('left')"
              />
              <img
                src="../../assets/images/topo/center.png"
                class="topo-attribute-icon"
                @click="alignText('center')"
              />
              <img
                src="../../assets/images/topo/right.png"
                class="topo-attribute-icon"
                @click="alignText('right')"
              />
            </Col>
            <Col
              span="6"
              offset="1"
              class="text-left mt-5"
              style="
                font-size: 20px;
                display: flex;
                justify-content: space-between;
              "
            >
              <img
                src="../../assets/images/topo/horizontal.png"
                class="topo-attribute-icon"
                @click="changeDuration('横向')"
              />
              <img
                src="../../assets/images/topo/vertical.png"
                class="topo-attribute-icon"
                @click="changeDuration('竖向')"
              />
            </Col>
            <Row type="flex" align="middle" class="mt-5">
              <Col span="4" class="text-left mb-5">发光</Col>
              <Col span="20" class="mb-5">
                <Row type="flex" align="middle">
                  <Col span="11">
                    <ColorPicker
                      class="node-drop-shadow-picker"
                      size="small"
                      alpha
                      :value="textStyles.dropShadowColor || '#ffffff'"
                      @on-change="
                        handleFontInputChange($event, 'dropShadowColor')
                      "
                    />
                  </Col>
                  <Col span="11" offset="2" class="gradient-content">
                    <InputNumber
                      size="small"
                      :min="0"
                      :value="textStyles.dropShadowBlurRadius"
                      @on-change="
                        handleFontInputChange($event, 'dropShadowBlurRadius')
                      "
                    ></InputNumber>
                  </Col>
                </Row>
              </Col>
            </Row>
            <Row type="flex" align="middle" class="gradient-content">
              <Col span="4" class="text-left mb-5">渐变</Col>
              <Col span="20" class="mb-5">
                <Row type="flex" align="middle">
                  <Col span="6" class="text-right"
                    ><Tooltip content="是否开启"> 开关 &nbsp;</Tooltip></Col
                  >
                  <Col span="8">
                    <i-switch
                      :value="textStyles.isGradient"
                      @on-change="handleFontInputChange($event, 'isGradient')"
                      size="small"
                    />
                  </Col>
                </Row>
              </Col>
              <Col span="20" offset="4" class="mb-5">
                <Row type="flex" align="middle">
                  <Col span="6" class="text-right"> 角度 &nbsp;</Col>
                  <Col span="8">
                    <InputNumber
                      size="small"
                      :min="0"
                      :max="360"
                      :step="10"
                      :value="textStyles.gradientAngle || 0"
                      @on-change="handleGradientAngleInputChange"
                    ></InputNumber>
                  </Col>
                </Row>
              </Col>
              <Col span="20" offset="4" class="mb-5">
                <Row type="flex" align="middle">
                  <Col span="6" class="text-right"
                    ><Tooltip content="颜色1设置"> 颜色1 &nbsp;</Tooltip></Col
                  >
                  <Col span="8">
                    <ColorPicker
                      class="node-gradient-picker"
                      size="small"
                      alpha
                      :value="textStyles.gradientColor1 || '#ffffff'"
                      @on-change="
                        handleFontInputChange($event, 'gradientColor1')
                      "
                    />
                  </Col>
                  <Col span="8" offset="2">
                    <InputNumber
                      size="small"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      :value="textStyles.gradientOffset1 || 0"
                      @on-change="
                        handleFontInputChange($event, 'gradientOffset1')
                      "
                    ></InputNumber>
                  </Col>
                </Row>
              </Col>
              <Col offset="4" span="20" class="mb-5">
                <Row type="flex" align="middle">
                  <Col span="6" class="text-right"
                    ><Tooltip content="颜色2设置"> 颜色2 &nbsp;</Tooltip></Col
                  >
                  <Col span="8">
                    <ColorPicker
                      class="node-gradient-picker"
                      size="small"
                      alpha
                      :value="textStyles.gradientColor2 || '#ffffff'"
                      @on-change="
                        handleFontInputChange($event, 'gradientColor2')
                      "
                    />
                  </Col>
                  <Col span="8" offset="2">
                    <InputNumber
                      size="small"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      :value="textStyles.gradientOffset2 || 0"
                      @on-change="
                        handleFontInputChange($event, 'gradientOffset2')
                      "
                    ></InputNumber>
                  </Col>
                </Row>
              </Col>
              <Col offset="4" span="20" class="mb-5">
                <Row type="flex" align="middle">
                  <Col span="6" class="text-right"
                    ><Tooltip content="颜色3设置"> 颜色3 &nbsp;</Tooltip></Col
                  >
                  <Col span="8">
                    <ColorPicker
                      class="node-gradient-picker"
                      size="small"
                      alpha
                      :value="textStyles.gradientColor3 || '#ffffff'"
                      @on-change="
                        handleFontInputChange($event, 'gradientColor3')
                      "
                    />
                  </Col>
                  <Col span="8" offset="2">
                    <InputNumber
                      size="small"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      :value="textStyles.gradientOffset3 || 0"
                      @on-change="
                        handleFontInputChange($event, 'gradientOffset3')
                      "
                    ></InputNumber>
                  </Col>
                </Row>
              </Col>
            </Row>
            <Divider />
            <Col span="4" class="text-left mb-5">文字</Col>
            <Col span="20" class="mb-5"
              ><Input
                size="small"
                :value="nodeInfo.nodeText"
                :type="nodeInfo.type === 'CustomText' ? 'textarea' : 'text'"
                @on-change="handleInputChange($event, 'nodeText')"
              ></Input>
            </Col>
            <Col span="8" class="">
              <Row type="flex" align="middle">
                <Col span="6"> X&nbsp; </Col>
                <Col span="17">
                  <InputNumber
                    :disabled="nodeInfo.type === 'CustomText'"
                    size="small"
                    :value="textPosition.x"
                    style="width: 100%"
                    type="number"
                    @on-change="handlTextPositionChange($event, 'x')"
                  ></InputNumber>
                </Col>
              </Row>
            </Col>
            <Col span="8" class="">
              <Row type="flex" align="middle">
                <Col span="6"> Y&nbsp; </Col>
                <Col span="17">
                  <InputNumber
                    :disabled="nodeInfo.type === 'CustomText'"
                    size="small"
                    style="width: 100%"
                    :value="textPosition.y"
                    type="number"
                    @on-change="handlTextPositionChange($event, 'y')"
                  ></InputNumber>
                </Col>
              </Row>
            </Col>
            <Col span="8" class="">
              <Row type="flex" align="middle">
                <Col span="8"> R&nbsp; </Col>
                <Col span="16">
                  <div style="display: flex">
                    <InputNumber
                      :disabled="nodeInfo.type === 'CustomText'"
                      size="small"
                      :value="textStyles.rotate || 0"
                      :max="360"
                      :min="-360"
                      @on-change="handleFontInputChange($event, 'rotate')"
                    ></InputNumber>
                    <span class="pl-5">°</span>
                  </div>
                </Col>
              </Row>
            </Col>
            <Divider />
            <Col span="6" class="text-left">自定义</Col>
            <Col span="6" offset="8" class="text-right">
              <Icon
                type="md-add"
                class="click"
                @click="$refs.customFontStyle.show()"
            /></Col>
          </Row>
        </div>
      </Panel>
      <Panel name="3">
        关联
        <div slot="content">
          <Row type="flex" align="middle">
            <Col span="4" class="text-left">连线 </Col>
            <Col span="15">
              {{ nodeInfo.bindLink }}
            </Col>
            <Col span="5">
              <img
                src="../../assets/images/topo/delete.png"
                class="mr-5 topo-attribute-icon"
                @click="handleClearBindLink('end')"
              />
            </Col>

            <Col span="4" class="text-left">位置 </Col>
            <Col span="20">
              {{ nodeInfo.bindSubLink }}
            </Col>
            <Divider />
            <Col span="4" class="text-left">组件 </Col>
            <Col span="20">
              <Select
                size="small"
                :value="nodeInfo.compClass"
                style="text-align: left"
                @on-change="handleInputChange($event, 'compClass')"
                clearable
              >
                <OptionGroup label="内部对象">
                  <Option
                    v-for="(item, index) in compontentsList.internalBindList"
                    :value="item.value"
                    :key="item.label + index + '-internal'"
                    >{{ item.label }}</Option
                  >
                </OptionGroup>
                <OptionGroup label="外部对象">
                  <Option
                    v-for="(item, index) in compontentsList.externalBindList"
                    :value="item.value"
                    :key="item.label + index + '-external'"
                    >{{ item.label }}</Option
                  >
                </OptionGroup>
              </Select>
            </Col>
            <Divider />
            <Col span="4" class="text-left">数据</Col>
            <Col span="12" offset="8" class="text-right">
              <Icon type="md-add" class="click" @click="handleAddDataClick"
            /></Col>
            <Col span="24">
              <div style="text-align: left; padding: 10px 10px 0 10px">
                <Tag v-for="item in dataKeys" :key="item">{{ item }}</Tag>
              </div></Col
            >
            <Divider />
            <Col span="4" class="text-left">图层</Col>
            <Col span="12" offset="8" class="text-right">
              <Icon type="md-add" class="click" @click="handleSelectMapClick"
            /></Col>
            <Col span="24">
              <div style="text-align: left; padding: 10px 10px 0 10px">
                <Tag v-for="item in mapNames" :key="item">{{ item }}</Tag>
              </div></Col
            >
            <Divider />
            <Col span="4" class="text-left">动画</Col>
            <Col span="12" offset="8" class="text-right">
              <Icon type="md-add"
            /></Col>
          </Row>
        </div>
      </Panel>
      <Panel name="4"> 扩展 </Panel>
    </Collapse>
    <DataBindModal
      ref="dataBind"
      :bindData="nodeInfo.bindData"
      @on-change="handleDataChange"
    ></DataBindModal>
    <MapSelectModal
      ref="selectMap"
      :bindMap="nodeInfo.bindMap"
      @on-select="handleMapSelect"
    ></MapSelectModal>
    <CustomStylesModal
      ref="customNodeStyle"
      :styles="nodeStyles.customStyles"
      @handleChange="handleCustomStyleChange"
    ></CustomStylesModal>
    <CustomStylesModal
      ref="customFontStyle"
      :styles="textStyles.customFontStyles"
      @handleChange="handleCustomFontStyleChange"
    ></CustomStylesModal>
    <MetaDataEditModal ref="metaDataEdit" :info="nodeInfo"></MetaDataEditModal>
  </div>
</template>
<script>
import { mapState, mapActions } from "vuex";
import DataBindModal from "./Modal/DataBindModal.vue";
import MapSelectModal from "./Modal/MapSelectModal.vue";
import CustomStylesModal from "./Modal/CustomStylesModal.vue";
import MetaDataEditModal from "./Modal/MetaDataEditModal.vue";
import { getCoordinateByAngle } from "./utils";

export default {
  props: {
    compontentsList: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    DataBindModal,
    MapSelectModal,
    CustomStylesModal,
    MetaDataEditModal,
  },
  data() {
    return {
      value1: [1, 2, 3],
      nodeStyles: {},
      textStyles: {},
      textPosition: {},
      nodeInfo: {
        bindMap: {},
        bindData: {},
      },
      fontFamilyOptions: [
        {
          label: "跟随系统",
          value: "system-ui",
        },
        {
          label: "微软雅黑",
          value: "Microsoft Yahei",
        },

        {
          label: "宋体",
          value: "SimSun",
        },
        {
          label: "新宋体",
          value: "NSimSun",
        },
        {
          label: "仿宋",
          value: "FangSong",
        },
        {
          label: "黑体",
          value: "SimHei",
        },
        {
          label: "Arial",
          value: "Arial",
        },
        {
          label: "Helvetica Neue",
          value: "Helvetica Neue",
        },
        {
          label: "sans-serif",
          value: "sans-serif",
        },
        {
          label: "cursive",
          value: "cursive",
        },
      ],
      fontSizeOptions: [],
      line: {
        length: 100,
        rotate: 0,
      },
      multiSize: {
        width: 0,
        height: 0,
      },
    };
  },
  mounted() {
    this.initEventBus();
  },
  beforeDestroy() {
    this.$bus.off("onNodeLinkSelected");
  },
  computed: {
    ...mapState("topoStore", {
      mapList: "mapList",
    }),
    mapNames() {
      const mapIdList = this.nodeInfo?.bindMap?.mapId || [];
      return this.mapList
        .map((ele) => {
          if (mapIdList.includes(ele.mapId)) {
            return ele.mapName;
          }
        })
        .filter((ele) => !!ele);
    },
    dataKeys() {
      const keys = this.nodeInfo?.bindData?.keys || [];
      return keys;
    },
  },

  methods: {
    initEventBus() {
      // 监听点击获取的nodeInfo
      this.$bus.on("onNodeLinkSelected", (val = []) => {
        if (val.length === 1) {
          this.nodeInfo = val[0];
          this.multiSize = {
            width: this.nodeInfo.w,
            height: this.nodeInfo.h,
          };
          this.initData();
        }
      });
    },
    initData() {
      const {
        nodeStyles,
        textStyles,
        textPosition,
        fontSize,
        bindData,
        bindMap,
      } = this.nodeInfo;

      this.nodeInfo.bindData = bindData ? bindData : {};
      this.nodeInfo.bindMap = bindMap ? bindMap : {};
      this.nodeStyles = nodeStyles ? JSON.parse(nodeStyles) : {};
      this.textStyles = textStyles ? JSON.parse(textStyles) : {};
      this.line = { length: 0, rotate: 0, ...this.nodeStyles.line };
      if (textPosition) {
        const [x, y] = textPosition.split(",").map((ele) => +ele);
        this.textPosition = { x, y };
      }

      this.fontSizeOptions = new Array(149).fill(0).map((ele, index) => {
        return {
          label: `${index + 12}`,
          value: `${index + 12}`,
        };
      });
      if (+fontSize > 150) {
        this.fontSizeOptions.push({
          label: fontSize + "",
          value: fontSize + "",
        });
      }
    },
    // metaData数据修改
    handleMetaEditClick() {
      this.$refs.metaDataEdit.show();
    },
    handleAddDataClick() {
      this.$refs.dataBind.show();
    },
    handleSelectMapClick() {
      this.$refs.selectMap.show();
    },
    // input输入框改变  color选择器跟input的值要分开处理
    handleInputChange(val, type) {
      if (type === "nodeText") {
        const { value } = val.target;
        this.nodeInfo[type] = value;
      } else {
        this.nodeInfo[type] = val;
      }
      this.updateNodeInfo();
    },
    handleSaveMultiNodeClick() {
      const { width, height } = this.multiSize;
      this.nodeInfo.w = width;
      this.nodeInfo.h = height;
      this.updateNodeInfo();
    },
    handleClearBindLink() {
      this.nodeInfo.bindLink = "";
      this.nodeInfo.bindSubLink = 0;
      this.updateNodeInfo();
    },
    // 选择图层
    handleMapSelect(val) {
      this.nodeInfo.bindMap.mapId = val;
      this.updateNodeInfo();
    },
    // flag 1:直接取val 2:val.target.value
    handlNodeStylesChange(val, type) {
      val = val instanceof Object ? val.target.value : val;
      this.nodeStyles[type] = val;
      this.updateNodeInfo();
    },
    handleChangeColorClick(val) {
      this.nodeStyles.dynamicColor = val === "reset" ? "" : val;

      this.updateNodeInfo();
    },
    handleFontSizeCreate(val) {
      let value = +val;
      if (Number.isNaN(value)) {
        return this.$Message.warning("请输入数字");
      }
      this.fontSizeOptions.push({
        value: value,
        label: value,
      });
    },
    // 大小、字体、颜色选择
    handlFontStylesChange(val, type) {
      if (val === undefined) return;
      if (type === "fontFamily") {
        this.textStyles[type] = val;
      } else {
        if (type === "fontSize") {
          if (Number.isNaN(+val)) {
            val = "16";
          } else if (+val < 12) {
            val = "12";
          }
        }
        this.nodeInfo[type] = val;
      }
      this.updateNodeInfo();
    },
    handlLineStylesChange(val, type) {
      if (!this.nodeStyles.line) {
        this.nodeStyles.line = {};
      }
      this.nodeStyles.line[type] = val;
      this.updateNodeInfo();
    },
    handleLineStylesClick() {
      this.nodeStyles.line = this.line;
      this.updateNodeInfo();
    },
    // 自定义样式
    handleCustomStyleChange(val) {
      this.nodeStyles.customStyles = val;
      this.updateNodeInfo();
    },
    // 自定义文字样式
    handleCustomFontStyleChange(val) {
      this.textStyles.customFontStyles = val;
      this.updateNodeInfo();
    },
    // 文字对齐
    alignText(position) {
      this.textStyles.align = position;
      this.updateNodeInfo();
    },
    changeDuration(duration) {
      this.textStyles.duration = duration;
      this.updateNodeInfo();
    },
    // 文字位置
    handlTextPositionChange(val, key) {
      this.textPosition[key] = val;
      this.updateNodeInfo();
    },
    handleGradientAngleInputChange(val) {
      const { x1, y1, x2, y2 } = getCoordinateByAngle(+val);
      this.textStyles.gradientX1 = x1;
      this.textStyles.gradientY1 = y1;
      this.textStyles.gradientX2 = x2;
      this.textStyles.gradientY2 = y2;
      this.textStyles.gradientAngle = val;
      this.updateNodeInfo();
    },
    // 样式改变
    handleFontInputChange(val, type) {
      this.textStyles[type] = val;
      this.updateNodeInfo();
    },
    // 数据绑定
    handleDataChange(val) {
      this.nodeInfo.bindData.keys = val;
      this.updateNodeInfo();
    },
    updateNodeInfo() {
      const { x, y, w, h } = this.nodeInfo;
      this.nodeInfo.nodeSize = `${w}*${h}`;
      this.nodeInfo.nodePosition = `${x},${y}`;
      this.nodeInfo.middleRotatePoint = {
        x: x + w / 2,
        y: y + h / 2,
      };
      this.nodeInfo.textPosition = `${this.textPosition.x},${this.textPosition.y}`;
      this.nodeInfo.nodeStyles = JSON.stringify(this.nodeStyles);
      this.nodeInfo.textStyles = JSON.stringify(this.textStyles);

      this.$bus.emit("updateNodeSelectedList", this.nodeInfo);
    },
  },
};
</script>
<style lang="scss">
.topo-point-index-active {
  background-color: #eee;
}
.node-fill-picker .ivu-select-dropdown {
  left: -50px !important;
}
.node-font-color-picker .ivu-select-dropdown {
  left: -197px !important;
}
.node-drop-shadow-picker .ivu-select-dropdown {
  left: -50px !important;
}
.node-gradient-picker .ivu-select-dropdown {
  left: -95px !important;
}
.gradient-content .ivu-input-number-small {
  width: 100%;
}
</style>
