<template>
  <Modal v-model="isVisible" title="版本发布">
    <Form
      :model="form"
      ref="formValidate"
      label-position="right"
      :rules="ruleValidate"
      :label-width="100"
    >
      <FormItem label="版本类型" prop="dummy">
        <Select v-model="form.dummy">
          <Option :value="1">临时版本</Option>
          <Option :value="0">正式版本</Option>
        </Select>
      </FormItem>
      <FormItem label="版本名称" prop="versionName">
        <Input v-model="form.versionName"></Input>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="hide">取消</Button>
      <Button type="primary" @click="handleSubmit">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  props: {
    mapInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      isVisible: false,
      form: {
        versionName: "",
        dummy: 1,
      },
      ruleValidate: {
        dummy: [
          {
            required: true,
          },
        ],
        versionName: [
          {
            required: true,
            message: "名称不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    ...mapState("topoStore", {
      versionList: "versionList",
    }),
  },
  methods: {
    ...mapActions("topoStore", ["getVersionList"]),
    show() {
      this.isVisible = true;
    },
    hide() {
      this.isVisible = false;
      this.form.versionName = "";
      this.form.dummy = 1;
    },
    handleSubmit() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          this.publishVersion();
        }
      });
    },
    publishVersion() {
      // 如果是临时版本，则不超过五个
      if (this.form.dummy) {
        const list = this.versionList.filter((ele) => ele.dummy);
        if (list.length >= 5) {
          return this.$Message.warning("临时版本不能超过5个");
        }
      }
      this.$post(`/topoEdit/publish`, {
        mapId: this.mapInfo.mapId,
        ...this.form,
      }).then((data) => {
        if (data.code == "0000") {
          this.$Message.success("添加成功");
          this.getVersionList(this.mapInfo.mapId);
          this.hide();
        }
      });
    },
  },
};
</script>

<style lang="scss">
.topo-form-item {
  margin-bottom: 12px;
}
.topo-form-item .ivu-form-item-content {
  margin-left: 20px !important;
  padding: 0 30px;
}
</style>
