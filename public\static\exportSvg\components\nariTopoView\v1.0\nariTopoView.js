/**
 * @description 拓扑图查看器
 * <AUTHOR>
 * @date 2020-12-29
 * @class TopoView
 * @extends {SVGComponentBase}
 */

const colorOptions = {
  _35KV: "#4bbefc",
  _110KV: "#4bbefc",
  _220KV: "#f70202",
  _500KV: "#6b83f8",
};
// 重新绘制图标，比例需要调整
const scaleOffset = 1.4;

class NariTopoView extends SVGComponentBase {
  /**
   * @constructor 组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件编码
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心 4为单一组件调试
   * @param option {Object} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认值
   */
  constructor(
    id,
    code,
    container,
    workMode,
    option = {},
    useDefaultOpt = true
  ) {
    super(id, code, container, workMode, option, useDefaultOpt);
    this.dblclick = option.dblclick || function () {};
    this.mounted = option.mounted || function () {};
    this.update = option.update || function () {};
    this._setupDefaultValues();
    this._draw();
    if (this.property.basic.needSync && workMode !== 2 && workMode !== 0) {
      this._initEventSync();
    }
    this.scaleByExport = this.property.viewSetting.isSmall ? 10 : 1;
    this.isSmall = this.property.viewSetting.isSmall;
    isSmall = this.isSmall;
    this.is500 = this.property.viewSetting.is500;
    this.compScale = this.property.viewSetting.compScale || 1;
    this.sublayerBaseList = this.property.viewSetting.sublayerList;
    if (workMode !== 2) {
      // this._getWSData();
    }
  }

  /**
   * @description 初始化组件所需要的数据
   */
  _setupDefaultValues() {
    super._setupDefaultValues();
    //topo图的节点和链路数据
    this.topoData = {};
    this.mapList = [];
    this.ip = "http://**************";
    this.port = "8080";
    this.mapIdList = [];
    //子图层列表
    this.subLayerList = [];
    //热点区域列表
    this.hotspotsList = [];
    // 绘制变压站所需的svg数据
    this.svgData = {};
    //图层位移缩放
    this.transform = {
      k: 0.15,
      x: 0,
      y: 0,
    };
    //图层动画时长
    this.animeDur = 0;
    //图层动画对象
    this.anime = null;
    //电厂组件列表
    this.powerPlantMap = {};
    //变电站组件列表
    this.transStationMap = {};
    //换流站组件列表
    this.exchStationMap = {};
    //分区板组件列表
    this.areaBoardMap = {};
    //图层数据
    this.mapData = {};

    // 颜色数据
    this.colorData = {};
    //节点样式 s小/l大/m变电站带母线/p打印
    this.nodeType = "l";
    //展示子图层           500kv链路      500kv节点      500kv标注    500kv链路标注   检修'SKzNoEbwja4',500网架
    this.showSublayer = ["SDAfqbRLOiv", "ShSbMrmGpXX"];
    this.activeValueFlag = true;
    this.change1Flag = this.change2Flag = this.change3Flag = false;
    this.change4Flag = this.change5Flag = this.change6Flag = false;
    this.divideFill = false;
    this.isRepairStatus = false;
    this.colorTimer = null;
  }

  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    super._initProperty();
    let options = {
      basic: {
        className: "NariTopoView",
        needSync: true,
      },
      viewSetting: {
        loadMapId: "O0InrXDzGj", //500kv
        // loadMapId: 'yO3u76xwhH',  //220kv
        // ip: 'http://**************',
        // port: '8899',
        // ftpIp: 'http://**************',
        // ftpPort: '6818',
        // dataUrl: 'http://************:8800/getDataByMapIdDynamic?mapId=l8kYKgfeUF',

        ip: "http://***********",
        port: "8899",
        ftpIp: "http://***********",
        ftpPort: "80",
        dataUrl: "http://***********/dwyzt/getRuntimeData",
        colorUrl: "http://***********/dwyztApp/dwyzt/getOverHaulStatus",
        w: 9800,
        h: 7033,
        minS: 1,
      },
    };

    let optionDic = [
      {
        name: "viewSetting",
        displayName: "显示设置",
        show: true,
        editable: true,
        children: [
          {
            name: "loadMapId",
            displayName: "加载图层id",
            description: "加载图层id",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "ip",
            displayName: "ip地址",
            description: "ip地址",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "port",
            displayName: "端口",
            description: "端口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "ftpIp",
            displayName: "ftp的ip地址",
            description: "ftp的ip地址",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "ftpPort",
            displayName: "ftp端口",
            description: "ftp端口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "dataUrl",
            displayName: "数据接口",
            description: "数据接口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "w",
            displayName: "显示宽度",
            description: "显示宽度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: "h",
            displayName: "显示高度",
            description: "显示高度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: "minS",
            displayName: "最小比例",
            description: "最小比例",
            type: OptionType.double,
            show: true,
            editable: true,
          },
        ],
      },
    ];

    this._addProperty(options, optionDic);
  }

  /**
   * @description 初始化组件事件
   */
  _initEvents() {
    super._initEvents();
    this.invokeFunc = this.invokeFunc.concat([
      {
        name: "switchTo220",
        displayName: "200KV全景图",
        params: [],
      },
      {
        name: "close500Link",
        displayName: "关闭500连线",
        params: [],
      },
      {
        name: "open500Link",
        displayName: "打开500连线",
        params: [],
      },
      {
        name: "switchTo500",
        displayName: "500KV全景图",
        params: [],
      },
      {
        name: "flyToHotspots",
        displayName: "跳转热点",
        params: [
          {
            name: "hotspotsId",
            displayName: "热点Id",
            type: OptionType.enum,
            options: [],
          },
        ],
      },
      {
        name: "switchLinkBolckMode",
        displayName: "线框模式",
        params: [],
      },
      {
        name: "getTotalData",
        displayName: "获取数据",
        params: [],
      },
      {
        name: "switchDivideLink",
        displayName: "切换显示分区联络线",
        params: [
          {
            name: "status",
            displayName: "是否显示",
            type: OptionType.boolean,
          },
        ],
      },
      {
        name: "switchDivideFill",
        displayName: "切换显示分区填充",
        params: [
          {
            name: "status",
            displayName: "是否显示",
            type: OptionType.boolean,
          },
        ],
      },
      {
        name: "equipmentTwinkle",
        displayName: "设备闪烁",
        params: [
          {
            name: "id",
            displayName: "设备ID",
            type: OptionType.string,
          },
        ],
      },
      {
        name: "closeEquipmentTwinkle",
        displayName: "关闭设备闪烁",
        params: [
          {
            name: "id",
            displayName: "设备ID",
            type: OptionType.string,
          },
        ],
      },
      {
        name: "crossingChannel",
        displayName: "过江通道",
        params: [
          {
            name: "status",
            displayName: "是否显示",
            type: OptionType.boolean,
          },
        ],
      },
    ]);
  }

  /**
   * 切换子图层显示隐藏状态
   * @param {string} name 子图层名称
   * @param {boolean} isShow 是否显示
   */
  switchSublayer(name, isShow) {
    let sublayerId = this.subLayerList.filter((d) => d.sublayerName === name)[0]
      .sublayerId;
    this.mainSVG
      .selectAll(`.${sublayerId}`)
      .style("display", isShow ? "block" : "none");
  }

  /**
   * @description 绘制入口
   */
  _draw() {
    super._draw();
    this.mainSVG
      .style("fill-rule", "evenodd")
      .style("clip-rule", "evenodd")
      .style("stroke-linejoin", "round")
      .style("stroke-miterlimit", 2);
    this._generateStyle();
    this._generateDefines();
    this._generateSymbol();
    this.containerPosition = {
      x: 0,
      y: 0,
      width: this.property.basic.frame[2],
      height: this.property.basic.frame[3],
    };
    this.mainSVG.select(".nari_current_container").remove();
    this.mainSVG.append("g").attr("class", "nari_current_container");
    // this._getTopo();
  }

  //   设备跳转
  equipmentLocation(position) {
    this.mainSVG
      .transition()
      .duration(1000)
      .call(
        this.zoom.transform,
        d3.zoomIdentity.translate(position.x, position.y).scale(position.k)
      );
  }
  // 220分区跳转
  flyToSub(position) {
    this.showSublayer = [
      "KS5tPfwEKN",
      "FS5tPfwEKT",
      "FS5wMfwEKT",
      "KS3tPfwEKM",
      "FS3tPfwEKF",
      "Fw5TXwwEDo",
      "M4WacTIW5E",
      "W4WacTIW5M",
    ];
    this.nodeType = "m";
    this.flyTo = true;
    this.mainSVG
      .transition()
      .duration(2000)
      .call(
        this.zoom.transform,
        d3.zoomIdentity.translate(position.x, position.y).scale(position.k)
      );
  }
  // 有功值
  activeValue(flag) {
    this.activeValueFlag = flag;
    // flag ? this._addShowSublayer(['Zr124XA8fb']):this._removeShowSublayer(['Zr124XA8fb']); Fw5TXwwEDo
    if (flag) {
      this.nodeCon.selectAll("text.SDAfqbRLOiv").each(function (d) {
        if (d3.select(this).style("fill") == "rgb(0, 255, 0)") {
          d3.select(this).style("display", "block");
        }
      });

      this.nodeCon.selectAll("text.Fw5TXwwEDo").each(function (d) {
        if (d3.select(this).style("fill") == "rgb(0, 255, 0)") {
          d3.select(this).style("display", "block");
        }
      });
    } else {
      this.nodeCon.selectAll("text.SDAfqbRLOiv").each(function (d) {
        if (d3.select(this).style("fill") == "rgb(0, 255, 0)") {
          d3.select(this).style("display", "none");
        }
      });

      this.nodeCon.selectAll("text.Fw5TXwwEDo").each(function (d) {
        if (d3.select(this).style("fill") == "rgb(0, 255, 0)") {
          d3.select(this).style("display", "none");
        }
      });
    }
  }
  // //断面、分排 'KS5tPfwEKN', 'FS5tPfwEKT', 'KS5TXwwEDE'
  // change1(flag){
  //     this.change1Flag = flag;
  //     if(flag){
  //         // this._addShowSublayer(['KS5tPfwEKN'])
  //         this.linkCon.selectAll('.KS5tPfwEKN').style('display', 'block');
  //     }else{
  //         // this._removeShowSublayer(['KS5tPfwEKN'])
  //         this.linkCon.selectAll('.KS5tPfwEKN').style('display', 'none');
  //     }
  // }

  //分排
  change2(flag) {
    this.change2Flag = flag;
    if (flag) {
      // this._addShowSublayer(['FS5tPfwEKT'])
      this.nodeCon.selectAll(".takeLine").style("display", "block");
    } else {
      // this._removeShowSublayer(['FS5tPfwEKT'])
      this.nodeCon.selectAll(".takeLine").style("display", "none");
    }
  }

  //充电
  change3(flag) {
    this.change3Flag = flag;
    if (flag) {
      // this._addShowSublayer(['KS5TXwwEDE'])
      this.linkCon.selectAll(".dashLine").classed("topo-flash", true);
    } else {
      // this._removeShowSublayer(['KS5TXwwEDE'])
      this.linkCon.selectAll(".dashLine").classed("topo-flash", false);
    }
  }
  // 检修
  change4(flag) {
    this.change4Flag = flag;
    if (flag) {
      // this._addShowSublayer(['FS5tPfwEKT'])
      this.nodeCon.selectAll(".SKzNoEbwja4").style("display", "block");
    } else {
      // this._removeShowSublayer(['FS5tPfwEKT'])
      this.nodeCon.selectAll(".SKzNoEbwja4").style("display", "none");
    }
  }
  //缺陷
  change5(flag) {
    this.change5Flag = flag;
    if (flag) {
      // this._addShowSublayer(['FS5tPfwEKT'])
      this.nodeCon.selectAll(".qxNode").style("display", "block");
    } else {
      // this._removeShowSublayer(['FS5tPfwEKT'])
      this.nodeCon.selectAll(".qxNode").style("display", "none");
    }
  }

  // 新设备
  change6(flag) {
    this.change6Flag = flag;
    if (flag) {
      // this._addShowSublayer(['FS5tPfwEKT'])
      this.nodeCon
        .select("#node_SGB74ZfN79_10748_113997366104686638")
        .classed("topo-flash", true);
      this.nodeCon
        .select("#node_uTU3KweONr_782640_113997366104686638")
        .classed("topo-flash", true);
    } else {
      // this._removeShowSublayer(['FS5tPfwEKT'])
      this.nodeCon
        .select("#node_SGB74ZfN79_10748_113997366104686638")
        .classed("topo-flash", false);
      this.nodeCon
        .select("#node_uTU3KweONr_782640_113997366104686638")
        .classed("topo-flash", false);
    }
  }

  switchTo220() {
    this.transform = {
      x: 0,
      y: 0,
      k: 1,
    };
    //展示子图层 500链路 500节点 500红字 220链路 220节点 220分区名称
    this.showSublayer = [
      "KS5tPfwEKN",
      "FS5tPfwEKT",
      "FS5wMfwEKT",
      "KS3tPfwEKM",
      "FS3tPfwEKF",
      "M4WacTIW5E",
      "Sxj1QeDYF2G",
    ];
    this.nodeType = "s";
    this.animeDur = 0;
    this._transformMain();
  }

  close500Link() {
    // this._removeShowSublayer(['KS5tPfwEKN', 's5iL6EUIpN', 'Zr124XA8fb']);
    this.linkCon.selectAll(".KS5tPfwEKN").style("display", "none");
    this.nodeCon.selectAll(".s5iL6EUIpN").style("display", "none");
    this.nodeCon.selectAll(".Zr124XA8fb").style("display", "none");
    this.arrowCon.style("display", "none");
  }

  open500Link() {
    // this._addShowSublayer(['KS5tPfwEKN', 's5iL6EUIpN']);
    this.linkCon.selectAll(".KS5tPfwEKN").style("display", "block");
    this.nodeCon.selectAll(".s5iL6EUIpN").style("display", "block");
    this.nodeCon.selectAll(".Zr124XA8fb").style("display", "block");
    this.arrowCon.style("display", "block");
  }

  switchTo500() {
    this.transform = {
      x: 0,
      y: 0,
      k: this.property.viewSetting.minS,
    };
    this.showSublayer = [
      "KS5tPfwEKN",
      "FS5tPfwEKT",
      "KS5TXwwEDE",
      "Zr124XA8fb",
      "ShSbMrmGpXX",
    ];
    this.nodeType = "l";
    this.animeDur = 0;
    this._transformMain();
  }

  flyToHotspots(id) {
    let hotspot = this.hotspotsList.filter((d) => d.hotspotId === id)[0];
    let [x, y] = hotspot.position.split(",");
    this.transform = {
      x: x,
      y: y,
      k: hotspot.scale,
    };
    this.showSublayer = [
      "KS5tPfwEKN",
      "FS5tPfwEKT",
      "FS5wMfwEKT",
      "KS3tPfwEKM",
      "FS3tPfwEKF",
      "Fw5TXwwEDo",
      "M4WacTIW5E",
      "W4WacTIW5M",
      "Sxj1QeDYF2G",
    ];
    this.nodeType = "m";
    this.animeDur = 5000;
    this._transformMain();
  }

  /**
   * 显示/隐藏跨区线
   * @param {boolean} status 状态 true开启 false关闭
   */
  switchDivideLink(status) {
    this.isDivide = status;

    this.linkCon.selectAll("path").style("opacity", `${status ? 0.3 : 1}`);
    this.nodeCon.selectAll("svg").style("opacity", `${status ? 0.3 : 1}`);
    this.nodeCon.selectAll(".Fw5TXwwEDo").style("opacity", status ? 0 : 1);
    this.nodeCon.selectAll("text.SDAfqbRLOiv").style("opacity", status ? 0 : 1);

    this.linkCon.selectAll(".topo-isDivide").style("opacity", 1);
    this.nodeCon.selectAll(".topo-isDivide").style("opacity", 1);

    status ? this.close500Link() : this.open500Link();
  }

  /**
   * 显示/隐藏分区填充
   * @param {boolean} status 状态 true开启 false关闭
   */
  switchDivideFill(status) {
    this.divideFill = status;
    this.blockCon.selectAll("path").style("fill-opacity", status ? 1 : 0);
  }
  /**
   * 显示/隐藏检修态
   * 目前实现连线变色
   * @param {boolean} status
   */
  switchRepairStatus(status) {
    this.isRepairStatus = status;
    if (status) {
      // if(JSON.stringify(this.colorData) !== "{}"){
      //     this._updateColor(this.colorData)
      // }
    } else {
      if (this.colorTimer != null) {
        clearInterval(this.colorTimer);
      }

      if (JSON.stringify(this.mapData) !== "{}") {
        this._update(this.mapData);
      }
    }
  }

  switchLinkBolckMode() {
    d3.select(this.container).style("background-color", "#fff");
    this.linkCon.selectAll(".KS3tPfwEKM").style("stroke", "#000");
    this.blockCon
      .selectAll("path")
      .style("filter", "")
      .style("fill", "none")
      .style("stroke", "#000")
      .style("stroke-width", 2.5);
    for (const key in this.powerPlantMap) {
      this.powerPlantMap[key].setType("p");
    }
    for (const key in this.exchStationMap) {
      this.exchStationMap[key].setType("p");
    }
    for (const key in this.transStationMap) {
      let comp = this.transStationMap[key];
      comp.con
        .attr("x", comp.option.x)
        .attr("y", comp.option.y)
        .attr("width", comp.option.w)
        .attr("height", comp.option.h);
      comp.setType("p");
    }
  }

  blockFlash(id, status) {
    this.blockCon
      .select(`#topoLink_${id}`)
      .classed("topo-flash", status ? true : false);
  }

  /**
   * 设备闪烁
   * @param {string} id 设备id
   */
  equipmentTwinkle(id) {
    setTimeout(() => {
      this.transStationMap[id]._setSize(50);
      this.nodeCon
        .select(`#node_${id.replace("#", "_")}`)
        .classed("topo-flash-node", true);
    }, 8000);
  }

  /**
   * 取消设备闪烁
   * @param {string} id 设备id
   */
  closeEquipmentTwinkle(id) {
    setTimeout(() => {
      this.transStationMap[id]._setSize(this.nodeType === "m" ? 60 : 31);
      this.nodeCon
        .select(`#node_${id.replace("#", "_")}`)
        .classed("topo-flash-node", false);
    }, 8000);
  }

  /**
   * 切换到过江子图层
   * @param {boolean} status 状态 true开启 false关闭
   */
  crossingChannel(status) {
    this.change1Flag = status;
    // this.transform = {
    //   x: 0,
    //   y: 0,
    //   k: 1,
    // };
    // this.showSublayer = ['SuVpsLPSY1l'];
    // //   this.nodeType = 'l';
    // this.animeDur = 0;
    // this._transformMain();
    this.linkCon
      .selectAll("path")
      .style("filter", `brightness(${status ? 0.3 : 1})`);
    this.nodeCon
      .selectAll("svg")
      .style("filter", `brightness(${status ? 0.3 : 1})`);
    this.nodeCon
      .selectAll("text")
      .style("filter", `brightness(${status ? 0.3 : 1})`);
    this.arrowCon
      .selectAll("polygon")
      .style("filter", `brightness(${status ? 0.3 : 1})`);

    this.linkCon.selectAll(".SuVpsLPSY1l").style("filter", "brightness(1)");
    this.nodeCon.selectAll(".SuVpsLPSY1l").style("filter", "brightness(1)");
    this.arrowCon.selectAll(".SuVpsLPSY1l").style("filter", "brightness(1)");
    this.nodeCon
      .selectAll(".SuVpsLPSY1l")
      .select("text")
      .style("filter", "brightness(1)");
  }

  _transformMain() {
    this.mainSVG
      //   .select('.nari_current_container')
      .transition()
      .duration(this.workMode == 0 ? this.animeDur : 100)
      .call(
        this.zoom.transform,
        d3.zoomIdentity
          .translate(this.transform.x, this.transform.y)
          .scale(this.transform.k)
      );
  }

  /**
   * 绑定缩放拖拽事件
   */
  _drawNodeLink() {
    this.showSublayer = this.sublayerBaseList;

    if (this.is500) {
      this.nodeType = "l";
    } else {
      this.nodeType = "m";
    }

    console.log("🚀 ~ _drawNodeLink ~ this.showSublayer:", this.showSublayer);

    if (this.timer != null) {
      clearTimeout(this.timer);
    }
    //   if (isFirst) {
    //     isFirst = false;
    //     return;
    //   }

    this.timer = setTimeout(() => {
      this._drawLinks(this.topoData.links);
      requestAnimationFrame(() => {
        this._drawNodes(this.topoData.nodes).then(() => {
          this.flyTo = false;
          this.activeValue(this.activeValueFlag); //有功值
          this.switchDivideFill(this.divideFill); //分区填充
          if (JSON.stringify(this.mapData) !== "{}") {
            requestAnimationFrame(() => {
              this._update(this.mapData);
              this.switchDivideLink(this.isDivide); //动态分区联络线，通过数据返回标识
            });
          }
        });
      });

      //   this._drawQx();
    }, 1000);
  }
  /**
   * 定义style
   */
  _generateStyle() {
    let defs = this.mainSVG.append("defs");
    defs.append("style").attr("type", "text/css").text(`
          @keyframes halfArrowMove { 100%  { offset-distance: 50%; } } 
          .area-board-name {
              fill: url(#area_board_name);
              filter: url(#area_board_name_shadow);
              font-size: 69px;
              font-family: 方正正大黑简体;
              dominant-baseline: central;
              text-anchor: middle;
              transform: translate(86px, 48px);
              letter-spacing: 5px;
          }
          .area-board-value {
              fill: url(#area_board_value);
              font-size: 90px;
              font-weight: bold;
              font-family: 微软雅黑;
              dominant-baseline: central;
              text-anchor: end;
              letter-spacing: 1px;
          }
      `);
  }

  /**
   * 定义svg的defs
   */
  _generateDefines() {
    let defs = this.mainSVG.append("defs");
    d3.text(`${this._foldPath}/defs.html`).then((data) => {
      if (data.includes("inset-shadow-block")) {
        defs.attr("class", "mydefs");
      }
      defs.html(data);
    });
  }

  _generateSymbol() {
    const images = [
      "powerPlant_35KV",
      "powerPlant_110KV",
      "powerPlant_220KV",
      "powerPlant_WIND",
      "powerPlant_PV",
      "powerPlant_STORAGE",
      "powerPlant_500KV",
      "exchStation_m",
      "exchStation_l",
      "exchStation_p",
      "exchStation_s_500KV",
      "exchStation_s_1000KV",
      "powerPlant_NUCLEAR",
      "areaBoard2-0",
      "areaBoard2-1",
      "areaBoard2-2",
      "areaBoard3-0",
      "areaBoard3-1",
      "areaBoard3-2",
      "t_node",
    ];
    images.forEach((ele) => {
      d3.xml(`${this._foldPath}/images/${ele}.svg`).then((xml) => {
        const symbolContent = xml.documentElement.innerHTML;
        const viewBox = xml.documentElement.getAttribute("viewBox");

        this.mainSVG
          .append("symbol")
          .attr("id", ele)
          .attr("viewBox", viewBox)
          .html(symbolContent);
      });
    });
  }

  async _prevMap() {
    this.mapIdList.pop();
    let mapId = this.mapIdList.reduceRight(
      (prev, cur) => [...prev, cur],
      []
    )[0];
    this.mapIdList.pop();
    await this._drawTopoByMapId(mapId);
  }

  /**
   * @description 获取拓扑图所有节点数据入口
   */
  async initTopo(data, sublayerList) {
    this.mapObj = data.map;
    this.topoData = data;
    let mapId = this.mapObj.mapId;
    this.mapExternalBind = this.mapObj.externalBind;
    this.mapInternalBind = this.mapObj.internalBind;

    this.subLayerList = sublayerList.sort((a, b) => {
      return b.listOrder - a.listOrder;
    });
    this.subLayerOrderMap = {};
    this.subLayerList.forEach((sub) => {
      this.subLayerOrderMap[sub.sublayerId] = sub.listOrder;
    });

    await this._drawTopoByMapId(mapId);
  }

  /**
   * 根据图层id绘制图层
   * @param {string} mapId 图层id
   */
  async _drawTopoByMapId(mapId) {
    if (mapId === "") return;
    await this._loadTransformerSubstationSvgData();
    await this._drawTopo(mapId);

    this._drawNodeLink();

    this.switchDivideFill(false); //默认不显示区域填充
  }

  /**
   * 实时数据获取更新
   * @param {*} data
   */
  updateRealData(data) {
    this.mapData = data;
    this._update(data);
  }

  /**
   * 绘制拓扑图
   * @returns
   */
  _drawTopo(mapId) {
    return new Promise((resolve, reject) => {
      if (this.topoData === {}) reject();
      this.mapIdList.push(mapId);
      let topoContainer = this.mainSVG
        .select(".nari_current_container")
        .attr("id", `topo_${mapId}`);
      if ($(topoContainer.node()).children().length !== 0) {
        this.mainSVG.select(".topo_last_container").remove();
        this.mainSVG
          .select(".nari_current_container")
          .attr("class", "topo_temp_container")
          .transition()
          .duration(1000)
          .style("opacity", 0)
          .style("transform", "scale(0)")
          .on("end", () => {
            this.mainSVG.select(".topo_temp_container").remove();
          });
        topoContainer = this.mainSVG
          .append("g")
          .attr("class", "nari_current_container")
          .attr("id", `topo_${mapId}`)
          .style("opacity", 0)
          .style("transform", "scale(0)");
        topoContainer
          .transition()
          .duration(1000)
          .style("opacity", 1)
          .style("transform", "scale(1)");
      }
      let tw = this.mapObj.mapSize.split("*")[0];
      let th = this.mapObj.mapSize.split("*")[1];
      //   this.mainSVG.attr("viewBox", [0, 0, tw, th]);
      this.mainSVG
        .attr(
          "viewBox",
          `0 0 ${this.property.viewSetting.w} ${this.property.viewSetting.h}`
        )
        .attr("data-scale", this.compScale)
        .attr("xmlns", "http://www.w3.org/2000/svg")
        .attr("xmlns:xlink", "http://www.w3.org/1999/xlink")
        .style("background-color", "#1a2f42");

      // this.mainSVG.attr('viewBox', `0 0 19600 12600`);
      topoContainer.attr("data-vb", `0 0 ${tw} ${th}`);

      this.blockCon = topoContainer.append("g").attr("id", "block-container");
      this.linkCon = topoContainer.append("g").attr("id", "link-container");
      this.arrowCon = topoContainer.append("g").attr("id", "arrow-container");
      this.nodeCon = topoContainer.append("g").attr("id", "node-container");

      //TODO:
      // this.conMap = {};
      // this.blockConMap = {};
      // this.linkConMap = {};
      // this.arrowConMap = {};
      // this.nodeConMap = {};

      // this.subLayerList.forEach(sub =>{
      //    let temp = topoContainer.append('g').attr('id',`sub-container-${sub.sublayerId}`);
      //    let blockTemp = temp.append('g').attr('id',`block-container-${sub.sublayerId}`)
      //    let linkTemp =temp.append('g').attr('id',`link-container-${sub.sublayerId}`)
      //    let arrowTemp =temp.append('g').attr('id',`arrow-container-${sub.sublayerId}`)
      //    let nodeTemp =temp.append('g').attr('id',`node-container-${sub.sublayerId}`)
      //    this.conMap[sub.sublayerId] = temp;

      //    this.blockConMap[sub.sublayerId] = blockTemp;
      //    this.linkConMap[sub.sublayerId] = linkTemp;
      //    this.arrowConMap[sub.sublayerId] = arrowTemp;
      //    this.nodeConMap[sub.sublayerId] = nodeTemp;

      // });
      // console.log(this.blockConMap)

      //对数据按照图层进行排序

      this.topoData.links.sort((a, b) => {
        return (
          this.subLayerOrderMap[
            b &&
              b.sublayerList &&
              b.sublayerList.sort((m, n) => {
                return (
                  this.subLayerOrderMap[m.sublayerId] -
                  this.subLayerOrderMap[n.sublayerId]
                );
              })[0] &&
              b &&
              b.sublayerList &&
              b.sublayerList.sort((m, n) => {
                return (
                  this.subLayerOrderMap[m.sublayerId] -
                  this.subLayerOrderMap[n.sublayerId]
                );
              })[0].sublayerId
          ] -
          this.subLayerOrderMap[
            a &&
              a.sublayerList &&
              a.sublayerList.sort((m, n) => {
                return (
                  this.subLayerOrderMap[m.sublayerId] -
                  this.subLayerOrderMap[n.sublayerId]
                );
              })[0] &&
              a &&
              a.sublayerList &&
              a.sublayerList.sort((m, n) => {
                return (
                  this.subLayerOrderMap[m.sublayerId] -
                  this.subLayerOrderMap[n.sublayerId]
                );
              })[0].sublayerId
          ]
        );
      });

      this.topoData.nodes.sort((a, b) => {
        return (
          this.subLayerOrderMap[
            b &&
              b.sublayerList &&
              b.sublayerList.sort((m, n) => {
                return (
                  this.subLayerOrderMap[m.sublayerId] -
                  this.subLayerOrderMap[n.sublayerId]
                );
              })[0] &&
              b &&
              b.sublayerList &&
              b.sublayerList.sort((m, n) => {
                return (
                  this.subLayerOrderMap[m.sublayerId] -
                  this.subLayerOrderMap[n.sublayerId]
                );
              })[0].sublayerId
          ] -
          this.subLayerOrderMap[
            a &&
              a.sublayerList &&
              a.sublayerList.sort((m, n) => {
                return (
                  this.subLayerOrderMap[m.sublayerId] -
                  this.subLayerOrderMap[n.sublayerId]
                );
              })[0] &&
              a &&
              a.sublayerList &&
              a.sublayerList.sort((m, n) => {
                return (
                  this.subLayerOrderMap[m.sublayerId] -
                  this.subLayerOrderMap[n.sublayerId]
                );
              })[0].sublayerId
          ]
        );
      });

      //pc端不用TODO:
      //   this._drawLinks(this.topoData.links);
      //   this._drawNodes(this.topoData.nodes);

      resolve();
    });
  }

  /**
   * 通过子图层绘制拓扑图
   */
  _drawTopoBySublayer() {}

  /**
   * 通过子图层移除拓扑图
   */
  _removeTopoBySublayer() {}

  /**
   * 绘制图层中所有链路
   * @param {Array} links 图层中链路数据
   */
  _drawLinks(links) {
    links.forEach((link) => {
      requestAnimationFrame(() => {
        let style = JSON.parse(link.linkStyles);
        let linkDom = null;

        if (style.isBlock) {
          if (
            !link.sublayerList ||
            !link.sublayerList.some((d) =>
              this.showSublayer.includes(d.sublayerId)
            )
          ) {
            return;
          }

          //绘制分区
          if (this.blockCon.select(`#topoLink_${link.linkId}`).node() !== null)
            return;
          linkDom = this.blockCon
            .append("path")
            .attr("id", "topoLink_" + link.linkId)
            .attr(
              "keyid",
              link.metaData && getStationId(link.metaData.rtKeyId0)
            )
            .attr(
              "class",
              link.sublayerList
                ? link.sublayerList.map((d) => d.sublayerId).join(" ")
                : ""
            )
            .attr("d", link.linkPath)
            .style("stroke", "#414141")
            .style("stroke-width", link.linkWidth)
            .style("fill", style.fill)
            .style("fill-opacity", 0);
          if (link.bindMap.mapId) {
            this._bindLinkMap(linkDom, link);
          }
        } else {
          //绘制链路
          // let linkNode = this.linkCon.select(`#topoLink_${link.linkId.replace('#', '_')}`);
          //如果链路所在子图层不在展示的子图层内且链路存在则删除链路

          if (
            link.sublayerList &&
            link.sublayerList
              .map((d) => d.sublayerId)
              .filter((d) => this.showSublayer.includes(d)).length === 0
          ) {
            if (
              this.linkCon
                .select(`#topoLink_${link.linkId.replace("#", "_")}`)
                .node() !== null
            ) {
              this.linkCon
                .select(`#topoLink_${link.linkId.replace("#", "_")}`)
                .remove();
              this.arrowCon
                .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
                .remove();
            }
            return;
          }
          //如果链路已存在则不重新绘制
          if (
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .node() !== null
          ) {
            return;
          }

          if (!link.metaData) return;
          //默认链路宽度为6  特高压子图层链路宽度为8, 220KV链路子图层链路宽度为1.5
          let linkWidth = {
            _35KV: this.isSmall ? 0.2 : 1.5,
            _110KV: this.isSmall ? 0.2 : 1.5,
            _220KV: this.isSmall ? 0.4 : 2,
            _500KV: this.isSmall ? 0.52 : 2,
          };
          //   if (link.sublayerList) {
          //     if (
          //       link.sublayerList.map((d) => d.sublayerId).includes("KoBiqOUwMo")
          //     ) {
          //       linkWidth = this.isSmall ? 0.52 : 8;
          //     } else if (
          //       link.sublayerList.map((d) => d.sublayerId).includes("KS3tPfwEKM")
          //     ) {
          //       linkWidth = this.isSmall ? 0.1 : 1.5;
          //     }
          //   }
          // console.log("要绘制的link", link.linkId);

          let linkNode = this.linkCon
            .append("path")
            .attr("id", "topoLink_" + link.linkId.replace("#", "_"))
            .attr(
              "class",
              link.sublayerList
                ? link.sublayerList.map((d) => d.sublayerId).join(" ")
                : "" + this._isLinkCrossArea(link.metaData)
                ? "topo_crossLink"
                : ""
            )
            .attr("d", link.linkPath)
            .attr(
              "keyid",
              link.metaData && getStationId(link.metaData.rtKeyId0)
            )
            .style("stroke-width", link.linkWidth)
            .style("stroke", colorOptions[link.metaData.volt])
            .style("fill", "none");
          this._isLinkCrossArea(link.metaData);
          // if (linkNode.node() !== null) {
          if (link.sublayerList && link.sublayerList.length) {
            if (
              this.arrowCon
                .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
                .node() === null
            ) {
              // 箭头容器
              this.arrowCon
                .append("g")
                .attr("id", `topoArrow_${link.linkId.replace("#", "_")}`);
              //   this._drawExternalCompOnLink(link, linkNode);
            }
          } else {
            if (
              this.arrowCon
                .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
                .node() !== null
            ) {
              this.arrowCon
                .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
                .remove();
            }
          }
          // return;
          // }
        }
      });
    });
  }

  //   _removeLinkBindNode(linkId) {
  //     let nodes = this.topoData.nodes;
  //     let bindNodes = nodes.filter((n) => {
  //       return n.bindLink == linkId;
  //     });
  //     bindNodes.forEach((bn) => {
  //       this.nodeCon.select(`#node_${bn.nodeId.replace("#", "_")}`).remove();
  //     });
  //   }

  /**
   * 添加显示子图层
   * @param {Array} arr 子图层id数组
   */
  _addShowSublayer(arr) {
    arr.forEach((item) => {
      if (this.showSublayer.filter((d) => d === item).length === 0) {
        this.showSublayer.push(item);
      }
    });
  }

  /**
   * 删除显示子图层
   * @param {Array} arr 子图层id数组
   */
  _removeShowSublayer(arr) {
    arr.forEach((item) => {
      this.showSublayer = this.showSublayer.filter((d) => d !== item);
    });
  }

  /**
   * 判断链路是否跨分区
   * @param {Object} metaData 链路MetaData对象
   * @returns 是否跨分区
   */
  _isLinkCrossArea(metaData) {
    if (!metaData.hasOwnProperty("areaKeyId")) return false;
    let areaList = metaData.areaKeyId.split(",");
    if (
      areaList.length >= 2 &&
      areaList.filter((d) => d === "0").length > 0 &&
      areaList[0] !== areaList[1]
    ) {
      return true;
    }
    return false;
  }

  /**
   * 绘制链路的外部对象
   * @param {Object} link 链路
   */
  _drawExternalCompOnLink(link, linkDom, data, num, inx) {
    if (link.compClass && link.compClass !== "") {
      let externalComp = this.mapExternalBind[link.compClass];
      let arrowGroup = this.arrowCon.select(
        `#topoArrow_${link.linkId.replace("#", "_")}`
      );
      // if(arrowGroup.node() === null){
      eval(
        `new ${externalComp.className}(link.linkId, 'test', arrowGroup, 2, externalComp.options, link, linkDom, data,num,inx )`
      );
      // }else{
      // arrowGroup.remove();
      // }
    }
  }

  // 绘制缺陷
  _drawQx() {
    let qxNodeList = [
      {
        x: 3264,
        y: 4970,
        w: 50,
        h: 50,
      },
      {
        x: 1040,
        y: 1119,
        w: 50,
        h: 50,
      },
      {
        x: 4400,
        y: 1460,
        w: 50,
        h: 50,
      },
    ];
    for (let i = 0; i < qxNodeList.length; i++) {
      this.nodeCon
        .append("image")
        //   .attr('id', `node_${node.nodeId.replace('#', '_')}`)
        .attr("class", "qxNode")
        .attr("x", qxNodeList[i].x)
        .attr("y", qxNodeList[i].y)
        .attr("width", qxNodeList[i].w)
        .attr("height", qxNodeList[i].h)
        .style("display", this.change5Flag ? "block" : "none")
        .attr("xlink:href", `${this._foldPath}/images/qx.svg`);
    }
  }

  /**
   * 绘制图层中所有节点
   * @param {Array} nodes 图层中节点数据
   */
  _drawNodes(nodes) {
    return new Promise((resolve, reject) => {
      let drawNodeList = [];
      for (let node of nodes) {
        if (
          this._needDrawNode(node) &&
          drawNodeList.filter((d) => d.nodeId === node.nodeId).length === 0
        ) {
          drawNodeList.push(node);
        }
      }
      const _this = this;
      function addNodes(resolve) {
        // let loadNum = 2000;
        for (let i = 0; i <= drawNodeList.length; i++) {
          if (i >= drawNodeList.length) {
            console.log("finish draw nodes");
            _this.drawEnd && _this.drawEnd();
            resolve();
            return;
          }

          let node = drawNodeList[i];
          requestAnimationFrame(() => {
            _this._drawNode(node);
          });
        }
      }
      requestAnimationFrame(() => {
        addNodes(resolve);
      });
    });
  }

  _downloadSvg() {
    let svgElement = this.mainSVG.node();
    let svgContent = svgElement.outerHTML;

    return svgContent;
    // let aTag = document.createElement("a");
    // let blob = new Blob([svgContent]);
    // aTag.download = "test.svg";
    // aTag.href = URL.createObjectURL(blob);
    // aTag.click();
    // URL.revokeObjectURL(blob);
  }
  /**
   * 判断是否需要绘制节点
   * @param {Object} node node对象
   * @returns true为绘制 false为不绘制
   */
  _needDrawNode(node) {
    //当节点的子图层列表与现在展示的子图层列表不匹配时  不展示节点
    if (
      node.sublayerList &&
      node.sublayerList
        .map((d) => d.sublayerId)
        .filter((d) => this.showSublayer.includes(d)).length === 0
    ) {
      //如果节点存在则删除节点
      if (
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).node() !==
        null
      ) {
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).remove();
        delete this.powerPlantMap[node.nodeId];
        delete this.transStationMap[node.nodeId];
        delete this.exchStationMap[node.nodeId];
        delete this.areaBoardMap[node.nodeId];
      }
      return false;
    }
    // 节点绑定的线路或站点不存在，则不绘制节点,删除线路
    // if (!this.showSublayer.includes("SDAfqbRLOiv")) {
    //   //只判断线上文字图层节点
    //   let classN = node.bindLink
    //     ? node.sublayerList.map((d) => d.sublayerId).join(" ")
    //     : "";
    //   if (classN != "" && classN.includes("Fw5TXwwEDo")) {
    //     if (
    //       node.bindLink &&
    //       this.linkCon
    //         .select(`#topoLink_${node.bindLink.replace("#", "_")}`)
    //         .node() == null
    //     ) {
    //       this.nodeCon
    //         .select(`#node_${node.nodeId.replace("#", "_")}`)
    //         .remove();
    //       return false;
    //     }
    //   }
    // }
    // if (
    //   node.bindLink &&
    //   this.linkCon.select(`#topoLink_${node.bindLink.replace("#", "_")}`).node() == null
    // ) {
    //   this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).remove();
    //   return false;
    // }

    //如果需要绘制节点且节点已存在 则不创建新节点但是同步状态
    if (
      this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).node() !==
      null
    ) {
      if (this.powerPlantMap[node.nodeId])
        this.powerPlantMap[node.nodeId].setType(this.nodeType);
      if (this.transStationMap[node.nodeId])
        this.transStationMap[node.nodeId].setType(this.nodeType);
      if (this.exchStationMap[node.nodeId])
        this.exchStationMap[node.nodeId].setType(this.nodeType);
      return false;
    }
    return true;
  }

  _drawNode(node) {
    if (!node) return;
    let nodeDom = null;
    switch (node.nodeType) {
      case "circle":
        nodeDom = this._drawCircleNode(this.nodeCon, node);
        break;
      case "rect":
        nodeDom = this._drawRectNode(this.nodeCon, node);
        break;
      case "text":
        nodeDom = this._drawTextNode(this.nodeCon, node);
        break;
      case "image":
        nodeDom = this._drawImageNode(this.nodeCon, node);
        break;
      case "jx1":
        nodeDom = this._drawImageNode(this.nodeCon, node);
        //   console.log(node)
        break;
      case "t":
        nodeDom = this._drawTNode(this.nodeCon, node);
        break;
      default:
        if (
          node.metaData &&
          (node.metaData.type === "TransStation" ||
            node.metaData.type === "PowerStation" ||
            node.metaData.type === "ExchStation")
        ) {
          nodeDom = this._drawTransformerSubstation(this.nodeCon, node);
        } else {
          nodeDom = this._drawComponentNode(this.nodeCon, node);
        }
        break;
    }
    let nodeStyle = JSON.parse(node.nodeStyles);
    // console.log(nodeStyle)
    if (nodeStyle && nodeStyle.line) {
      let nodeLine = nodeStyle.line;
      if (!nodeLine.length) return;
      let nodePos = {
        x: Number(node.nodePosition.split(",")[0]),
        y: Number(node.nodePosition.split(",")[1]),
        w: Number(node.nodeSize.split("*")[0]),
        h: Number(node.nodeSize.split("*")[1]),
      };
      // console.log(node)
      // 绘制分排
      let lineDom = this.nodeCon
        .append("line")
        .attr("class", "takeLine")
        .attr("x1", nodePos.x + nodePos.w / 2 - nodeLine.length / 2)
        .attr("y1", nodePos.y + nodePos.h / 2)
        .attr("x2", nodePos.x + nodePos.w / 2 + nodeLine.length / 2)
        .attr("y2", nodePos.y + nodePos.h / 2)
        .attr("stroke-width", 3)
        .attr("stroke", "white")
        .style("display", this.change2Flag ? "block" : "none")
        // .attr('transform-origin','center')
        // TODO:rotate  后两个参数为旋转中心
        .attr(
          "transform",
          `rotate(${nodeLine.rotate || 0} ${nodePos.x + nodePos.w / 2} ${
            nodePos.y + nodePos.h / 2
          })`
        );
      // .attr('transform-origin','center')
    }
    if (nodeDom !== null) {
      //   this._bindLinkMap(nodeDom, node);
      if (node.nodeText !== "" && node.nodeType !== "text") {
        // this._drawNodeText(this.nodeCon, node);
      }
    }
  }

  /**
   * 通过node对象获取节点位置以及宽高
   * @param {Object} node 节点对象
   * @returns {x,y,w,h} 节点位置以及宽高
   */
  _getPositionAndSize(node) {
    return {
      x: Number(node.nodePosition.split(",")[0]),
      y: Number(node.nodePosition.split(",")[1]),
      w: Number(node.nodeSize.split("*")[0]),
      h: Number(node.nodeSize.split("*")[1]),
    };
  }

  /**
   * 绘制矩形节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawRectNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("rect")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("keyid", node.metaData && getStationId(node.metaData.rtKeyId0))
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr("fill", style.fill);
    return nodeDom;
  }

  /**
   * 绘制椭圆节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawCircleNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("ellipse")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("keyid", node.metaData && getStationId(node.metaData.rtKeyId0))
      .attr("cx", x + w / 2)
      .attr("cy", y + h / 2)
      .attr("rx", w / 2)
      .attr("ry", h / 2)
      .attr("stroke", style.stroke)
      .attr("stroke-width", style.strokeWidth)
      .attr("fill", style.fill);
    return nodeDom;
  }

  /**
   * 绘制节点文字
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawNodeText(con, node) {
    let { x, y } = this._getPositionAndSize(node);
    let textPosition = node.textPosition.split(",").map((d) => (d = Number(d)));
    let textStyle = JSON.parse(node.textStyles);
    con
      .append("text")
      .attr("x", textPosition[0] + x)
      .attr("y", textPosition[1] + y)
      .attr("rotate", textStyle.duration === "竖向" ? -90 : 0)
      .attr(
        "transform",
        textStyle.duration === "竖向"
          ? `rotate(90 ${x + node.fontSize / 2} ${y + node.fontSize / 2})`
          : ""
      )
      .style("font-size", node.fontSize)
      .style("fill", node.fontColor)
      .style("letter-space", "2px")
      .style("user-select", "none")
      .text(node.nodeText);
  }

  /**
   * 绘制文字节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawTextNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let textStyle = JSON.parse(node.textStyles);
    let fontColor = node.fontColor;
    if (
      node.sublayerList &&
      node.sublayerList.map((d) => d.sublayerId).includes("KS5TXwwEDE")
    ) {
      fontColor = "url(#text_500KV_trans_name)";
    }
    if (
      node.sublayerList &&
      node.sublayerList.map((d) => d.sublayerId).includes("SDAfqbRLOiv") &&
      node.fontSize == (this.isSmall ? "14" : "110")
    ) {
      fontColor = "url(#text_500KV_WJ)";
    }
    const pattern = new RegExp("[\u4E00-\u9FA5]+");
    const letterSpacing = pattern.test(node.nodeText)
      ? `-${3 / this.scaleByExport}px`
      : "0";

    let nodeDom = con
      .append("text")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("keyid", node.metaData && getStationId(node.metaData.rtKeyId0))
      .attr(
        "class",
        node.sublayerList
          ? node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr(
        "x",
        textStyle.align === "left"
          ? x
          : textStyle.align === "center"
          ? x + w / 2
          : x + w
      )
      .attr("y", y + +node.fontSize)
      .attr("width", w)
      .attr("height", h)
      .style("fill", fontColor)
      .style("user-select", "none")
      .style("font-size", node.fontSize)
      .style("letter-spacing", letterSpacing)
      .style("font-family", "AR PL UKai CN,楷体,楷体_GB2312")
      // .style('dominant-baseline', 'hanging')
      .style(
        "text-anchor",
        textStyle.align === "left"
          ? "start"
          : textStyle.align === "center"
          ? "middle"
          : "end"
      )
      .attr(
        "transform",
        node.rotate === 0
          ? ""
          : `rotate(${node.rotate} ${x + w / 2} ${y + h / 2})`
      );
    const list = node.nodeText ? node.nodeText.split(/[\r\n|↵]+/) : [];
    if (list.length > 1) {
      list.forEach((d, i) => {
        nodeDom
          .append("tspan")
          .attr("dy", i === 0 ? 0 : node.fontSize)
          .attr("x", x)
          .style("letter-spacing", letterSpacing)
          .style("font-family", "AR PL UKai CN,楷体,楷体_GB2312")
          .text(d);
      });
    } else {
      nodeDom.text(node.nodeText === "0000" ? "0" : node.nodeText);
    }
    if (textStyle.duration === "竖向") {
      nodeDom
        .style("text-anchor", "start")
        .attr("rotate", "-90")
        .attr("transform", `rotate(90 ${x} ${y})`)
        .style("dominant-baseline", "");
    }
    return nodeDom;
  }

  /**
   * 绘制图片节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawImageNode(con, node) {
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("image")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("keyid", node.metaData && getStationId(node.metaData.rtKeyId0))
      .attr(
        "class",
        node.sublayerList
          ? node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr(
        "xlink:href",
        this.property.viewSetting.ftpIp +
          ":" +
          this.property.viewSetting.ftpPort +
          style.image
      );
    return nodeDom;
  }

  /**
   * 绘制T节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawTNode(con, node) {
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let container = con
      .append("svg")
      .attr(
        "class",
        node.sublayerList
          ? node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr("nodeType", node.metaData?.type)
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("keyid", node.metaData && getStationId(node.metaData.rtKeyId0))
      .attr("viewBox", "0 0 246 246")
      // .style("transform", "translateZ(0)")
      .style("transform-origin", "center center");
    container
      .append("use")
      .attr("width", 246)
      .attr("height", 246)
      .attr("xlink:href", "#t_node");

    return container;
  }

  /**
   * 绘制组件节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawComponentNode(con, node) {
    if (node.compClass === "" || node.compClass === undefined) return;
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let internalComp = this.mapInternalBind[node.compClass];
    if (internalComp) {
      //   let container = con
      //     .append("foreignObject")
      //     .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      //     .attr("keyid", node.metaData && getStationId(node.metaData.rtKeyId0))
      //     .attr(
      //       "class",
      //       node.sublayerList
      //         ? node.sublayerList.map((d) => d.sublayerId).join(" ")
      //         : ""
      //     )
      //     .attr("x", x)
      //     .attr("y", y)
      //     .attr("width", w)
      //     .attr("height", h)
      //     .append("xhtml:div")
      //     .node();
      internalComp.options["basic"] = {
        frame: [0, 0, w, h],
        position: { x, y },
      };
      let opt = $.extend(
        true,
        internalComp.options,
        JSON.parse(node.nodeStyles).hasOwnProperty("customStyles")
          ? JSON.parse(node.nodeStyles).customStyles
          : {}
      );
      let comp = eval(
        `new ${internalComp.className}(node, 'test', con, 2, {'property': opt, 'metaData': node.metaData})`
      );
      if (internalComp.className === "AreaBoard") {
        this.areaBoardMap[node.nodeId] = comp;
      }
      return comp.mainSVG;
    }
  }

  /**
   * 加载电厂svg数据
   */
  _loadTransformerSubstationSvgData() {
    return new Promise((resolve, reject) => {
      d3.json(`${this._foldPath}/json/svgData.json`).then((data) => {
        // d3.json(`${this._foldPath}/json/svgData_30.json`).then((data) => {

        this.svgData = data;
        resolve();
      });
    });
  }
  _emitType(cb) {
    cb(this.emitType);

    this.emitType = "";
  }

  /**
   * 绘制电厂节点
   * @param {Ibject} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawTransformerSubstation(con, node) {
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let container = con
      .append("svg")
      .attr(
        "class",
        node.sublayerList
          ? node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr("nodeType", node.metaData.type)
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("keyid", node.metaData && getStationId(node.metaData.rtKeyId0))
      // .style("transform", "translateZ(0)")
      .style("transform-origin", "center center");

    // if (node.nodeId == 'P9M5KwTkta#840157_113997366104686638' || node.nodeId == 'ftQ9I9Lja9#10902_113997366104686638') {
    //   container.classed('topo-flash-node', true);
    // }
    if (node.metaData.type === "TransStation") {
      container.attr("viewBox", "0 0 595.3 841.9");
      let pp = new TransformerSubstation(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
        svgData: this.svgData,
        scale: this.scaleByExport,
        isSmall: this.isSmall,
      });
      this.transStationMap[node.nodeId] = pp;
    } else if (node.metaData.type === "PowerStation") {
      container.attr("viewBox", "0 0 209 209");
      let pp = new PowerPlant(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
        scale: this.scaleByExport,
        isSmall: this.isSmall,
      });
      this.powerPlantMap[node.nodeId] = pp;
    } else {
      if (
        (node.metaData.volt == "_500KV" || node.metaData.volt == "_220KV") &&
        (this.nodeType == "l" || this.nodeType == "m")
      ) {
        container.attr("viewBox", "0 0 595.3 841.9");
      } else {
        container.attr("viewBox", "0 0 246 246");
      }
      let pp = new ExchStation(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
        svgData: this.svgData,
        scale: this.scaleByExport,
        isSmall: this.isSmall,
      });
      this.exchStationMap[node.nodeId] = pp;
    }
    return container;
  }
  //   更新颜色
  _updateColor(data) {
    // console.log('coloData',data)

    if (this.nodeType === "m" || this.nodeType === "l") {
      this.topoData.nodes.forEach((node) => {
        if (node.metaData) {
          let nodeMeta = node.metaData;
          if (!data[node.nodeId]) {
            return;
          }
          switch (nodeMeta.type) {
            case "TransStation":
              if (this.transStationMap.hasOwnProperty(node.nodeId)) {
                // if(node.nodeId == "wk9BoYpcog#11018_113997366104686638"){
                //     debugger
                // }
                this.transStationMap[node.nodeId].updateColor(
                  data[node.nodeId]
                );
              }
              break;
            default:
              break;
          }
        }
      });
    }
    this.topoData.links.forEach((link) => {
      if (link.metaData) {
        let nodeMeta = link.metaData;
        if (!data[link.linkId]) {
          return;
        }
        switch (nodeMeta.type) {
          case "AcLine":
            this._updateLinkColor(link.linkId, data[link.linkId]);
            break;
          default:
            break;
        }
      }
    });
  }

  /**
   * 更新拓扑图数据
   */
  _update(data) {
    if (this.nodeType === "m" || this.nodeType === "l") {
      this.topoData.nodes.forEach((node) => {
        if (node.metaData) {
          let nodeMeta = node.metaData;
          if (!data[node.nodeId]) {
            return;
          }
          switch (nodeMeta.type) {
            case "DText":
              // if(data[node.nodeId]){
              this._updateText(node.nodeId, data[node.nodeId]);
              // }
              break;
            case "TransStation":
              if (this.transStationMap[node.nodeId]) {
                this.transStationMap[node.nodeId].update(data[node.nodeId]);
              }
              break;
            case "PowerStation":
              // 非火电厂不更新数据
              if (
                !this.powerPlantMap[node.nodeId] ||
                nodeMeta.num === 0 ||
                (nodeMeta.powerType !== "THERMAL" &&
                  nodeMeta.powerType !== "PUMP")
              )
                break;
              this.powerPlantMap[node.nodeId].update(data[node.nodeId]);
              break;
            case "ExchStation":
              if (this.exchStationMap[node.nodeId]) {
                this.exchStationMap[node.nodeId].update(data[node.nodeId]);
              }
              break;
            case "AreaBoard": //!this.showSublayer.includes("KS5TXwwEDE") ||
              if (!this.showSublayer.includes("SDAfqbRLOiv")) break;
              this.areaBoardMap[node.nodeId] &&
                this.areaBoardMap[node.nodeId].update(data[node.nodeId]);
              break;
            default:
              break;
          }
        }
      });
    }
    this.topoData.links.forEach((link) => {
      if (link.metaData) {
        let nodeMeta = link.metaData;
        if (!data[link.linkId]) {
          return;
        }
        switch (nodeMeta.type) {
          case "AcLine":
            let vol = "500";
            if (
              link.sublayerList &&
              link.sublayerList.map((d) => d.sublayerId).includes("KS3tPfwEKM")
            ) {
              vol = "220";
            }

            this._updateAcLine(link, data[link.linkId], vol);
            break;
          default:
            break;
        }
      }
    });
  }

  /**
   * 更新文字组件
   */
  _updateText(id, data) {
    requestAnimationFrame(() => {
      this.nodeCon
        .select(`#node_${id.replace("#", "_")}`)
        // .classed("topo-flash", data && data.flash === "1")
        .text(data.value);
    });
  }

  /**
   * 更新链路运动方向
   */
  _updateAcLine(link, data, vol) {
    //data:{ "isDivide": "0","direction": "1","flash": "0","status": "0"}
    //direction字典: -1 反向 1 正向 0无箭头
    //status字典: -1 灰 0 蓝 1红 2 紫 3 玫红 4 橙 5 黄 6 虚
    let lineColorDic = {
      "-1": "#e800ff",
      0: "#2cebff",
      1: "#a5a9b6",
      2: "#dac4a0",
      3: "#e71864",
      4: "#ff7800",
      5: "#e4ff00",
      6: "#00b4ff",
    };

    // let linkLen = this.linkCon
    //   .select(`#topoLink_${link.linkId.replace("#", "_")}`)
    //   .node()
    //   ?.getTotalLength();
    let linkLen = 100;
    if (
      !this.linkCon.select(`#topoLink_${link.linkId.replace("#", "_")}`).empty()
    ) {
      linkLen = this.linkCon
        .select(`#topoLink_${link.linkId.replace("#", "_")}`)
        .node()
        .getTotalLength();
    }
    // console.log(linkLen)
    // let arrPos =   this.linkCon.select(`#topoLink_${nodeId.replace('#', '_')}`).node()?.getPointAtLength(linkLen*0.5)

    let linkNode = this.linkCon.select(
      `#topoLink_${link.linkId.replace("#", "_")}`
    );

    let arrowGroup = this.arrowCon.select(
      `#topoArrow_${link.linkId.replace("#", "_")}`
    );
    arrowGroup.selectAll("polygon").remove();

    // 根据线路状态绘制箭头 status :5 :无箭头, 其他：多箭头;  flash:1: 多箭头,
    let num =
      data.flash === "1" ? Math.ceil(linkLen / 40) : Math.ceil(linkLen / 80);
    if (data.status === "5") {
      num = 0;
    }
    if (data.status === "6") {
      num = 1;
    }
    if (link.metaData.volt == "_220KV") {
      num = 1;
    }
    for (let i = 0; i < num; i++) {
      this._drawExternalCompOnLink(link, linkNode, data, num, i);
    }

    // this.arrowCon
    //     .select(`#topoArrow_${nodeId.replace('#', '_')}`)
    //     .style('display', vol == '500' ? (data.direction === '0' ? 'none' : 'block') : (data.status === '6' ? 'block' : 'none'))
    //     .style('animation-direction', data.status === '6' ? 'normal' : (data.direction === '1' ? 'normal' : 'reverse'))
    //     .style('transform', `rotate(${data.direction === '1' ? 0 : 180}deg)`)
    //     .attr('fill', vol == '220' ? '#2cebff' : '#e70742')

    // if (data.status === '6') {
    //     //   console.log(arrPos?.x)
    //     this.arrowCon
    //         .select(`#topoArrow_${nodeId.replace('#', '_')}`)
    //         // .style('animation','null' )
    //         .style('animation', `halfArrowMove 0s linear forwards`)
    // }

    this.linkCon
      .select(`#topoLink_${link.linkId.replace("#", "_")}`)
      .classed("topo-isDivide", data.isDivide === "1")
      //   .classed("topo-flash", data.flash === "1")
      .attr("stroke-dasharray", data.status === "6" ? "20 20" : "none");
    if (data.status != "6") {
      this.linkCon
        .select(`#topoLink_${link.linkId.replace("#", "_")}`)
        .style("stroke", lineColorDic[data.status]);
    }

    if (data.isDivide === "1") {
      data.nodes &&
        data.nodes.forEach((id) => {
          this.nodeCon
            .select(`#node_${id.replace("#", "_")}`)
            .classed("topo-isDivide", true);
        });
    }
  }

  _updateLinkColor(linkId, data) {
    //data:{ "status": "0"}
    //status字典: -1 灰 0 蓝 1红 2 紫 3 玫红 4 橙 5 黄 6 虚
    let lineColorDic = {
      "-1": "#e800ff",
      0: "#2cebff",
      1: "#a5a9b6",
      2: "#dac4a0",
      3: "#e71864",
      4: "#ff7800",
      5: "#e4ff00",
      6: "#00b4ff",
    };
    if (data.status != "0") {
      this.linkCon
        .select(`#topoLink_${linkId.replace("#", "_")}`)
        .style("stroke", lineColorDic[data.status]);
    }
  }
}
let isSmall = false;
function getStationId(rtKeyId) {
  if (!rtKeyId) return;
  if (isSmall) return rtKeyId;
  let stationId = rtKeyId.replace(":", "").slice(0, -4);

  if (stationId.slice(0, 1) === "0") {
    stationId = 0 + stationId;
  }
  return stationId;
}

/**
 * 链路箭头
 */
class NariTopoArrow {
  constructor(id, code, con, workMode, option, link, linkDom, data, num, inx) {
    this.id = id;
    this.con = con;
    // this.option = option;
    this.link = link;
    this.linkDom = linkDom;
    this.data = data; //接口数据
    this.num = num;
    this.inx = inx;
    this._foldPath = WisUtil.scriptPath("NariTopoView");

    this.style = JSON.parse(link.linkStyles);
    this._draw();
  }

  // 箭头宽高30*30
  _draw() {
    let w =
      this.link.metaData.volt == "_500KV"
        ? 8
        : this.link.metaData.volt == "_1000KV"
        ? 10
        : 15;
    let h =
      this.link.metaData.volt == "_500KV"
        ? 8
        : this.link.metaData.volt == "_1000KV"
        ? 10
        : 15;
    let arrowArr = [
      [15, 0],
      [5, -4],
      [5, -2],
      [7, 0],
      // [0, 2],
      [5, 2],
      [5, 4],
    ];

    // let arrowArr = [
    //     [10, 0],
    //     [7, 7],
    //     [5, 5],
    //     [7, 0],
    //     [-5, -5],
    //     [-7, -7],
    //     [10, 0]
    // ];
    arrowArr.forEach((d) => {
      d[0] = (d[0] * w) / 10;
      d[1] = (d[1] * h) / 10;
      d = d.join(",");
    });
    // let pathLength = this.linkDom.node()?.getTotalLength();
    if (!this.linkDom.empty()) {
      let pathLength = this.linkDom.node().getTotalLength();
    }
    // console.log(pathLength/75)
    this.con
      .append("polygon")
      // .attr('id', `topoArrow_${this.id.replace('#', '_')}`)
      .attr("points", arrowArr.join(" "))
      .style(
        "display",
        "block"
        // this.style.volt == "500" || this.style.volt == "1000"
        //   ? "block"
        //   : this.data.status === "6"
        //   ? "block"
        //   : "none"
      )
      .attr(
        "class",
        this.link.sublayerList
          ? this.link.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      // .style('fill', 'url(#arrow-fill-white)')
      // .style('transform', 'translateZ(0)')
      // .style('will-change','transform')
      .style("transform", `rotate(${this.data.direction === "1" ? 0 : 180}deg)`)
      .style(
        "fill",
        this.link.metaData.volt === "_500KV" ||
          this.link.metaData.volt === "_1000KV"
          ? "#ffffff"
          : "#2cebff"
      )
      .style("filter", "url(#arrow_shadow)")
      .style("offset-path", `path("${this.link.linkPath}")`)

      .style("offset-distance", `${(100 / this.num) * (this.inx + 1)}%`)
      //   .style(
      //     "animation",
      //     `arrowMove ${(pathLength / 20) * 1}s linear ${
      //       this.inx * (((pathLength / 20) * 1000) / this.num)
      //     }ms infinite`
      //   )
      .style(
        "animation-direction",
        this.data.status === "6"
          ? "normal"
          : this.data.direction === "1"
          ? "normal"
          : "reverse"
      );

    if (this.data.status === "6" || this.link.metaData.volt == "_220KV") {
      //   console.log(arrPos?.x)
      this.con
        .selectAll("polygon")

        // .style('animation','null' )
        .style("animation", `halfArrowMove 0s linear forwards`);
    }

    // w *= 4;
    // h *= 4;
    // this.con
    //   .append('image')
    //   .attr('id', `topoArrow_${this.id.replace('#', '_')}`)
    //   .attr('class', this.link.sublayerList ? this.link.sublayerList.map((d) => d.sublayerId).join(' ') : '')
    //   .attr('width', w)
    //   .attr('height', h)
    //   .attr('x', -w / 2)
    //   .attr('y', -h / 2)
    //   .attr('xlink:href', `${this._foldPath}/images/point.svg`)
    //   .style('offset-path', `path("${this.link.linkPath}")`)
    //   .style('animation', `arrowMove ${(pathLength / 100) * 1}s linear infinite`);
  }
}

/**
 * 变电站节点
 */
class TransformerSubstation {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    // if (this.option.data.volt === '_500KV') {
    //   this.con.attr('x', this.option.x - this.option.w / 2);
    //   this.con.attr('y', this.option.y - this.option.h / 2);
    //   this.con.attr('width', this.option.w * 2);
    //   this.con.attr('height', this.option.h * 2);
    // }

    this._getSwitchIndex();

    let _size = this.option.w * scaleOffset;
    if (this.option.data.volt === "_110KV") {
      _size = this.option.w * scaleOffset * 1.3;
    }
    this._setSize(_size);

    // if (this.option.data.volt === "_500KV") {
    //   this._setSize(this.option.type === "m" ? 100 : 160);
    // }

    // this._setSize(this.option.w);
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_220KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246 / this.option.scale)
        .attr("height", 246 / this.option.scale)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_500_${
            Number(this.option.data["xfmr_num"]) >= 3
              ? 3
              : this.option.data["xfmr_num"]
          }.svg`
        );
      return;
    }
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_500KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246 / this.option.scale)
        .attr("height", 246 / this.option.scale)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_1000_${
            Number(this.option.data["xfmr_num"]) >= 3
              ? 3
              : this.option.data["xfmr_num"]
          }.svg`
        );
      return;
    }
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_110KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246 / this.option.scale)
        .attr("height", 246 / this.option.scale)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_220.svg`
        );
      return;
    }
    this.defs = this.con.append("defs");
    // this.bgImage = this.con
    //   .append("image")
    //   .attr("width", 595.3)
    //   .attr("height", 841.9)
    //   .attr("transform", "scale(1.5)")
    //   .attr("transform-origin", "center");
    // if (
    //     (this.option.type === "l" || this.option.type === "m") &&
    //     this.option.data.volt === "_110KV"
    // ) {
    //     this.con
    //         .append("circle")
    //         .attr("class", "bg_cir")
    //         .attr("cx", 123)
    //         .attr("cy", 123)
    //         .attr("r", this.option.data["highbus_num"] === "0" ? 105 : 123)
    //         .style("fill", "#231815");
    // } else {
    this.con
      .append("circle")
      .attr("class", "bg_cir")
      .attr("cx", 297.2)
      .attr("cy", 421.9)
      .attr("r", this.option.data["highbus_num"] === "0" ? 210 : 298)
      .style("fill", "#231815");
    // }

    if (
      Number(this.option.data["switch_num"]) >
      Number(this.option.data["lowbus_num"])
    ) {
      this.option.data["switch_num"] = this.option.data["lowbus_num"];
    }
    this._drawHighBus(this.option.data["highbus_num"]);
    this._drawLowBus(
      this.option.data["lowbus_num"],
      this.option.data["switch_num"]
    );
    this._drawSwitch(
      this.option.data["lowbus_num"],
      this.option.data["switch_num"]
    );
    this._drawXfmr(this.option.data["xfmr_num"]);
  }

  _setSize(l) {
    this.con
      .attr("x", this.option.x - (l - this.option.w) / 2)
      .attr("y", this.option.y - (l - this.option.h) / 2)
      .attr("width", l)
      .attr("height", l);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }

  _getSwitchIndex() {
    let switchNo = this.option.data["switchNo"].split(",");
    let switchArray = switchNo.map((d) => d.split("_"));
    this.switchIndex = [];
    switch (this.option.data["switch_num"]) {
      case "4":
        this.switchIndex = switchArray.map((d) => {
          if (d[1] === "3" && d[2] === "4") {
            return 1;
          } else if (d[1] === "1" && d[2] === "3") {
            return 2;
          } else if (d[1] === "2" && d[2] === "4") {
            return 3;
          } else if (d[1] === "1" && d[2] === "2") {
            return 4;
          }
        });
    }
  }

  _getFillColor() {
    return colorOptions[this.option.data.volt] || "#4bbefc";
  }

  _getLowbusColor() {
    const _colorOptions = {
      _110KV: "#4bbefc",
      _220KV: "#4bbefc",
      _500KV: "#f70202",
    };

    return _colorOptions[this.option.data.volt] || "#4bbefc";
  }

  _drawXfmr(num) {
    if (!this.option.svgData.xfmr.hasOwnProperty(num)) {
      console.warn(`无主变数量为${num}的svg数据`);
      return;
    }
    let xfmrList = this.option.data["xfmrNo"].split(",") || [];
    let g = this.con.append("g").attr("class", "xfmr");
    const xfmrNos = this.option.data["xfmrNo"].split(",");

    // if (this.option.data.volt === "_110KV") {
    //     this.option.svgData.xfmr[num].forEach((d) => {
    //         let con = g
    //             .append("g")
    //             .attr("transform", "scale(0.8)")
    //             .attr("transform-origin", "center");
    //         con
    //             .append("path")
    //             .attr("class", "topo_xfmr_inset")
    //             .attr("d", d)
    //             .style("filter", "url(#inset-shadow-blue)")
    //             .style("fill", "#0f4372")
    //             .style("stroke-width", 1)
    //             .style("stroke", "#2cebff");
    //     });
    // } else {

    // 更新后的图元
    this.option.svgData.xfmr550[num].forEach((d, i) => {
      const rtKeyId = this.option.data[`rtKeyId${xfmrNos[i]}`];

      // 主变容量
      let xfMrMvarate = this.option.data[`xfmrMvarate${xfmrList[i]}`] || "";
      let con = g
        .append("g")
        .attr("keyid", getStationId(rtKeyId))
        .attr("class", "device-detail")
        .attr("data-type", "Xfmr");
      let volt = this.option.data.volt;

      con.append("path").attr("class", "topo_xfmr_inset").attr("d", d);
      // TODO:500和1000按照容量，220和110按照电压区分颜色
      con.style("fill", this._getFillColor());
    });
    // }
  }

  _genXfmrRate(k) {
    if (k >= 1) return "M 33 123 a 90 90 0 1 0 180 0 a 90 90 0 1 0 -180 0 z";
    let cx = 123;
    let cy = 123;
    let r = 90;
    let x0 = cx - 2 * r * Math.sqrt(k - k * k);
    let y0 = cy + (r - 2 * k * r);
    let x1 = cx + 2 * r * Math.sqrt(k - k * k);
    let y1 = cy + (r - 2 * k * r);
    return `M ${x0} ${y0} A ${r} ${r} 0 ${k < 0.5 ? 0 : 1} 0 ${x1} ${y1} Z`;
  }

  _drawLowBus(lowNum, switchNum) {
    // if (!this.option.svgData.lowbus.hasOwnProperty(`${lowNum}-${switchNum}`)) {
    //   console.warn(`无低压数量为${lowNum}且开关数量为${switchNum}的svg数据`);
    //   return;
    // }
    let g = this.con.append("g").attr("class", "lowbus");
    const lowBusNos = this.option.data["lowBusNo"]?.split(",");

    // if (this.option.data.volt === "_110KV") {
    //     this.option.svgData.lowbus[`${lowNum}-${switchNum}`].forEach((d) => {
    //         let con = g.append("g");
    //         con
    //             .append("path")
    //             .attr("class", "TransformerSubstation")
    //             .attr("d", d)
    //             .style("filter", "url(#shadow-xfmr)")
    //             .style("fill", "#0088ff");
    //         con
    //             .append("path")
    //             .attr("class", "TransformerSubstation-inset")
    //             .attr("d", d)
    //             .style("filter", "url(#inset-shadow-blue)")
    //             .style("fill", "#2d99fc")
    //             .style("stroke-width", 1)
    //             .style("stroke", "#00e7ff");
    //     });
    // } else {
    this.option.svgData?.lowbus550[`${lowNum}`]?.forEach((d, i) => {
      const rtKeyId = lowBusNos
        ? this.option.data[`rtKeyId${lowBusNos[i]}`]
        : "";

      let con = g
        .append("g")
        .attr("keyid", getStationId(rtKeyId))
        .attr("class", "device-detail")
        .attr("data-type", "LowBus");

      con
        .append("path")
        .attr("class", "TransformerSubstation-inset")
        .attr("d", d)
        // .style("fill", "url(#silver_Gradient)");
        .style("fill", this._getLowbusColor());
    });
    // }
  }

  _drawSwitch(lowNum, switchNum) {
    // if (!this.option.svgData.switch.hasOwnProperty(`${lowNum}-${switchNum}`)) {
    //   console.warn(`无低压数量为${lowNum}且开关数量为${switchNum}的svg数据`);
    //   return;
    // }
    let g = this.con.append("g").attr("class", "switch");
    const switchNos = this.option.data["switchNo"]
      .split(",")
      .map((ele) => ele.split("_")[0]);
    // if (this.option.data.volt === "_110KV") {
    //     this.option.svgData.switch[`${lowNum}-${switchNum}`].forEach((d) => {
    //         let [x, y, w, h, rx, tf] = d.split(",");
    //         let con = g.append("g");
    //         con
    //             .append("rect")
    //             .attr("class", "TransformerSwitch")
    //             .attr("x", x)
    //             .attr("y", y)
    //             .attr("width", w)
    //             .attr("height", h)
    //             .attr("rx", rx)
    //             .attr("transform", tf === "null" ? "" : tf)
    //             .style("fill", "#0088ff");
    //     });
    // } else {
    //用一个圆遮挡住开关
    this.con
      .append("circle")
      .attr("cx", 297.2)
      .attr("cy", 421.9)
      .attr("r", 150)
      .style("fill", "#231815");

    //筛选出低压母线对应的开关map
    let switchMap = this.option.svgData.switchObj[lowNum];
    //   获得开关位置列表
    let switchList = [];
    //   if (lowNum == "2") {
    switchList = this.option.data["switchNo"].split(",").map((ele, i) => {
      return lowNum == "2"
        ? ele.split("_")[1] + "_" + ele.split("_")[2] + "_" + i
        : ele.split("_")[1] + "_" + ele.split("_")[2];
    });

    if (this.id === "KxhCEJPSOj") {
      console.log(this.option, lowNum, switchMap, switchList);
    }

    switchList.forEach((d, index) => {
      const rtKeyId = this.option.data[`rtKeyId${switchNos[index]}`];

      let con = g
        .append("g")
        .attr("keyid", rtKeyId)
        .attr("class", "device-detail")
        .attr("data-type", "Switch");
      try {
        con
          .append("path")
          .attr("class", "TransformerSwitch")
          .attr("d", switchMap[d])
          //   .style("filter", "url(#inset-shadow-blue)")
          .style("stroke-width", 2)
          .style("stroke", "black")
          .style("fill", "#daa520");
      } catch (error) {
        console.log(d, this.id, switchMap);
        // debugger
      }
    });

    // }
  }

  _drawHighBus(num) {
    if (num === "0") return;
    if (!this.option.svgData.highbus.hasOwnProperty(num)) {
      console.warn(`无高压数量为${num}的svg数据`);
      return;
    }
    let g = this.con.append("g").attr("class", "highbus");
    const highBusNos = this.option.data["highBusNo"].split(",");

    // if (this.option.data.volt === "_110KV") {
    //     this.option.svgData.highbus[num].forEach((d) => {
    //         let con = g.append("g");
    //         con
    //             .append("path")
    //             .attr("class", "TransformerSubstation")
    //             .attr("d", d)
    //             .style("filter", "url(#shadow-xfmr)")
    //             .style("fill", "#ff0083");
    //         con
    //             .append("path")
    //             .attr("class", "TransformerSubstation-inset")
    //             .attr("d", d)
    //             .style("filter", "url(#inset-shadow-red)")
    //             .style("fill", "#fc0046")
    //             .style("stroke-width", 1)
    //             .style("stroke", "#e1b5bd");
    //     });
    // } else {
    this.option.svgData.highbus550[num].forEach((d, index) => {
      const rtKeyId = this.option.data[`rtKeyId${highBusNos[index]}`];

      let con = g
        .append("g")
        .attr("keyid", getStationId(rtKeyId))
        .attr("class", "device-detail")
        .attr("data-type", "HighBus");
      con
        .append("path")
        .attr("class", "TransformerSubstation-inset")
        .attr("d", d)
        // .style("fill", "url(#silver_Gradient)");
        .style("fill", this._getFillColor());
    });
    // }
  }

  update(data) {
    let nodeMeta = this.option.data;
    let busColorDic = {
      0: "blue", //蓝色
      1: "silver",
      2: "golden",
      3: "roseo",
      4: "orange",
      5: "yellow",
    };
    let xfmrColorDic = {
      "-1": { color: "gary", filter: "gary", insetColor: "", stroke: "" },
      0: {
        color: "#0088ff",
        filter: "blue",
        insetColor: "#0f4372",
        stroke: "#2cebff",
      },
      1: {
        color: "#ff2600",
        filter: "red",
        insetColor: "#72180f",
        stroke: "#ff3f2c",
      },
      2: {
        color: "#ff009c",
        filter: "purple",
        insetColor: "#720f4e",
        stroke: "#ff2cb5",
      },
      3: { color: "gary", filter: "roseo", insetColor: "", stroke: "" },
      4: { color: "gary", filter: "orange", insetColor: "", stroke: "" },
      5: { color: "#e4ff00", filter: "yellow", insetColor: "", stroke: "" },
    };
    let waterColorDic = {
      0: "#226d00", //绿
      1: "#a2451f", //黄
      2: "#7f1b2c", //红
    };
    // let xfmr500 = {

    // };
    //变电站闪烁
    // this.con.select(".xfmr").classed("topo-flash", data.rate >= 1);
    //越限外发光
    const hasFlash = data.xfmr.some((item) => item.flash === "1");

    this.con.classed("redTransFilter", hasFlash);
    // if (hasFlash) {
    //   this.bgImage.attr("xlink:href", `${this._foldPath}/images/redBorder.png`);
    // } else {
    //   this.bgImage.attr("xlink:href", null);
    // }
    //更新主变数据
    // if (nodeMeta.xfmr_num !== "0" && nodeMeta.volt !== "_110KV") {

    [...this.con.select(".xfmr").selectAll("g").nodes()].forEach((p, i) => {
      if (data.xfmr[i]?.status == "5") {
        d3.select(p)
          .selectAll(".topo_xfmr_inset")
          .style(
            "fill",
            `url(#${busColorDic[data.xfmr[i]?.status]}_xfmr_Gradient)`
          );
      } else {
        d3.select(p).selectAll(".topo_xfmr_inset").style("fill", null);
      }
    });
    // } else {
    //     [...this.con.select(".xfmr").selectAll("g").nodes()].forEach((p, i) => {
    //         let color = xfmrColorDic[data.xfmr[i]?.status];
    //         d3.select(p).selectAll(".topo_xfmr").style("fill", color?.color);
    //         d3.select(p)
    //             .selectAll(".topo_xfmr_inset")
    //             .style(
    //                 "filter",
    //                 `url(#inset-shadow-${xfmrColorDic[data.xfmr[i]?.status]?.filter})`
    //             )
    //             .style("fill", color?.insetColor)
    //             .style("stroke", color?.stroke);
    //     });
    // }
    //更新低压母线数据
    if (nodeMeta.lowBusNo !== "") {
      [...this.con.select(".lowbus").selectAll("g").nodes()].forEach((p, i) => {
        // if (nodeMeta.volt === "_110KV") {
        //     d3.select(p)
        //         .selectAll(".TransformerSubstation-inset")
        //         .style(
        //             "filter",
        //             `url(#inset-shadow-${busColorDic[data.lowBus[i]?.status]})`
        //         );
        // } else {
        if (!data.lowBus[i]?.status) {
          d3.select(p)
            .selectAll(".TransformerSubstation-inset")
            .style("fill", `url(#silver_Gradient)`);
        } else {
          d3.select(p)
            .selectAll(".TransformerSubstation-inset")
            .style(
              "fill",
              `url(#${busColorDic[data.lowBus[i]?.status]}_Gradient)`
            );
        }
        // }
      });
    }
    //更新高压母线数据
    if (nodeMeta.highBusNo !== "") {
      [...this.con.select(".highbus").selectAll("g").nodes()].forEach(
        (p, i) => {
          // if (nodeMeta.volt === "_110KV") {
          //     d3.select(p)
          //         .selectAll(".TransformerSubstation-inset")
          //         .style(
          //             "filter",
          //             `url(#inset-shadow-${busColorDic[data.highBus[i]?.status]})`
          //         );
          // } else {
          if (!data.highBus[i]?.status) {
            d3.select(p)
              .selectAll(".TransformerSubstation-inset")
              .style("fill", `url(#silver_Gradient)`);
          } else {
            d3.select(p)
              .selectAll(".TransformerSubstation-inset")
              .style(
                "fill",
                `url(#${busColorDic[data.highBus[i]?.status]}_Gradient)`
              );
          }
          // }
        }
      );
    }
    //更新开关数据
    if (nodeMeta.switchNo !== "") {
      let switchArray = [...this.con.select(".switch").selectAll("g").nodes()];

      switchArray.forEach((p, i) => {
        d3.select(p)
          .selectAll(".TransformerSwitch")
          .style("stroke", data.switch[i]?.status === "0" ? "black" : "#FF0000")
          .style("fill", data.switch[i]?.status === "0" ? "#daa520" : null);

        if (this.option.data.volt === "_220KV") {
          d3.select(p)
            .selectAll(".TransformerSwitch")

            // .style('fill', data.switch[i]?.status === '0' ? "url(#blue_switch_Gradient)" : '#19eb42');
            .style("stroke-width", data.switch[i]?.status === "0" ? 2 : 10)
            .style(
              "stroke",
              data.switch[i]?.status === "0" ? "black" : "#daa520"
            );
        }
      });
    }
  }
}

/**
 * 电厂节点
 */
class PowerPlant {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this.data = [];
    this.option.data.no &&
      this.option.data.no.split(",").forEach((d) => {
        this.data.push({
          mvarate: 100,
          value: 0,
          name: this.option.data[`keyDesc${d}`],
          no: d,
          status: "0",
        });
      });
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this.fontSizeDic = [55, 50, 45, 40, 35, 30, 25, 20];
    this.colorMap = {
      0: { fillColor: "blue", endColor: "#77f9ff" },
      1: { fillColor: "green", endColor: "#00ff00" },
    };
    this._draw();
  }

  _draw() {
    let _size = 31;
    if (this.option.type === "l") {
      _size = 91;
    } else if (this.option.data.volt === "_220KV") {
      _size = 45;
    }
    this._setSize(_size);
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.powerType === "THERMAL"
    ) {
      this.con
        .append("image")
        .attr("width", 209)
        .attr("height", 209)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/powerPlant_${this.option.type}${this.option.data.volt}.svg`
        );
      return;
    }
    if (this.option.type === "p" || this.option.type === "s") {
      this.con
        .append("image")
        .attr("width", 209)
        .attr("height", 209)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/powerPlant_${this.option.type}_${this.option.data.powerType}.svg`
        );
      return;
    }
    if (
      this.option.data.powerType !== "THERMAL" &&
      this.option.data.powerType !== "PUMP"
    ) {
      this.con
        .append("use")
        .attr("width", 209)
        .attr("height", 209)
        .attr("xlink:href", `#powerPlant_${this.option.data.powerType}`);
      // .attr(
      //   "xlink:href",
      //   `${this._foldPath}/images/powerPlant_${this.option.data.powerType}.svg`
      // );
      return;
    }
    this.con
      .append("use")
      .attr("width", 209)
      .attr("height", 209)
      .attr("xlink:href", `#powerPlant${this.option.data.volt}`);
    //   .attr(
    //     "xlink:href",
    //     `${this._foldPath}/images/powerPlant${this.option.data.volt}.svg`
    //   );
    if (
      this.option.type === "s" ||
      (this.option.data.powerType !== "THERMAL" &&
        this.option.data.powerType !== "PUMP")
    ) {
      return;
    }

    this.drawRect = {
      x: 15,
      y: 17,
      width: 176,
      height: 176,
    };

    this.mainG = this.con
      .append("g")
      .attr("class", "container")
      .attr("transform", `translate(${this.drawRect.x}, ${this.drawRect.y})`);
    this.barNum = Number(this.option.data.num);
    this._genRange();
    this._drawBar();
    this._drawName();
    this._drawValue();
  }

  _setSize(l) {
    l = this.option.w;
    this.con
      .attr("x", this.option.x - (l - this.option.w) / 2)
      .attr("y", this.option.y - (l - this.option.h) / 2)
      .attr("width", l)
      .attr("height", l);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }

  _genRange() {
    this.xRange = [];
    this.data.forEach((d) => {
      this.xRange.push(
        d3
          .scaleLinear()
          .domain([0, Number(d.mvarate)])
          .range([this.drawRect.x, this.drawRect.width])
      );
    });
    this.yRange = d3
      .scaleBand()
      .domain(this.data.map((d, i) => i))
      .range([this.drawRect.y, this.drawRect.y + this.drawRect.height]);
  }

  _drawBar() {
    let barG = this.mainG.append("g").attr("class", "powerplant_bar");

    barG
      .selectAll(".bgRect")
      .data(this.data)
      .enter()
      .append("rect")
      .attr("class", "bgRect")
      .attr("x", 0)
      .attr("y", (d, i) => this.yRange.bandwidth() * i)
      .attr("width", this.drawRect.width)
      .attr("height", this.yRange.bandwidth())
      .style("fill", `url(#powerplant-bar-bg${this.option.data.volt})`);

    barG
      .selectAll(".rect")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("rect")
            .attr("class", "rect")
            .attr("keyid", (d) =>
              getStationId(this.option.data[`rtKeyId${d.no}`])
            )
            .attr("x", 0)
            .attr("y", (d, i) => this.yRange.bandwidth() * i)
            .attr("width", (d, i) => this.xRange[i](d.value))
            .attr("height", this.yRange.bandwidth())
            // .style("fill", `url(#powerplant-bar${this.option.data.volt})`),
            .style("fill", (d, i) => {
              if (d.status != "0") {
                return `url(#powerplant-bar_${
                  this.colorMap[d.status].fillColor
                })`;
              } else {
                return `url(#powerplant-bar${this.option.data.volt})`;
              }
            }),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .attr("y", (d, i) => this.yRange.bandwidth() * i)
            .attr("height", this.yRange.bandwidth())
            .attr("width", (d, i) => this.xRange[i](d.value))
            .style("fill", (d, i) => {
              if (d.status != "0") {
                return `url(#powerplant-bar_${
                  this.colorMap[d.status].fillColor
                })`;
              } else {
                return `url(#powerplant-bar${this.option.data.volt})`;
              }
            }),
        (exit) => exit.remove()
      );

    if (this.option.data.volt == "_220KV") {
      barG
        .selectAll(".endRect")
        .data(this.data)
        .join(
          (enter) =>
            enter
              .append("rect")
              .attr("class", "endRect")
              .attr("keyid", (d) =>
                getStationId(this.option.data[`rtKeyId${d.no}`])
              )
              .attr("x", (d, i) => this.xRange[i](d.value) - 2.5)
              .attr("y", (d, i) => this.yRange.bandwidth() * i)
              .attr("width", 5)
              .attr("height", this.yRange.bandwidth())
              .style("fill", "#f70202")
              .style("opacity", 0.8),
          (update) =>
            update
              .transition(d3.transition().duration(1000))
              .attr("x", (d, i) => this.xRange[i](d.value) - 2.5)
              .attr("y", (d, i) => this.yRange.bandwidth() * i)
              .attr("height", this.yRange.bandwidth())
              .style("fill", "#f70202"),

          (exit) => exit.remove()
        );
    }
  }

  _drawName() {
    // let pattern = /([0-9]{1,2})/;
    let pattern = /(#\d+|(?<!\d)\d+号)/;

    // let pattern2 = /([0-9]{1,2})号/;
    let nameG = this.mainG.append("g").attr("class", "powerplant_name");
    nameG
      .selectAll(".name")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("text")
            .attr("class", "name")
            .attr("keyid", (d) =>
              getStationId(this.option.data[`rtKeyId${d.no}`])
            )
            .attr("x", 10)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .style("fill", "#fff")
            .style("font-size", `${50 - this.data.length * 5}px`)
            .style("dominant-baseline", "central")
            .text((d, i) => {
              if (!d.name) return;
              let n = d.name.match(pattern);
              if (!n) return "";
              if (n[0].indexOf("号") != "-1") {
                return `#${n[0].slice(0, -1)}`;
              } else {
                return n[0];
              }
            }),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .style("font-size", `${50 - this.data.length * 5}px`)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .attr("width", (d, i) => this.xRange[i](d.value))
            .text((d, i) => {
              if (!d.name) return;
              let n = d.name.match(pattern);
              if (n[0].indexOf("号") != "-1") {
                return `#${n[0].slice(0, -1)}`;
              } else {
                return n[0];
              }
            }),
        (exit) => exit.remove()
      );
  }

  _drawValue() {
    let valueG = this.mainG.append("g").attr("class", "powerplant_value");
    valueG
      .selectAll(".value")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("text")
            .attr("class", "value")
            .attr("keyid", (d) =>
              getStationId(this.option.data[`rtKeyId${d.no}`])
            )
            .attr("x", this.drawRect.width)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .style("fill", "#fff")
            .style("font-size", `${50 - this.data.length * 5}px`)
            .style("dominant-baseline", "central")
            .style("text-anchor", "end")
            .text((d) => `${Math.round(d.value)}`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .style("font-size", `${50 - this.data.length * 5}px`)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .text((d) => `${Math.round(d.value)}`),
        (exit) => exit.remove()
      );
  }

  _drawMask() {
    let barG = this.mainG.append("g");

    barG
      .selectAll(".device-detail")
      .data(this.data)
      .enter()
      .append("rect")
      .attr("class", "device-detail")
      .attr("keyid", (d) => getStationId(this.option.data[`rtKeyId${d.no}`]))
      .attr("data-type", "PowerPlant")
      .attr("x", 0)
      .attr("y", (d, i) => this.yRange.bandwidth() * i)
      .attr("width", this.drawRect.width)
      .attr("height", this.yRange.bandwidth())
      .style("fill", "transparent");
  }

  update(data) {
    if (data === undefined) return;
    this.data = data.machineList.map((i, index) => {
      return {
        mvarate: i.mvarate,
        value: i.value,
        name: this.data[index] && this.data[index].name,
        no: this.data[index] && this.data[index].no,
        status: i.status || "0",
      };
    });
    if (this.mainG === undefined) return;
    requestAnimationFrame(() => {
      $(this.mainG.node()).empty();
      this._genRange();
      this._drawBar();
      this._drawName();
      this._drawValue();
      this._drawMask();
    });
  }
}

/**
 * 换流站组件
 */
class ExchStation {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    if (this.option.type === "m") {
      this._setSize(60);
    } else {
      if (this.option.data.volt == "_1000KV") {
        this._setSize(148);
      } else {
        this._setSize(148);
      }
    }
    if (this.option.type === "p" || this.option.type === "s") {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `#exchStation_${this.option.type}${this.option.data.volt}`
        );
    } else {
      this.con
        .append("circle")
        .attr("cx", 123)
        .attr("cy", 123)
        .attr("r", this.option.data["highbus_num"] === "0" ? 105 : 123)
        .style("fill", "#231815");

      this.con
        .append("use")
        .attr("width", 200)
        .attr("height", 200)
        .attr("xlink:href", `#exchStation_${this.option.type}`)
        .attr("x", 23)
        .attr("y", 23);
    }

    this._drawHighBus(this.option.data["highbus_num"]);
  }

  _drawHighBus(num) {
    if (num === "0") return;
    if (!this.option.svgData.highbus550.hasOwnProperty(num)) {
      console.warn(`无高压数量为${num}的svg数据`);
      return;
    }
    let g = this.con.append("g").attr("class", "highbus");

    this.option.svgData.highbus550[num].forEach((d) => {
      let con = g.append("g");

      con
        .append("path")
        .attr("class", "TransformerSubstation-inset")
        .attr("d", d)
        .style("fill", "url(#silver_Gradient)");
    });
    // }
  }

  update(data) {
    let nodeMeta = this.option.data;
    let busColorDic = {
      0: "blue", //蓝色
      1: "silver",
      2: "golden",
      3: "roseo",
      4: "orange",
      5: "yellow",
    };

    requestAnimationFrame(() => {
      //更新高压母线数据
      if (nodeMeta.highBusNo !== "") {
        [...this.con.select(".highbus").selectAll("g").nodes()].forEach(
          (p, i) => {
            if (!data.highBus[i].status) {
              d3.select(p)
                .selectAll(".TransformerSubstation-inset")
                .style("fill", `url(#silver_Gradient)`);
            } else {
              d3.select(p)
                .selectAll(".TransformerSubstation-inset")
                .style(
                  "fill",
                  `url(#${
                    data.highBus[i] && busColorDic[data.highBus[i].status]
                  }_Gradient)`
                );
            }
          }
        );
      }
    });
  }

  _setSize(l) {
    // l = l / this.option.scale;
    l = this.option.w * scaleOffset;

    this.con
      .attr("x", this.option.x - (l - this.option.w) / 2)
      .attr("y", this.option.y - (l - this.option.h) / 2)
      .attr("width", l)
      .attr("height", l);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }
}

/**
 * 分区板组件
 */
class AreaBoard {
  constructor(node, code, con, workMode, option) {
    this.id = node.nodeId;
    this.node = node;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    this.nameStr = this.option.property.name.length;
    let svg = this.con
      .append("svg")
      .attr("id", `area_board_${this.id}`)
      .attr(
        "keyid",
        this.node.metaData && getStationId(this.node.metaData.rtKeyId0)
      )
      .attr(
        "class",
        this.node.sublayerList
          ? this.node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr("x", this.option.property.basic.position.x)
      .attr("y", this.option.property.basic.position.y)
      .attr("width", this.option.property.basic.frame[2])
      .attr("height", this.option.property.basic.frame[3])
      .attr("viewBox", `0 0 ${this.nameStr === 3 ? 563 : 497} 120`);
    this.image = svg
      .append("use")
      .attr("width", this.nameStr === 3 ? 563 : 497)
      .attr("height", 120)
      .attr("xlink:href", `#areaBoard${this.nameStr}-0`);

    svg
      .append("text")
      .attr("class", "area-board-name")
      .style("transform", `translate(${this.nameStr === 3 ? 116 : 86}px, 48px)`)
      .text(this.option.property.name);
    this.text = svg
      .append("text")
      .attr("class", "area-board-value")
      .style(
        "transform",
        `translate(${this.nameStr === 3 ? 465 : 397}px, 50px)`
      )
      .text(this.option.property.value);
  }

  /**
   * 更新分区板数据
   * @param {Object} data 分区数据
   */
  update(data) {
    requestAnimationFrame(() => {
      this.text.text(data.value);
      this.image.attr("xlink:href", `#areaBoard${this.nameStr}-${data.status}`);
    });
  }
}
