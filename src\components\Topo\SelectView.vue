<template>
  <div
    v-show="isShow && !isSpaceDown"
    class="topo-select-view"
    id="topoSelectView"
    :style="getStyle"
  ></div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "Selectview",
  props: {
    scale: {
      type: Number,
      default: 1,
    },
    isSpaceDown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShow: false,
      isMove: false,
      el: null,
      selectStyle: {
        left: 0,
        top: 0,
        width: 0,
        height: 0,
      },
      startPoint: {
        x: 0,
        y: 0,
      },
    };
  },
  mounted() {
    this.initEvent();
  },
  beforeDestroy() {
    document.removeEventListener("mousedown", this.handleMousedown);
    document.removeEventListener("mousemove", this.handleMosuemove);
    document.removeEventListener("mouseup", this.handleMosueup);
  },
  computed: {
    ...mapState("topoStore", {
      viewBoxScale: "viewBoxScale",
    }),
    getStyle() {
      const style = {};
      for (const key in this.selectStyle) {
        style[key] = this.selectStyle[key] + "px";
      }
      return style;
    },
  },
  methods: {
    initEvent() {
      document.addEventListener("mousedown", this.handleMousedown);
      document.addEventListener("mousemove", this.handleMosuemove);
      document.addEventListener("mouseup", this.handleMosueup);
    },
    handleMousedown(e) {
      if (this.isSpaceDown) return;
      const { pageX, pageY } = e;
      //   鼠标在SVG内部开始选中
      if (e.target.id === "topoEdit") {
        this.selectStyle.left = pageX;
        this.selectStyle.top = pageY;
        this.startPoint = {
          x: pageX,
          y: pageY,
        };
        this.isShow = true;
      }
    },
    handleMosuemove(e) {
      if (!this.isShow || this.isSpaceDown) return;
      this.isMove = true;
      const { pageX, pageY } = e;
      const { x, y } = this.startPoint;
      if (pageX >= x && pageY >= y) {
        // 右下
        this.selectStyle.width = pageX - x;
        this.selectStyle.height = pageY - y;
      } else if (pageX >= x && pageY < y) {
        // 右上
        this.selectStyle.height = Math.abs(pageY - y);
        this.selectStyle.width = pageX - x;
        this.selectStyle.top = pageY;
      } else if (pageX < x && pageY >= y) {
        // 右上
        this.selectStyle.height = pageY - y;
        this.selectStyle.width = Math.abs(pageX - x);
        this.selectStyle.left = pageX;
      } else {
        // 左下
        this.selectStyle.width = Math.abs(pageX - x);
        this.selectStyle.height = Math.abs(pageY - y);
        this.selectStyle.top = pageY;
        this.selectStyle.left = pageX;
      }
    },
    handleMosueup(e) {
      this.getPositionBySvg();
      this.isShow = false;
      this.isMove = false;
      this.selectStyle = {
        left: 0,
        top: 0,
        width: 0,
        height: 0,
      };
    },
    // 获取勾选框在svg中的实际位置
    getPositionBySvg() {
      if (!this.isMove) return;
      const { x, y } = document
        .querySelector("#topoEdit")
        .getBoundingClientRect();
      let { left, top, width, height } = this.selectStyle;
      const svg = document.querySelector("#topoEdit");
      const formatNumber = (val) => {
        return Math.round(val / this.scale / this.viewBoxScale);
      };
      const x1 = formatNumber(left - x) + svg.viewBox.animVal.x;
      const y1 = formatNumber(top - y) + svg.viewBox.animVal.y;

      this.$emit("onSelect", {
        x: x1,
        y: y1,
        width: formatNumber(width),
        height: formatNumber(height),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.topo-select-view {
  position: fixed;
  border: 1px solid #00a8ff;
  background-color: rgba($color: #00a8ff, $alpha: 0.4);
  pointer-events: none; //表示不响应鼠标事件， 元素永远不会成为鼠标事件的target
}
</style>
