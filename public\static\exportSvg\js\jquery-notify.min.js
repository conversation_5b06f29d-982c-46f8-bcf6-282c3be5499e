﻿(function(n){function u(){this._items=[]}var i={getYPosition:function(t,i){if(t.container.attr("data-notify-adjust")!="scroll")return i.getYPosition(t.notifier);var r=i.getYPosition(t.notifier),u=r+n(document).scrollTop()-t.container.position().top;return u<0?r:u}},r={show:function(){var u=n(this),t=u.data("notify"),r,f;t&&(t.notifier.trigger("beforeshow",{element:this,settings:t.settings}),r=n.notify.queue[t.container.attr("data-notify-id")],r.add(t.notifier)?(f=i.getYPosition(t,r),t.notifier.position().top!=f&&t.notifier.animate({top:f},{queue:!1}),t.settings.widthAdjust&&(t.notifier.width(""),t.settings.widthAdjust=!1)):(t.notifier.show().animate({opacity:t.settings.opacity},t.settings.animationDuration,function(){n(this).trigger("aftershow",{element:u[0],settings:t.settings})}).css({top:i.getYPosition(t,r)}),t.settings.displayTime&&!t.settings.sticky&&setTimeout(function(){u.trigger("hide")},t.settings.displayTime)),t.notifier.outerWidth(!0)>n(document).width()&&(t.settings.widthAdjust=!0,t.notifier.outerWidth(n(window).width()-t.notifier.css("padding-left").replace("px",""))),t.container.attr("data-notify-adjust")=="content"&&r.isLast(t.notifier)&&t.container.animate({"padding-top":r.getHeight()},{queue:!1},t.settings.animationDuration))},hide:function(){var i=n(this),t=i.data("notify");t&&t.notifier.css("opacity")&&(t.notifier.trigger("beforehide",{element:this,settings:t.settings}),t.notifier.animate({opacity:0},t.settings.animationDuration,function(){var r=n(this);r.hide(),n.notify.queue[t.container.attr("data-notify-id")].remove(t.notifier),t.container.attr("data-notify-adjust")=="content"&&t.container.animate({"padding-top":n.notify.queue[t.container.attr("data-notify-id")].getHeight()},{queue:!1},t.settings.animationDuration),r.trigger("afterhide",{element:i[0],settings:t.settings})}))}},t={init:function(t){var f={animationDuration:500,displayTime:3e3,appendTo:"body",autoShow:!0,closeText:"x",sticky:!1,style:"bar",type:"info",adjustContent:!1,adjustScroll:!1,notifyClass:"",opacity:1,beforeShow:null,beforeHide:null,afterShow:null,afterHide:null},e=t&&t.type?t.type:f.type,i=n.extend({},f,n.notify.settings[e]);return i=n.extend(i,t),i.type=n.notify.settings[e]?e:f.type,n(this).each(function(){var f=n(this),h=f.data("notify"),e,t,o,s;h||(e=n("<div />",{"class":"notify "+i.style+" "+i.type+(i.notifyClass?" "+i.notifyClass:""),"data-notifier-id":(new Date).getTime(),css:{display:"none",opacity:0,position:"absolute"}}).bind({update:function(){f.trigger("show")},beforeshow:i.beforeShow,beforehide:i.beforeHide,aftershow:i.afterShow,afterhide:i.afterHide}).append(n("<span />",{"class":"icon"})).append(n("<span />",{"class":"close",text:i.closeText,click:function(){f.trigger("hide")}})),f.bind({show:r.show,hide:r.hide}),e.append(f),t=n(i.appendTo),t.attr("data-notify-id")||(o=(new Date).getTime(),n.notify.queue[o]=new u,t.attr("data-notify-id",o),s=t.css("position"),s=="static"&&t.css("position","relative"),i.adjustContent&&t.attr("data-notify-adjust","content"),i.adjustScroll&&t.attr("data-notify-adjust","scroll")),t.append(e),f.data("notify",{settings:i,notifier:e,container:t}),i.autoShow&&f.trigger("show"))})},destroy:function(){return this.each(function(){var i=n(this),t=i.data("notify");t&&t.notifier.remove()})},show:function(){return this.each(function(){var t=n(this).data("notify");t&&n(this).trigger("show")})},hide:function(){return this.each(function(){var t=n(this).data("notify");t&&n(this).trigger("hide")})}};n.fn.notify=function(i){var r=i;if(t[r])return t[r].apply(this,Array.prototype.slice.call(arguments,1));if(typeof r!="object"&&r)n.error("Method "+r+" does not exist on jQuery notify");else return t.init.apply(this,arguments)},n.extend(u.prototype,{add:function(t){var i=n.inArray(t,this._items)>-1;return i||this._items.push(t),i},remove:function(t){var i=n.inArray(t,this._items);this._items.splice(i,1),this.update(i)},update:function(n){for(var r=n||0,i,t=r;t<this._items.length;t++)i=this._items[t],i&&i.length&&i.trigger("update")},getYPosition:function(n){for(var r=0,i,t=0;t<this._items.length;t++){if(i=this._items[t],i==n)break;r+=i.outerHeight(!0)}return r},getHeight:function(){for(var t=0,n=0;n<this._items.length;n++)t+=this._items[n].outerHeight(!0);return t},isLast:function(t){return n.inArray(t,this._items)==this._items.length-1}}),n.notify={queue:{},settings:{info:{},success:{sticky:!0,type:"success"},error:{sticky:!0,type:"error"},warning:{sticky:!0,type:"warning"}},create:function(t,i){return n("<span />",{text:t}).notify(i)}},n(window).resize(function(){for(var t in n.notify.queue)n.notify.queue[t].update()}),n(window).scroll(function(){for(var t in n.notify.queue)n.notify.queue[t].update()})})(jQuery);
(function (win, doc) {
    var ENV = {
        on: function (el, type, cb) {
            if ('addEventListener' in win) {
                el.addEventListener(type, cb, false);
            } else {
                el.attachEvent('on' + type, cb);
            }
        },

        off: function (el, type, cb) {
            if ('addEventListener' in win) {
                el.removeEventListener(type, cb, false);
            } else {
                el.detachEvent('on' + type, cb);
            }
        },

        bind: function (fn, ctx) {
            return function () {
                fn.apply(ctx, arguments);
            };
        },

        isArray: Array.isArray ||
        function (obj) {
            return Object.prototype.toString.call(obj) === '[object Array]';
        },

        config: function (preferred, fallback) {
            return preferred ? preferred : fallback;
        },

        transSupport: false,

        useFilter: /msie [678]/i.test(navigator.userAgent),
        // sniff, sniff
        checkTransition: function () {
            var el = doc.createElement('div');
            var vendors = {
                webkit: 'webkit',
                Moz: '',
                O: 'o',
                ms: 'MS'
            };

            for (var vendor in vendors) {
                if (vendor + 'Transition' in el.style) {
                    this.vendorPrefix = vendors[vendor];
                    this.transSupport = true;
                }
            }
        }
    };

    ENV.checkTransition();

// 所有由脚本创建的DOM结构都应该放置在这个容器里
// 以便统一DOM树形结构 方便调试
    var DOMPanel = (function () {

        var panel = null;

        return {

            append: function (dom) {
                this.getPanel().append(dom);
            },

            prepend: function (dom) {
                this.getPanel().prepend(dom);
            },

            getPanel: function () {
                if (panel === null) {
                    panel = jQuery('#domPanel');
                    if (panel.length === 0) { // 新版jQuery size()方法废弃
                        panel = jQuery('<div id="domPanel" />').prependTo('body');
                    }

                    // 点击对话框不会触发给document绑定的点击行为
                    panel.click(Toolkit.cancelBubble);
                    panel.mousedown(Toolkit.cancelBubble);
                }

                return panel;
            }
        };

    })();

    var Notify = function (o) {
        o = o || {};
        this.queue = [];
        this.baseCls = o.baseCls || 'notify';
        this.addnCls = o.addnCls || '';
        this.timeout = 'timeout' in o ? o.timeout : 2000;
        this.waitForMove = o.waitForMove || false;
        this.clickToClose = o.clickToClose || false;
        this.container = o.container;

        try {
            this.setupEl();
        } catch (e) {
            jQuery(ENV.bind(this.setupEl, this));
        }
    };

    Notify.prototype = {

        constructor: Notify,

        setupEl: function () {
            var el = doc.createElement('div');
            el.style.display = 'none';
            this.container = this.container || DOMPanel.getPanel()[0];
            this.container.appendChild(el);
            this.el = el;
            this.removeEvent = ENV.bind(this.remove, this);
            this.transEvent = ENV.bind(this.afterAnimation, this);
            this.run();
        },

        afterTimeout: function () {
            if (!ENV.config(this.currentMsg.waitForMove, this.waitForMove)) {
                this.remove();
            } else if (!this.removeEventsSet) {
                ENV.on(doc.body, 'mousemove', this.removeEvent);
                ENV.on(doc.body, 'click', this.removeEvent);
                ENV.on(doc.body, 'keypress', this.removeEvent);
                ENV.on(doc.body, 'touchstart', this.removeEvent);
                this.removeEventsSet = true;
            }
        },

        run: function () {
            if (this.animating || !this.queue.length || !this.el) {
                return;
            }

            this.animating = true;
            if (this.currentTimer) {
                clearTimeout(this.currentTimer);
                this.currentTimer = null;
            }

            var msg = this.queue.shift();
            var clickToClose = ENV.config(msg.clickToClose, this.clickToClose);

            if (clickToClose) {
                ENV.on(this.el, 'click', this.removeEvent);
                ENV.on(this.el, 'touchstart', this.removeEvent);
            }
            var timeout = ENV.config(msg.timeout, this.timeout);

            if (timeout > 0) {
                this.currentTimer = setTimeout(ENV.bind(this.afterTimeout, this), timeout);
            }

            if (ENV.isArray(msg.html)) {
                msg.html = '<ul><li>' + msg.html.join('<li>') + '</ul>';
            }

            this.el.innerHTML = msg.html;
            this.currentMsg = msg;
            this.el.className = this.baseCls;
            if (ENV.transSupport) {
                this.el.style.display = 'block';
                setTimeout(ENV.bind(this.showMessage, this), 50);
            } else {
                this.showMessage();
            }

        },

        setOpacity: function (opacity) {
            if (ENV.useFilter) {
                try {
                    this.el.filters.item('DXImageTransform.Microsoft.Alpha').Opacity = opacity * 100;
                } catch (err) {
                }
            } else {
                this.el.style.opacity = String(opacity);
            }
        },

        showMessage: function () {
            var addnCls = ENV.config(this.currentMsg.addnCls, this.addnCls);
            if (ENV.transSupport) {
                this.el.className = this.baseCls + ' ' + addnCls + ' ' + this.baseCls + '-animate';
            } else {
                var opacity = 0;
                this.el.className = this.baseCls + ' ' + addnCls + ' ' + this.baseCls + '-js-animate';
                this.setOpacity(0); // reset value so hover states work
                this.el.style.display = 'block';

                var self = this;
                var interval = setInterval(function () {
                    if (opacity < 1) {
                        opacity += 0.1;
                        opacity = Math.min(1, opacity);
                        self.setOpacity(opacity);
                    } else {
                        clearInterval(interval);
                    }
                }, 30);
            }
        },

        hideMessage: function () {
            var addnCls = ENV.config(this.currentMsg.addnCls, this.addnCls);
            if (ENV.transSupport) {
                this.el.className = this.baseCls + ' ' + addnCls;
                ENV.on(this.el, ENV.vendorPrefix ? ENV.vendorPrefix + 'TransitionEnd' : 'transitionend', this.transEvent);
            } else {
                var opacity = 1;
                var self = this;
                var interval = setInterval(function () {
                    if (opacity > 0) {
                        opacity -= 0.1;
                        opacity = Math.max(0, opacity);
                        self.setOpacity(opacity);
                    } else {
                        self.el.className = self.baseCls + ' ' + addnCls;
                        clearInterval(interval);
                        self.afterAnimation();
                    }
                }, 30);
            }
        },

        afterAnimation: function () {
            if (ENV.transSupport) {
                ENV.off(this.el, ENV.vendorPrefix ? ENV.vendorPrefix + 'TransitionEnd' : 'transitionend', this.transEvent);
            }

            if (this.currentMsg.cb) {
                this.currentMsg.cb();
            }
            this.el.style.display = 'none';
            this.animating = false;
            this.run();
        },

        remove: function (e) {
            var cb = typeof e === 'function' ? e : null;

            ENV.off(doc.body, 'mousemove', this.removeEvent);
            ENV.off(doc.body, 'click', this.removeEvent);
            ENV.off(doc.body, 'keypress', this.removeEvent);
            ENV.off(doc.body, 'touchstart', this.removeEvent);
            ENV.off(this.el, 'click', this.removeEvent);
            ENV.off(this.el, 'touchstart', this.removeEvent);
            this.removeEventsSet = false;

            if (cb && this.currentMsg) {
                this.currentMsg.cb = cb;
            }
            if (this.animating) {
                this.hideMessage();
            } else if (cb) {
                cb();
            }
        },

        log: function (html, o, cb, defaults) {
            var msg = {},
                opt = null;
            if (defaults) {
                for (opt in defaults) {
                    msg[opt] = defaults[opt];
                }
            }

            if (typeof o === 'function') {
                cb = o;
            } else if (o) {
                for (opt in o) {
                    msg[opt] = o[opt];
                }
            }

            msg.html = html;
            msg.cb = cb ? cb : msg.cb;
            this.queue.push(msg);
            this.run();
            return this;
        },

        spawn: function (defaults) {
            var self = this;
            return function (html, o, cb) {
                return self.log.call(self, html, o, cb, defaults);
            };
        }
    };

    var notify = window.notify = new Notify();
    notify.info = notify.spawn({
        addnCls: 'notify-info'
    });

    notify.error = notify.spawn({
        addnCls: 'notify-error'
    });

    notify.warn = notify.spawn({
        addnCls: 'notify-warn'
    });

    notify.success = notify.spawn({
        addnCls: 'notify-success'
    });
})(window, document);