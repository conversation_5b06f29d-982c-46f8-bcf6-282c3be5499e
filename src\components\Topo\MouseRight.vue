<template>
  <div
    v-show="isVisible"
    class="topo-mouse-right-layout"
    :style="{
      left: `${positionLimit.x || -1000}px`,
      top: `${positionLimit.y || -1000}px`,
    }"
  >
    <ul class="topo-mouse-right-list">
      <li
        v-show="nodeSelectedList.length"
        class="topo-mouse-right-item"
        @mouseenter="handleMouseenter($event, 'align')"
        @mouseleave="handleMouseleave()"
      >
        <span>对齐</span>
        <span> &gt; </span>
      </li>

      <li
        v-show="nodeSelectedList.length"
        class="topo-mouse-right-item"
        @click="$emit('handleCloneClick')"
      >
        <span>复制</span>
        <span>Ctrl+C</span>
      </li>
      <li class="topo-mouse-right-item" @click="$emit('handlePasteClick')">
        <span>粘贴</span>
        <span>Ctrl+V</span>
      </li>
      <li
        v-show="nodeSelectedList.length"
        class="topo-mouse-right-item"
        @click="$emit('handleDeleteClick', $event)"
      >
        <span>删除</span>
        <span>Delete</span>
      </li>
      <li
        v-show="nodeSelectedList.length"
        class="topo-mouse-right-item"
        @click="$emit('handleLockClick', $event)"
      >
        <span>锁定</span>
        <span>Ctrl+L</span>
      </li>
      <li
        v-show="nodeSelectedList.length"
        class="topo-mouse-right-item"
        @click="$emit('handleUnlockClick', $event)"
      >
        <span>解除锁定</span>
        <span>Ctrl+L</span>
      </li>
      <!-- 超过两个才能组合 -->
      <li
        v-show="nodeSelectedList.length >= 2"
        class="topo-mouse-right-item"
        @click="$emit('handleCompositionClick', $event)"
      >
        <span>编组</span>
        <span>Ctrl+G</span>
      </li>
      <!-- 打散 -->
      <li
        v-show="nodeSelectedList.length >= 2"
        class="topo-mouse-right-item"
        @click="$emit('handleBreakClick', $event)"
      >
        打散
      </li>
      <div v-show="nodeSelectedList.length === 1 && nodeSelectedList[0].linkId">
        <li
          class="topo-mouse-right-item"
          @mouseenter="handleMouseenter($event, 'rotate')"
          @mouseleave="handleMouseleave()"
        >
          旋转角度
        </li>
        <li
          class="topo-mouse-right-item"
          @mouseenter="handleMouseenter($event, 'mirror')"
          @mouseleave="handleMouseleave()"
        >
          翻转
        </li>
      </div>
      <li
        v-show="nodeSelectedList.length > 1"
        class="topo-mouse-right-item"
        @click="handleAlignLinkClick()"
      >
        连线等距
      </li>
      <li
        v-show="nodeSelectedList.length > 1"
        class="topo-mouse-right-item"
        @click="handleParallelLinkClick()"
      >
        平行连线
      </li>
      <li
        v-show="nodeSelectedList.length"
        class="topo-mouse-right-item"
        @click="handleSubLayerClick"
      >
        子图层
      </li>
      <li
        v-show="nodeSelectedList.length"
        class="topo-mouse-right-item"
        @click="handleSubLayerRemoveClick"
      >
        移除子图层
      </li>
    </ul>
    <ul
      v-if="subOptions.length"
      class="topo-mouse-right-list topo-mouse-right-list-sub"
      @mouseover="handleSubMouseover()"
      :style="{
        top: `${subTop}px`,
      }"
    >
      <li
        v-for="item in subOptions"
        :key="item.value"
        class="topo-mouse-right-item"
        @click="handleSubClick(item.value)"
      >
        <span>{{ item.label }}</span>
        <span> {{ item.shortcutKey }} </span>
      </li>
    </ul>
    <Modal
      v-model="isAlignLinkVisible"
      title="设置等距"
      @on-ok="handleAlignLinkSubmit"
      @on-cancel="handleAlignLinkCancel"
    >
      <InputNumber v-model="distance" :min="0"></InputNumber>
      <div slot="footer">
        <Button type="info" @click="handleAlignLinkSubmit"> 确定 </Button>
        <Button @click="handleAlignLinkCancel"> 取消 </Button>
      </div>
    </Modal>
    <SublayeAddrModal
      ref="addSublayerModal"
      :nodeSelectedList="nodeSelectedList"
      :mapId="mapId"
    ></SublayeAddrModal>
    <SublayerNodeRemoveModal
      ref="removeSublayerModal"
      :nodeSelectedList="nodeSelectedList"
      :mapId="mapId"
    ></SublayerNodeRemoveModal>
  </div>
</template>

<script>
import SublayeAddrModal from "./Modal/SublayerAddModal.vue";
import SublayerNodeRemoveModal from "./Modal/SublayerNodeRemoveModal.vue";

export default {
  name: "Mouseright",
  components: { SublayeAddrModal, SublayerNodeRemoveModal },
  props: {
    position: {
      type: Object,
      default: () => {},
    },
    isVisible: {
      type: Boolean,
      default: false,
    },
    nodeSelectedList: {
      type: Array,
      default: () => [],
    },
    mapId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      isAlignLinkVisible: false,
      distance: 10,
      subOptions: [],
      isSubVisible: false,
      timer: null,
      subTop: 0,
      subTitle: "",
      positionLimit: { x: 0, y: 0 },
    };
  },

  mounted() {},
  watch: {
    isVisible(val) {
      if (val) {
        this.setViewPosition();
      } else {
        this.clearSub();
      }
    },
  },
  methods: {
    // 旋转
    handleRotateClick(angle) {
      this.$emit("onMenuClick", {
        type: "rotate",
        val: angle,
      });
    },
    // 镜像
    handleMirrorClick(direction) {
      this.$emit("onMenuClick", {
        type: "mirror",
        val: direction,
      });
    },
    handleAlignLinkSubmit() {
      this.$emit("onMenuClick", {
        type: "alignLink",
        val: this.distance,
      });
      this.handleAlignLinkCancel();
    },
    handleAlignLinkClick() {
      this.isAlignLinkVisible = true;
    },
    handleAlignLinkCancel() {
      this.isAlignLinkVisible = false;
    },
    // 平行连线
    handleParallelLinkClick() {
      this.$emit("onMenuClick", {
        type: "paralleLink",
      });
    },
    handleSubLayerClick() {
      this.$refs.addSublayerModal.show();
    },
    handleSubLayerRemoveClick() {
      this.$refs.removeSublayerModal.show();
    },
    handleMouseenter(ev, val) {
      this.subTop = ev.target.offsetTop;
      this.subTitle = val;
      switch (val) {
        case "rotate":
          this.subOptions = [
            {
              label: "30°",
              value: 30,
            },
            {
              label: "45°",
              value: 45,
            },
            {
              label: "60°",
              value: 60,
            },
          ];
          break;
        case "mirror":
          this.subOptions = [
            {
              label: "水平翻转",
              value: "vertical",
            },
            {
              label: "垂直翻转",
              value: "horizontal",
            },
          ];
          break;
        case "subLayer":
          this.subOptions = [
            {
              label: "新子图层",
              value: "1",
            },
            {
              label: "已有子图层",
              value: "2",
            },
          ];
          break;
        case "align":
          this.subOptions = [
            {
              label: "左对齐",
              value: 1,
              shortcutKey: "Ctrl+Alt+L",
            },
            {
              label: "右对齐",
              value: 2,
              shortcutKey: "Ctrl+Alt+R",
            },
            {
              label: "顶对齐",
              value: 3,
              shortcutKey: "Ctrl+Alt+T",
            },
            {
              label: "底对齐",
              value: 4,
              shortcutKey: "Ctrl+Alt+B",
            },
            {
              label: "水平居中",
              value: 5,
              shortcutKey: "Ctrl+Alt+C",
            },
            {
              label: "垂直居中",
              value: 6,
              shortcutKey: "Ctrl+Alt+M",
            },
            {
              label: "水平等距",
              value: 7,
              shortcutKey: "Ctrl+Alt+H",
            },
            {
              label: "垂直等距",
              value: 8,
              shortcutKey: "Ctrl+Alt+V",
            },
          ];
          break;

        default:
          break;
      }
      this.cancelHideSub();
    },
    clearSub() {
      this.subOptions = [];
      this.subTitle = "";
    },
    handleMouseleave() {
      if (this.timer) clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.clearSub();
      }, 500);
    },
    cancelHideSub() {
      if (this.timer) clearTimeout(this.timer);
    },
    handleSubMouseover() {
      this.cancelHideSub();
    },
    handleSubClick(val) {
      switch (this.subTitle) {
        case "rotate":
          this.handleRotateClick(val);
          break;
        case "mirror":
          this.handleMirrorClick(val);
          break;
        case "subLayer":
          this.handleSubLayerClick(val);
          break;
        case "align":
          this.$bus.emit("alignClick", val);
          break;
        default:
          break;
      }
    },
    setViewPosition() {
      const marginBottom = document.body.clientHeight - this.position.y;
      this.positionLimit = this.position;
      if (this.nodeSelectedList.length && marginBottom <= 330) {
        this.positionLimit.y = document.body.clientHeight - 330;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.topo-mouse-right-layout {
  position: fixed;
  width: 160px;
  z-index: 999999;
}
.topo-mouse-right-list {
  display: flex;
  flex-direction: column;
  list-style: none;
  border-radius: $borderRadius;
  border: $border;
  overflow: hidden;
}
.topo-mouse-right-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  background-color: $cardBg;
  cursor: pointer;
  &:hover {
    background-color: $selectBgHover;
  }
}
.topo-mouse-right-list-sub {
  position: absolute;
  width: 160px;
  left: 160px;
  top: 0;
}
</style>
