import util from "@/utils/utils";
import store from "../../store/";
import vue from "../../main.js";
import { formatColor } from "@/utils/tools/format";

function transformRotate(options) {
  let x = options.x;
  let y = options.y;
  let w = options.w;
  let h = options.h;
  let angle = options.rotate;

  let r = Math.sqrt(Math.pow(w, 2) + Math.pow(h, 2)) / 2;
  let a = Math.round((Math.atan(h / w) * 180) / Math.PI);
  let tlbra = 180 - angle - a;
  let trbla = a - angle;
  let ta = 90 - angle;
  let ra = angle;

  let halfWidth = w / 2;
  let halfHeight = h / 2;

  let middleX = x + halfWidth;
  let middleY = y + halfHeight;

  let topLeft = {
    x: middleX + r * Math.cos((tlbra * Math.PI) / 180),
    y: middleY - r * Math.sin((tlbra * Math.PI) / 180),
  };
  let top = {
    x: middleX + halfHeight * Math.cos((ta * Math.PI) / 180),
    y: middleY - halfHeight * Math.sin((ta * Math.PI) / 180),
  };
  let topRight = {
    x: middleX + r * Math.cos((trbla * Math.PI) / 180),
    y: middleY - r * Math.sin((trbla * Math.PI) / 180),
  };
  let right = {
    x: middleX + halfWidth * Math.cos((ra * Math.PI) / 180),
    y: middleY + halfWidth * Math.sin((ra * Math.PI) / 180),
  };
  let bottomRight = {
    x: middleX - r * Math.cos((tlbra * Math.PI) / 180),
    y: middleY + r * Math.sin((tlbra * Math.PI) / 180),
  };
  let bottom = {
    x: middleX - halfHeight * Math.sin((ra * Math.PI) / 180),
    y: middleY + halfHeight * Math.cos((ra * Math.PI) / 180),
  };
  let bottomLeft = {
    x: middleX - r * Math.cos((trbla * Math.PI) / 180),
    y: middleY + r * Math.sin((trbla * Math.PI) / 180),
  };
  let left = {
    x: middleX - halfWidth * Math.cos((ra * Math.PI) / 180),
    y: middleY - halfWidth * Math.sin((ra * Math.PI) / 180),
  };
  let minX = Math.min(topLeft.x, topRight.x, bottomRight.x, bottomLeft.x);
  let maxX = Math.max(topLeft.x, topRight.x, bottomRight.x, bottomLeft.x);
  let minY = Math.min(topLeft.y, topRight.y, bottomRight.y, bottomLeft.y);
  let maxY = Math.max(topLeft.y, topRight.y, bottomRight.y, bottomLeft.y);
  return {
    point: [
      topLeft,
      top,
      topRight,
      right,
      bottomRight,
      bottom,
      bottomLeft,
      left,
    ],
    w: maxX - minX,
    h: maxY - minY,
    left: minX,
    right: maxX,
    top: minY,
    bottom: maxY,
  };
}

function getPointAndOpposite(point, ex, ey) {
  let oppositePoint = {};
  let currentPoint = {};

  let minDelta = 1000;
  let currentIndex = 0;
  let oppositeIndex = 0;

  point.forEach((p, index) => {
    const delta = Math.sqrt(Math.pow(p.x - ex, 2) + Math.pow(p.y - ey, 2));
    if (delta < minDelta) {
      currentPoint = p;
      currentIndex = index;
      minDelta = delta;
      // 对角线点index相差4
      let offset = 4;
      let oIndex = index - offset;
      if (oIndex < 0) {
        oIndex = index + offset;
      }
      // 取对角线点坐标
      oppositePoint = point.slice(oIndex, oIndex + 1)[0];
      oppositeIndex = oIndex;
    }
  });

  return {
    // current: {
    //   index: currentIndex,
    //   point: currentPoint,
    // },
    // opposite: {
    oppositeIndex: oppositeIndex,
    oppositePoint: oppositePoint,
    // },
  };
}

function getNewDraw(oPoint, scale, oTransformedRect, baseIndex) {
  let scaledRect = getScaledRect({
    x: oPoint.x,
    y: oPoint.y,
    w: oPoint.w,
    h: oPoint.h,
    scale: scale,
    rotate: oPoint.rotate,
  });
  let transformedRotateRect = transformRotate({
    ...scaledRect,
    rotate: oPoint.rotate,
  });
  // 计算到平移后的新坐标
  let translatedX =
    oTransformedRect.point[baseIndex].x -
    transformedRotateRect.point[baseIndex].x +
    transformedRotateRect.left;
  let translatedY =
    oTransformedRect.point[baseIndex].y -
    transformedRotateRect.point[baseIndex].y +
    transformedRotateRect.top;

  // 计算平移后元素左上角的坐标
  let newX = translatedX + transformedRotateRect.w / 2 - scaledRect.w / 2;
  let newY = translatedY + transformedRotateRect.h / 2 - scaledRect.h / 2;

  // 缩放后元素的高宽
  let newWidth = scaledRect.w;
  let newHeight = scaledRect.h;

  return {
    x: Math.round(newX),
    y: Math.round(newY),
    width: Math.round(newWidth),
    height: Math.round(newHeight),
  };
}

function getScaledRect(params, baseIndex) {
  let { x, y, w, h, scale } = params;
  let offset = {
    x: 0,
    y: 0,
  };
  let deltaXScale = scale.x - 1;
  let deltaYScale = scale.y - 1;
  let deltaWidth = w * deltaXScale;
  let deltaHeight = h * deltaYScale;
  let newWidth = w + deltaWidth;
  let newHeight = h + deltaHeight;
  let newX = x - deltaWidth / 2;
  let newY = y - deltaHeight / 2;
  if (baseIndex) {
    let points = [
      { x, y },
      { x: x + w, y },
      { x: x + w, y: y + h },
      { x, y: y + h },
    ];
    let newPoints = [
      { x: newX, y: newY },
      { x: newX + newWidth, y: newY },
      { x: newX + newWidth, y: newY + newHeight },
      { x: newX, y: newY + newHeight },
    ];
    offset.x = points[baseIndex].x - newPoints[baseIndex].x;
    offset.y = points[baseIndex].y - newPoints[baseIndex].y;
  }
  return {
    x: newX + offset.x,
    y: newY + offset.y,
    w: newWidth,
    h: newHeight,
  };
}

function updatePathPoint(points, { tx, ty }) {
  points.x += tx;
  points.y += ty;
  return points;
}

function generatePathD({ pathPoints = [] }, isDrawBlock) {
  let d = "";
  pathPoints.forEach((p, i) => {
    if (i === 0) {
      // first point
      d += "M ";
    } else if (p.q) {
      // quadratic
      d += `Q ${p.q.x} ${p.q.y} `;
    } else if (p.c) {
      // cubic
      d += `C ${p.c[0].x} ${p.c[0].y} ${p.c[1].x} ${p.c[1].y} `;
    } else if (p.a) {
      // arc
      d += `A ${p.a.rx} ${p.a.ry} ${p.a.rot} ${p.a.laf} ${p.a.sf} `;
    } else {
      d += "L ";
    }
    d += `${p.x} ${p.y} `;
  });
  if (isDrawBlock) d += "Z";
  return d;
}

function generatePathPoint(d) {
  let points = [];
  if (d) {
    const arr = d.split(" ").map((ele) => (isNaN(+ele) ? ele : +ele));
    for (let i = 0; i < arr.length; i++) {
      if (arr[i] === "M" || arr[i] === "L") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          type: arr[i],
        });
      } else if (arr[i] === "Q") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          q: {
            x: arr[i + 3],
            y: arr[i + 4],
          },
        });
      } else if (arr[i] === "C") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          c: [
            {
              x: arr[i + 3],
              y: arr[i + 4],
            },
            {
              x: arr[i + 5],
              y: arr[i + 6],
            },
          ],
        });
      } else if (arr[i] === "A") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          a: {
            rx: arr[i + 3],
            ry: arr[i + 4],
            rot: arr[i + 5],
            laf: arr[i + 6],
            sf: arr[i + 7],
          },
        });
      }
    }

    return points;
  }
}
// 获取四个边缘的位置
// 先不加旋转
function getEdgePosition(ele, groupPostionList) {
  let position = {};
  if (ele.groupId && groupPostionList && groupPostionList.length) {
    const groupItem = groupPostionList.find(
      (eleGroup) => ele.groupId === eleGroup.groupId
    );
    const { x, y, w, h } = groupItem.position;
    position = {
      l: x,
      r: x + w,
      t: y,
      b: y + h,
      x,
      y,
      w,
      h,
    };
  } else if (ele.type === "CustomPath") {
    if (!ele.pathPoints) return position;
    const xList = ele.pathPoints.map((ele) => ele.x).sort((a, b) => a - b);
    const yList = ele.pathPoints.map((ele) => ele.y).sort((a, b) => a - b);
    const l = xList[0];
    const r = xList[xList.length - 1];
    const t = yList[0];
    const b = yList[yList.length - 1];
    position = {
      l,
      r,
      t,
      b,
      x: l,
      y: t,
      w: r - l,
      h: b - t,
    };
  } else {
    const data = { ...ele };
    if (ele.nodeType === "text") {
      let { w, h, rotate, middleRotatePoint } = ele;
      if (rotate < -180) {
        rotate = 360 + rotate;
        ele.rotate = rotate;
      }

      const r = Math.abs(rotate);

      if (r >= 85 && r <= 95) {
        data.w = h;
        data.h = w;

        data.x = middleRotatePoint.x - h / 2;
        data.y = middleRotatePoint.y - w / 2;
      }
    }

    const { x, y, w, h } = data;
    position = {
      l: x,
      r: x + w,
      t: y,
      b: y + h,
      x,
      y,
      w,
      h,
    };
  }
  return position;
}
function hexToRgb(hex) {
  return (
    "rgb(" +
    parseInt("0x" + hex.slice(1, 3)) +
    "," +
    parseInt("0x" + hex.slice(3, 5)) +
    "," +
    parseInt("0x" + hex.slice(5, 7)) +
    ")"
  );
}
// 根据框选的位置，找到在范围内的元素
function getNodesInScope(nodeLinkList, position, isDrawBlock) {
  let list = [];
  const { pL, pR, pT, pB } = position;
  nodeLinkList.forEach((ele) => {
    if (ele.nodeType === "text") {
      let { fontColor, sublayerList = [] } = ele;
      if (fontColor === "#fff") {
        fontColor = "#ffffff";
      }
      if (fontColor.includes("#")) {
        fontColor = hexToRgb(fontColor);
      }
      const isInclude = sublayerList.some((ele) =>
        store.state.topoStore.textHideBySublayerList.includes(ele.sublayerId)
      );
      if (store.state.topoStore.isHideTextBySublayer) {
        if (isInclude) return;
      } else if (
        isInclude &&
        [
          "rgb(255,0,0)",
          "rgb(0,255,0)",
          "rgb(255,255,255)",
          "rgb(51,96,251)",
        ].includes(fontColor)
      ) {
        if (!store.state.topoStore.textColorList.includes(fontColor)) return;
      }
    }
    if (ele.linkId) {
      const linkStyles = JSON.parse(ele.linkStyles);
      if (linkStyles.isBlock && !isDrawBlock) return;
    }
    const { l, r, t, b } = getEdgePosition(ele);
    if (l >= pL && r <= pR && t >= pT && b <= pB) {
      list.push(ele);
    }
  });
  return list;
}
// 获取框选的左上角坐标
function getLeftPosition(list) {
  const positionList = list.map((ele) => {
    if (ele.nodeId) {
      return {
        x: ele.x,
        y: ele.y,
      };
    } else {
      return {
        x: ele.pathPoints[0].x,
        y: ele.pathPoints[0].y,
      };
    }
  });
  const leftPosition = {
    x: positionList.sort((a, b) => a.x - b.x)[0].x,
    y: positionList.sort((a, b) => a.y - b.y)[0].y,
  };
  return leftPosition;
}
// 获取组合区域的大小
function getGroupAreaSize(nodeList) {
  const positionList = [];
  nodeList.forEach((ele) => {
    positionList.push(getEdgePosition(ele));
  });

  const getLimitValue = (position, type) => {
    return Math[type](...positionList.map((ele) => ele[position]));
  };
  const l = getLimitValue("l", "min");
  const r = getLimitValue("r", "max");
  const t = getLimitValue("t", "min");
  const b = getLimitValue("b", "max");

  return {
    w: r - l,
    h: b - t,
    x: l,
    y: t,
  };
}
// 批量获取linkId和nodeId
function getNodeLinkList(list, isDelete) {
  let nodeList = [];
  let linkList = [];
  list.forEach((ele) => {
    if (!ele.mapId) return;
    if (ele.linkId) {
      const { linkStyles } = ele;
      const styles = linkStyles ? JSON.parse(linkStyles) : {};

      ele.linkPath = generatePathD(ele, styles.isBlock);
      linkList.push(isDelete ? ele.linkId : ele);
    } else {
      const { x, y, w, h } = ele;
      ele.nodeSize = `${w}*${h}`;
      ele.nodePosition = `${x},${y}`;
      ele.nodePosition = `${x},${y}`;
      ele.fontColor = formatColor(ele.fontColor);
      nodeList.push(isDelete ? ele.nodeId : ele);
    }
  });
  return {
    nodeList,
    linkList,
  };
}

function getImageUrl(url) {
  if (url.startsWith("img/")) return url;
  let basePath = `${util.httpHead}://${window.location.host}`;
  const { host } = window.location;
  if (host.includes("localhost")) {
    basePath = "http://**************:6818";
  }
  return `${basePath}${url}`;
}
// 获取节点和分组的大小
function getNodeAndGroupPosition(positionList, groupPostionList) {
  let data = {};
  for (let i = 0; i < positionList.length; i++) {
    const item = groupPostionList.find(
      (ele) => positionList[i].groupId === ele.groupId
    );
    if (item) {
      data[item.groupId] = {
        ...item.position,
        groupId: item.groupId,
      };
    } else {
      const { nodeId, linkId } = positionList[i];
      data[linkId ? linkId : nodeId] = positionList[i];
    }
  }
  return Object.values(data);
}
//计算向量叉乘
function crossMul(v1, v2) {
  return v1.x * v2.y - v1.y * v2.x;
}
//判断两条线段是否相交
function checkCross(p1, p2, p3, p4) {
  var v1 = { x: p1.x - p3.x, y: p1.y - p3.y },
    v2 = { x: p2.x - p3.x, y: p2.y - p3.y },
    v3 = { x: p4.x - p3.x, y: p4.y - p3.y },
    v = crossMul(v1, v3) * crossMul(v2, v3);
  v1 = { x: p3.x - p1.x, y: p3.y - p1.y };
  v2 = { x: p4.x - p1.x, y: p4.y - p1.y };
  v3 = { x: p2.x - p1.x, y: p2.y - p1.y };
  return v <= 0 && crossMul(v1, v3) * crossMul(v2, v3) <= 0 ? true : false;
}

// 获取连线时，线段与元素相交的点，然后线段结束点设置为相交的点
function getIntersectPoint(points, nodeId, nodeList) {
  let node = null;
  for (let i = 0; i < nodeList.length; i++) {
    if (nodeId === nodeList[i].nodeId) {
      node = nodeList[i];
    }
  }
  if (node) {
    // 取中心点
    return node.middleRotatePoint;
    // 以下是取相交的点
    const { l, r, t, b, w, h } = getEdgePosition(node);
    const [p1, p2] = points;
    // 顶部相交
    const nodePoints = [
      [
        { x: l, y: t },
        { x: l, y: b },
      ],
      [
        { x: l, y: t },
        { x: r, y: t },
      ],
      [
        { x: r, y: t },
        { x: r, y: b },
      ],
      [
        { x: l, y: b },
        { x: r, y: b },
      ],
    ];
    let index = null;
    let point = {};
    nodePoints.forEach((ele, i) => {
      const isIntersect = checkCross(
        ...ele,
        { x: p1.x, y: p1.y },
        { x: p2.x, y: p2.y }
      );
      if (isIntersect) {
        index = i;
      }
    });
    switch (index) {
      case 0:
        point = { x: l, y: t + h / 2 };
        break;
      case 1:
        point = { x: l + w / 2, y: t };
        break;
      case 2:
        point = { x: r, y: t + h / 2 };
        break;
      case 3:
        point = { x: l + w / 2, y: b };
        break;
      default:
        point = { x: l + w / 2, y: b };
        break;
    }
    return point;
  }
}
/**
 *
 * @param {*} ptSrc  要旋转的点
 * @param {*} ptRotationCenter 圆心点
 * @param {*} angle 要旋转的角度
 * @returns
 */
function rotatePoint(ptSrc, ptRotationCenter, angle) {
  let a = ptRotationCenter.x;
  let b = ptRotationCenter.y;
  let x0 = ptSrc.x;
  let y0 = ptSrc.y;
  let rx =
    a +
    (x0 - a) * Math.cos((angle * Math.PI) / 180) -
    (y0 - b) * Math.sin((angle * Math.PI) / 180);
  let ry =
    b +
    (x0 - a) * Math.sin((angle * Math.PI) / 180) +
    (y0 - b) * Math.cos((angle * Math.PI) / 180);
  let res = { x: +rx.toFixed(2), y: +ry.toFixed(2) };
  return res;
}

function getTransformPosition(positions, len) {
  const [p1, p2] = positions;
  // 弧度
  let hd = Math.atan2(p2.y - p1.y, p2.x - p1.x);
  // 直角三角形 角度
  const angle = (hd * 180) / Math.PI;
  let jd = 0;
  // 不同象限分别处理
  // if (angle > 0 && angle < 90) {
  //   jd = angle;
  // } else
  if (angle >= 90 && angle < 180) {
    jd = angle - 90;
  } else if (angle < 0 && angle >= 90) {
    jd = angle - 270;
  } else {
    jd = 90 + angle;
  }

  hd = (jd * Math.PI) / 180;

  const tx = Math.round(Math.cos(hd) * len);
  const ty = Math.round(Math.sin(hd) * len);
  return {
    tx: tx,
    ty: ty,
    angle,
  };
}
// 判断点在线的左边还是右边
// 小于 在右边
function checkPointLeftOrRight(point, line) {
  // let PA = [line[0].x - point.x, line[0].y - point.y];
  // let PB = [line[1].x - point.x, line[1].y - point.y];
  // console.log("🚀 ~ ", PA[0] * PB[1] - PA[1] * PB[0]);

  // const res = PA[0] * PB[1] - PA[1] * PB[0] >= 0;
  const [p1, p2] = line;
  const res =
    (p1.y - p2.y) * point.x +
    (p2.x - p1.x) * point.y +
    p1.x * p2.y -
    p2.x * p1.y;

  return res <= 0;
}
function getTransformPosition1(positions, len) {
  const [p1, p2] = positions;
  // 弧度
  let hd = Math.atan2(p2.y - p1.y, p2.x - p1.x);
  // 直角三角形 角度
  const angle = (hd * 180) / Math.PI;
  const jd = 90 + angle;

  hd = (jd * Math.PI) / 180;

  const tx = Math.round(Math.cos(hd) * len);
  const ty = Math.round(Math.sin(hd) * len);
  return {
    tx: jd === 0 || jd === 180 ? -tx : tx,
    ty: jd === 0 || jd === 180 ? -ty : ty,
  };
}
// 获取点到线的距离
function distanceOfPointAndLine(p, line) {
  const [p1, p2] = line;
  //三角形三个边长
  let A = Math.abs(
    Math.sqrt(Math.pow(p.x - p1.x, 2) + Math.pow(p.y - p1.y, 2))
  );
  let B = Math.abs(
    Math.sqrt(Math.pow(p.x - p2.x, 2) + Math.pow(p.y - p2.y, 2))
  );
  let C = Math.abs(
    Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2))
  );
  //利用海伦公式计算三角形面积
  //周长的一半
  let P = (A + B + C) / 2;
  let allArea = Math.abs(Math.sqrt(P * (P - A) * (P - B) * (P - C)));
  //普通公式计算三角形面积反推点到线的垂直距离
  let dis = (2 * allArea) / C;
  return Math.round(dis);
}

function getCoordinateByAngle(angle) {
  let x1 = 0,
    y1 = 0,
    x2 = 0,
    y2 = 0;
  const getAngle = (a) => {
    a = a > 45 ? a - 45 : a;
    const hd = (Math.PI / 180) * a;
    return 0.5 * Math.tan(hd);
  };
  if (angle % 90 === 0) {
    if (angle === 0 || angle === 360) {
      x2 = 1;
    } else if (angle === 90) {
      y2 = 1;
    } else if (angle === 180) {
      x1 = 1;
    } else if (angle === 270) {
      y1 = 1;
    }
  } else {
    if (angle > 0 && angle < 90) {
      if (angle < 45) {
        y1 = 0.5 - getAngle(angle);
        x2 = 1;
        y2 = 1 - y1;
      } else {
        x1 = getAngle(angle);
        y2 = 1;
        x2 = 1 - x1;
      }
    } else if (angle > 90 && angle < 180) {
      angle = angle - 90;
      if (angle < 45) {
        x1 = 0.5 + getAngle(angle);
        y2 = 1;
        x2 = 1 - x1;
      } else {
        x1 = 1;
        y1 = getAngle(angle);
        y2 = 1 - y1;
      }
    } else if (angle > 180 && angle < 270) {
      angle = angle - 180;
      if (angle < 45) {
        x1 = 1;
        y1 = 0.5 + getAngle(angle);
        y2 = 1 - y1;
      } else {
        x2 = getAngle(angle);
        x1 = 1 - x2;
        y1 = 1;
      }
    } else if (angle > 270 && angle < 360) {
      angle = angle - 270;
      if (angle < 45) {
        x2 = 0.5 + getAngle(angle);
        x1 = 1 - x2;
        y1 = 1;
      } else {
        x2 = 1;
        y2 = getAngle(angle);
        y1 = 1 - y2;
      }
    }
  }

  return { x1, y1, x2, y2 };
}

// 获取弧度
function getRadian(startPoint, centerPoint) {
  let r = Math.atan2(
    startPoint.y - centerPoint.y,
    startPoint.x - centerPoint.x
  );
  return r;
}

// 获取圆外部点与中心点的连线  与  圆的交点  （三角函数计算）
function getIntersectionPoint(startPoint, centerPoint, radius) {
  const radian = getRadian(startPoint, centerPoint);

  const x = Math.cos(radian) * radius;
  const y = Math.sin(radian) * radius;
  let resX = 0,
    resY = 0;

  resX = centerPoint.x + x;
  resY = centerPoint.y + y;

  return {
    x: resX,
    y: resY,
  };
}

// 去除小数  防止计算错误
function segmentsIntr(a, b, c, d) {
  /** 1 解线性方程组, 求线段交点. **/
  // 如果分母为0 则平行或共线, 不相交
  let denominator = (b.y - a.y) * (d.x - c.x) - (a.x - b.x) * (c.y - d.y);
  if (denominator == 0) {
    return false;
  }

  // 线段所在直线的交点坐标 (x , y)
  let x =
    ((b.x - a.x) * (d.x - c.x) * (c.y - a.y) +
      (b.y - a.y) * (d.x - c.x) * a.x -
      (d.y - c.y) * (b.x - a.x) * c.x) /
    denominator;
  let y =
    -(
      (b.y - a.y) * (d.y - c.y) * (c.x - a.x) +
      (b.x - a.x) * (d.y - c.y) * a.y -
      (d.x - c.x) * (b.y - a.y) * c.y
    ) / denominator;

  x = +x.toFixed(2);
  y = +y.toFixed(2);

  a.x = +a.x.toFixed(2);
  a.y = +a.y.toFixed(2);
  b.x = +b.x.toFixed(2);
  b.y = +b.y.toFixed(2);
  c.x = +c.x.toFixed(2);
  c.y = +c.y.toFixed(2);
  d.x = +d.x.toFixed(2);
  d.y = +d.y.toFixed(2);

  /** 2 判断交点是否在两条线段上 **/
  if (
    // 交点在线段1上
    (x - a.x) * (x - b.x) <= 0 &&
    (y - a.y) * (y - b.y) <= 0 &&
    // 且交点也在线段2上
    (x - c.x) * (x - d.x) <= 0 &&
    (y - c.y) * (y - d.y) <= 0
  ) {
    // 返回交点p
    return {
      x: x,
      y: y,
    };
  }
  //否则不相交
  return false;
}

/**
 * 跟据线段 和 模长  获取点
 * len: 斜边
 */
function getVectorPoint(line, len) {
  const [p1, p2] = line;
  const radian = Math.atan2(p2.y - p1.y, p2.x - p1.x);
  let x = 0,
    y = 0;

  x = Math.cos(radian) * len;
  y = Math.sin(radian) * len;

  return { x: p1.x + x, y: p1.y + y };
}

function getVectorLen(line, centerDistanceChord) {
  const [p1, p2] = line;
  // 圆心到圆的起点长度
  const len = Math.abs(
    Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2))
  );
  return Math.abs(
    Math.sqrt(len * len - centerDistanceChord * centerDistanceChord)
  );
}

// 获取圆或者矩形与线的交点
function getIntersection(node, line) {
  const { middleRotatePoint, w } = node;
  const r = w / 2 - 1;
  // 圆心到线段的距离
  const centerDistanceLine = distanceOfPointAndLine(middleRotatePoint, line);

  // 线段不与圆相交
  if (centerDistanceLine > r) return;
  // 弦长
  const chordLength = Math.abs(
    Math.sqrt(r * r - centerDistanceLine * centerDistanceLine)
  );
  // 斜边
  const vectorLen = Math.abs(
    getVectorLen([middleRotatePoint, line[0]], centerDistanceLine) - chordLength
  );
  const point = getVectorPoint(line, vectorLen);
  return point;
}

function getRectLine(node) {
  const { w, h, x, y } = node;
  const res = [
    [
      { x, y },
      { x: x + w, y },
    ],
    [
      { x: x + w, y },
      { x: x + w, y: y + h },
    ],
    [
      { x: x + w, y: y + h },
      { x, y: y + h },
    ],
    [
      { x, y: y + h },
      { x, y },
    ],
  ];
  return res;
}
// 获取两点的距离
function getLengthByPoint(p1, p2) {
  return Math.abs(
    Math.sqrt(Math.pow(p2.y - p1.y, 2) + Math.pow(p2.x - p1.x, 2))
  );
}
// 设置连线吸附
function setLinkAdsorb(elements = [], adsorbType = "circle") {
  const nodes = elements.filter((ele) => ele.nodeId);
  const links = elements.filter((ele) => ele.linkId);
  links.forEach((link) => {
    const len = link.pathPoints.length;
    const first = link.pathPoints[0];
    const last = link.pathPoints[len - 1];
    nodes.forEach((node) => {
      const { middleRotatePoint, w } = node;
      const r = w / 2;

      // 判断连线的点是否在圆内 或者圆的附近，表示可以吸附这个圆
      // 跟据线段的点到圆心的距离与半径对比

      // 圆心距离起点的距离
      const distanceFirstDiff = Math.abs(
        Math.sqrt(
          Math.pow(middleRotatePoint.x - first.x, 2) +
            Math.pow(middleRotatePoint.y - first.y, 2)
        )
      );
      // 圆心距离终点的距离
      const distanceLastFirst = Math.abs(
        Math.sqrt(
          Math.pow(middleRotatePoint.x - last.x, 2) +
            Math.pow(middleRotatePoint.y - last.y, 2)
        )
      );
      // const { type, stationType } = node.metaData || {};

      // 起点距离圆心更近
      if (distanceFirstDiff < distanceLastFirst) {
        let point = null;

        if (adsorbType === "rect") {
          // 矩形
          const rectLines = getRectLine(node);
          rectLines.forEach((rectLine) => {
            const point1ByRotate = rotatePoint(
              rectLine[0],
              middleRotatePoint,
              node.rotate
            );

            const point2ByRotate = rotatePoint(
              rectLine[1],
              middleRotatePoint,
              node.rotate
            );

            const res = segmentsIntr(
              first,
              link.pathPoints[1],
              point1ByRotate,
              point2ByRotate
            );
            if (res) {
              point = res;
            }
          });
        } else {
          if (getLengthByPoint(node.middleRotatePoint, first) > node.w / 2)
            return;
          point = getIntersection(node, [link.pathPoints[1], first]);
        }
        if (!point) return;
        if (Number.isNaN(point.x) || Number.isNaN(point.x)) return;

        link.pathPoints[0] = point;
        vue.$set(link.pathPoints, 0, point);
        link.fromObj = node.nodeId;
      }

      if (distanceFirstDiff >= distanceLastFirst) {
        let point = null;
        if (adsorbType === "rect") {
          // 矩形
          const rectLines = getRectLine(node);
          rectLines.forEach((rectLine) => {
            const point1ByRotate = rotatePoint(
              rectLine[0],
              middleRotatePoint,
              node.rotate
            );

            const point2ByRotate = rotatePoint(
              rectLine[1],
              middleRotatePoint,
              node.rotate
            );

            const res = segmentsIntr(
              last,
              link.pathPoints[len - 2],
              point1ByRotate,
              point2ByRotate
            );
            if (res) {
              point = res;
            }
          });
        } else {
          if (getLengthByPoint(node.middleRotatePoint, last) > node.w / 2)
            return;
          point = getIntersection(node, [link.pathPoints[len - 2], last]);
        }
        if (!point) return;
        if (Number.isNaN(point.x) || Number.isNaN(point.x)) return;

        link.pathPoints[len - 1] = point;
        vue.$set(link.pathPoints, len - 1, point);
        link.endObj = node.nodeId;
      }
    });
  });
  return [...links, ...nodes];
}
function getLineLength(line) {
  const [p1, p2] = line;
  return Math.abs(
    Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2))
  );
}

function getAngleAndRadian(startPoint, endPoint) {
  const radian = Math.atan2(
    endPoint.y - startPoint.y,
    endPoint.x - startPoint.x
  );

  return {
    radian,
    angle: radian * (180 / Math.PI),
  };
}

const getTransform = (isRight, line, distance) => {
  let { angle, tx, ty } = getTransformPosition(line, distance);
  if (
    (angle === -90 && isRight) ||
    (angle === 90 && !isRight) ||
    (angle === 0 && isRight) ||
    (angle === 180 && isRight) ||
    (angle < 0 && angle > -180 && isRight) ||
    (angle > 0 && angle < 90 && isRight) ||
    (angle > 90 && angle < 180 && !isRight)
  ) {
    tx = -tx;
    ty = -ty;
  }

  return {
    tx,
    ty,
  };
};

function setText(list, type) {
  let textList = list.filter((ele) => ele.nodeId && ele.nodeType === "text");
  let linkList = list.filter((ele) => ele.linkId);

  linkList.forEach((link) => {
    let textBindList = [];
    textList.forEach((text) => {
      if (text.bindLink !== link.linkId) return;

      if (type === "Paralle") {
        paralleTextLink(text, link);
      } else if (type === "Distance") {
        distanceTextLink(text, link);
      }
      textBindList.push(text);
    });
    if (type === "AlignAndDistance") {
      textBindList.sort((a, b) => {
        if (
          "rgb(255,255,255)" === a.fontColor &&
          "rgb(255,255,255)" !== b.fontColor
        ) {
          return -1;
        } else {
          return 1;
        }
      });
      alignAndDistanceText(textBindList, link);
    }
  });

  return list;
}

// 等距文字和连线
function distanceTextLink(text, link) {
  let bindLinkIndexs = +text.bindSubLink - 1;
  let p1;
  let p2;
  p1 = link.pathPoints[bindLinkIndexs];
  p2 = link.pathPoints[bindLinkIndexs + 1];

  if (!p1 || !p2) {
    p1 = link.pathPoints[0];
    p2 = link.pathPoints[1];
  }

  // 获取文字中心点到线段的距离
  let distance = distanceOfPointAndLine(text.middleRotatePoint, [p1, p2]);
  const isRight = checkPointLeftOrRight(text.middleRotatePoint, [p1, p2]);

  // 设置文字移动距离
  const recordCenterPointY = (p1.y + p2.y) / 2;
  const centerPointY = (p1.y + p2.y) / 2;

  const diffY = centerPointY - recordCenterPointY;
  text.y += diffY;
  text.middleRotatePoint.y = text.y + text.h / 2;

  // 移动后 重新计算文字与连线的距离
  let targetDistance = 5 + text.h / 2;

  distance = targetDistance - distance;

  if (distance === 0) return;

  const { tx, ty } = getTransform(isRight, [p1, p2], distance);

  text.x += tx;
  text.y += ty;
  text.middleRotatePoint.y = text.y + text.h / 2;
  text.middleRotatePoint.x = text.x + text.w / 2;
}

// 平行文字和连线
function paralleTextLink(text, link) {
  let bindLinkIndexs = +text.bindSubLink - 1;
  let p1;
  let p2;
  p1 = link.pathPoints[bindLinkIndexs];
  p2 = link.pathPoints[bindLinkIndexs + 1];

  if (!p1 || !p2) {
    p1 = link.pathPoints[0];
    p2 = link.pathPoints[1];
  }

  // 设置文字角度
  let angelBase = (Math.atan2(p2.y - p1.y, p2.x - p1.x) * 180) / Math.PI;

  angelBase = +angelBase.toFixed();
  let rotate = 0;

  if (angelBase === 90 || angelBase === -90) {
    rotate = 90;
  } else if (angelBase > 90) {
    rotate = angelBase - 180;
  } else if (angelBase < -90) {
    rotate = 180 + angelBase;
  } else {
    rotate = angelBase;
  }

  text.rotate = rotate;
}

// 文字对齐 等距
function alignAndDistanceText(textList) {
  if (!textList[0]) return textList;
  //   文字排序 绿字在后面
  // textList.sort((a, b) => {
  //   if (a.fontColor === "rgb(0,255,0)" || a.nodeText === "000") {
  //     return 1;
  //   } else {
  //     return -1;
  //   }
  // });

  const { middleRotatePoint, rotate, w } = textList[0];
  const p1 = middleRotatePoint;

  textList.forEach((ele, index) => {
    if (index > 0 && ele.nodeType === "text") {
      const p2 = ele.middleRotatePoint;
      let { angle } = getAngleAndRadian(p1, p2);

      if (rotate > 0 && angle < 0) {
        angle = angle + 180;
      } else if (rotate < 0 && angle > 0) {
        angle = angle - 180;
      }

      const res = rotatePoint(p2, p1, rotate - angle);

      //   两个文字中心点的间距
      const len = getLineLength([p1, p2]);

      ele.middleRotatePoint = res;
      ele.x = ele.middleRotatePoint.x - ele.w / 2;
      ele.y = ele.middleRotatePoint.y - ele.h / 2;

      let { radian } = getAngleAndRadian(
        ele.middleRotatePoint,
        middleRotatePoint
      );
      const textDistance = 0;
      //   计算实际两中心点和间距为0时候的差值
      const diffLen = w / 2 + ele.w / 2 - len + textDistance;

      const tx = -diffLen * Math.cos(radian);
      const ty = -diffLen * Math.sin(radian);

      ele.middleRotatePoint = {
        x: ele.middleRotatePoint.x + tx,
        y: ele.middleRotatePoint.y + ty,
      };

      ele.x = ele.x + tx;
      ele.y = ele.y + ty;
    }
  });
  return textList;
}

export {
  transformRotate,
  getPointAndOpposite,
  getNewDraw,
  updatePathPoint,
  generatePathD,
  generatePathPoint,
  getNodesInScope,
  getLeftPosition,
  getNodeLinkList,
  getImageUrl,
  getGroupAreaSize,
  getEdgePosition,
  getNodeAndGroupPosition,
  getIntersectPoint,
  rotatePoint,
  getTransformPosition,
  distanceOfPointAndLine,
  checkPointLeftOrRight,
  getCoordinateByAngle,
  getIntersectionPoint,
  setLinkAdsorb,
  setText,
  alignAndDistanceText,
};
