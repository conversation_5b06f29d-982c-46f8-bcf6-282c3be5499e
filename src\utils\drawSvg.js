import * as d3 from "d3";

const legends = [
  {
    icon: "tyclkzq.svg",
    title: "统一潮流控制器",
  },
  {
    icon: "220kVxdy.svg",
    title: "220kV含小电源变电站",
  },
  {
    icon: "220kVbdz.svg",
    title: "220kV变电站",
  },
  {
    icon: "fdc.svg",
    title: "发电厂",
  },
  {
    icon: "csxn.svg",
    title: "抽水蓄能",
  },
  {
    icon: "storage.svg",
    title: "储能",
  },
  {
    icon: "kgz.svg",
    title: "开关站",
  },
  {
    icon: "hlz.svg",
    title: "换流站",
  },
  {
    icon: "500MVA.svg",
    title: "500MVA",
  },
  {
    icon: "1000MVA.svg",
    title: "1000MVA",
  },
  {
    icon: "750MVA.svg",
    title: "750MVA",
  },
  {
    icon: "tgy3000MVA.svg",
    title: "特高压3000MVA",
  },
  {
    icon: "gfdz.svg",
    title: "光伏电站",
  },
  {
    icon: "hsfd.svg",
    title: "海上风电",
  },
  {
    icon: "lsfd.svg",
    title: "陆上风电",
  },
  {
    icon: "ytc220kVxl.svg",
    title: "已投产220kV线路",
  },
  {
    icon: "xtc220kV500kxl.svg",
    title: "新投产220kV500kV线路",
    color: "#37a14c",
    color1: "#c1006f",
  },
  {
    icon: "ytc500kVxl.svg",
    title: "已投产500kV线路",
    color: "#c30d23",
  },
  {
    icon: "ytc500kV4x400xl.svg",
    title: "已投产500kV4x400线路",
    color: "#c1006f",
  },
  {
    icon: "ytc1000kVxl.svg",
    title: "已投产1000kV线路",
    color: "#8d06f2",
  },
  {
    icon: "ytczlxl.svg",
    title: "已投产直流线路",
    color: "#036EB8",
  },
  {
    icon: "xtczlxl.svg",
    title: "新投产直流线路",
    color: "#27C7FF",
  },
  {
    icon: "byxl.svg",
    title: "备用线路",
  },
  {
    icon: "jianxiu.svg",
    icon1: "ji.svg",
    title: "重大检修",
    color: "#D46B08",
    class: "jianxiu",
  },
];

const legendsByPlan = [
  {
    icon: "220kVbdz.svg",
    title: "220kV变电站",
  },
  {
    icon: "fdc.svg",
    title: "发电厂",
  },
  {
    icon: "kgz.svg",
    title: "开关站",
  },
  {
    icon: "hlz.svg",
    title: "换流站",
  },
  {
    icon: "500MVA.svg",
    title: "500MVA",
  },
  {
    icon: "1000MVA.svg",
    title: "1000MVA",
  },
  {
    icon: "750MVA.svg",
    title: "750MVA",
  },
  {
    icon: "tgy3000MVA.svg",
    title: "特高压3000MVA",
  },
  {
    icon: "ytc220kVxl.svg",
    title: "已投产220kV线路",
  },
  {
    icon: "xtc220kV500kxl.svg",
    iconSummer: "xtc220kV500kxl-summer.svg",
    title: "新投产220kV500kV线路",
    color: "#37a14c",
    color1: "#c1006f",
    color2: "#0700ae",
  },
  {
    icon: "ytc500kVxl.svg",
    title: "已投产500kV线路",
    color: "#c30d23",
  },
  {
    icon: "byxl.svg",
    title: "备用线路",
  },
];

const nameTextSize = 48;
let isDescVisible = false;

// 添加边框
function addBorder(el, x, y, svgSize) {
  let { width, height } = svgSize;
  width = width - 2;
  const heightBorder = height + nameTextSize + 20;
  // 添加外框
  el.append("path")
    .attr(
      "d",
      `M${x} ${y} L${x + width} ${y} L${
        x + width
      } ${heightBorder} L${x} ${heightBorder} z`
    )
    .attr("stroke", "#036EB8")
    .attr("stroke-width", "3")
    .attr("fill", "none");

  // 增肌A4纸的折痕
  // A4纸的尺寸 1123*794
  const scale = height / 1123;
  const widthScale = width / scale;
  //   可打印 的A4纸的数量
  const A4Num = parseInt(widthScale / 794);

  const arr = [1, -1];

  arr.forEach((ele, index) => {
    new Array(A4Num).fill(0).forEach((eleChild, indexChild) => {
      const x = parseInt(794 * (indexChild + 1) * scale);
      const y = heightBorder * index;

      //   分割线长度  上面是
      el.append("path")
        .attr("d", `M${x} ${y} L${x} ${y + 20 * ele} Z`)
        .attr("stroke", "#036EB8")
        .attr("stroke-width", "5")
        .attr("fill", "none");
    });
  });
}

function getNextMonth() {
  const year = new Date().getFullYear();
  const month = new Date().getMonth() + 1;

  let nextMonth = month + 1;
  let nextYear = year;
  if (nextMonth > 12) {
    nextMonth = 1;
    nextYear = year + 1;
  }
  nextMonth = nextMonth < 10 ? `0${nextMonth}` : nextMonth;
  return [nextYear, nextMonth];
}

// 添加左下角名称和信息
function addSvgMeta(metaGroup, svgSize, svgPostion, options) {
  const { height } = svgSize;
  const y = height + nameTextSize + 5;

  metaGroup
    .append("text")
    .text("内 部 资 料")
    .attr("x", "20")
    .attr("y", y - 10)
    .attr("font-size", nameTextSize - 8)
    .style("font-family", "楷体,楷体_GB2312");

  metaGroup
    .append("rect")
    .attr("x", 10)
    .attr("y", y - 49)
    .attr("width", 240)
    .attr("height", 50)
    .attr("stroke", "#000000")
    .attr("stroke-width", "1")
    .attr("fill", "none");

  const nameMap = {
    all: "江南 江北",
    north: "江北",
    south: "江南",
  };

  let title = "接线示意图";
  if (options.isPlan) {
    title = options.isPlanSummber ? "夏季高峰潮流图" : "冬季高峰潮流图";
  }
  const [nextYear, nextMonth] = options.date.split("-");
  metaGroup
    .append("text")
    .text(
      `${nextYear}年${nextMonth}月 江苏电网（${nameMap[svgPostion]}）${title}`
    )
    .attr("x", 280)
    .attr("y", y - 5)
    .attr("font-size", nameTextSize)
    .style("font-family", "'Microsoft YaHei'")
    .style("color", "#000")
    .style("letter-spacing", "-1px")
    .style("font-weight", "bold");
}

let textEl = null;
function getTextWidth(text, size) {
  textEl = textEl ? textEl : document.getElementById("textWidth");

  textEl.style.fontSize = `${size}px`;
  textEl.textContent = text;
  return {
    textWidth: textEl.clientWidth,
    textHieght: textEl.clientHeight,
  };
}

// 添加图例
function generateLegend(metaGroup, svgSize, { isPlan, isPlanSummber }) {
  const { width, height } = svgSize;
  const position = {
    x: 1390,
    y: height + 12,
  };
  const fontSize = 35;
  const legendList = isPlan ? legendsByPlan : legends;
  legendList.forEach((legend) => {
    const { textWidth, textHieght } = getTextWidth(legend.title, fontSize);
    if (position.x + 30 + textWidth > width) {
      position.x = 0;
      position.y += textHieght + 5;
    }
    const group = metaGroup
      .append("g")
      .attr("class", legend.class || "")
      .attr("transform", `translate(${position.x}, ${position.y})`);

    group
      .append("image")
      .attr("class", "custom-image")
      .attr("width", "35")
      .attr("height", "35")
      .attr("href", function () {
        console.log("isPlanSummber", isPlanSummber, legend.iconSummer);
        if (isPlanSummber && legend.iconSummer) {
          return require(`../assets/images/legend/${legend.iconSummer}`);
        } else {
          return require(`../assets/images/legend/${legend.icon}`);
        }
      });

    if (legend.icon1) {
      group
        .append("text")
        .style("font-family", "SimSun")
        .attr("font-size", fontSize)
        .attr("alignment-baseline", "before-edge")
        .attr("x", 33)
        .text("/");

      group
        .append("image")
        .attr("class", "custom-image")
        .attr("width", "35")
        .attr("height", "35")
        .attr("x", 45)
        .attr("href", require(`../assets/images/legend/${legend.icon1}`));
    }
    //   图片和文字的间距
    const px = legend.icon1 ? 83 : 38;
    position.x += px;

    const textEl = group
      .append("text")
      .style("font-family", "SimSun")
      .attr("font-size", fontSize)
      .attr("x", px);

    if (legend.title === "新投产220kV500kV线路") {
      textEl
        .append("tspan")
        .text("新投产220kV")
        .attr("fill", isPlanSummber ? legend.color2 : legend.color)
        .attr("alignment-baseline", "before-edge");
      textEl
        .append("tspan")
        .text("500kV线路")
        .attr("fill", legend.color1)
        .attr("alignment-baseline", "before-edge");
    } else {
      textEl
        .text(legend.title)
        .attr("fill", legend.color)
        .attr("alignment-baseline", "before-edge");
    }

    //   文字和后一个的间距
    position.x += textWidth + 10;
  });
  d3.select(".jianxiu").attr(
    "style",
    `display:${isDescVisible ? "block" : "none"}`
  );
}

function generateMetaGroup(isChecked, id) {
  let group = d3.selectAll(`#${id}`);

  group.remove();

  if (isChecked) {
    group = d3.select("#svgPreview").append("g").attr("id", id);
  }

  return group;
}

// 绘制打印所需的元素
function drawSvgPrintData(isDraw, svgSize, svgPostion, options) {
  const metaGroup = generateMetaGroup(isDraw, "metaGroup");

  if (isDraw) {
    addSvgMeta(metaGroup, svgSize, svgPostion, options);
    addBorder(metaGroup, 1, 1, svgSize);
    generateLegend(metaGroup, svgSize, options);
    d3.select("#svgPreview").style("height", svgSize.height + 69);
  } else {
    d3.select("#svgPreview").style("height", svgSize.height);
  }
}

function clipSvg(w, h, svgPostion, moveY) {
  let clipGroup = d3.select("#clipGroup");

  clipGroup.remove();

  if (svgPostion === "all") return;

  clipGroup = d3.select("#svgPreview").append("g").attr("id", "clipGroup");

  clipGroup
    .append("defs")
    .append("clipPath")
    .attr("id", "clipPath")
    .append("rect")
    .attr("x", 0)
    .attr("y", moveY)
    .attr("width", w)
    .attr("height", h);

  d3.select("#svgPreview .topo-component-list").attr(
    "clip-path",
    "url(#clipPath)"
  );
}

function convertImgToBase64(url) {
  return new Promise((resolve, reject) => {
    let canvas = document.createElement("canvas"),
      ctx = canvas.getContext("2d"),
      img = new Image();
    // 服务端也要设置允许跨域
    img.crossOrigin = "Anonymous";
    img.src = url;
    img.onload = function () {
      canvas.height = img.height;
      canvas.width = img.width;
      ctx.drawImage(img, 0, 0);
      let dataURL = canvas.toDataURL("image/png");
      canvas = null;
      resolve(dataURL);
    };
    img.onerror = function () {
      reject("");
    };
  });
}

// 注释栏
function drawSvgDescription(
  isDrawDescription,
  isDrawLenged,
  svgSize,
  description
) {
  const descriptionWidth = 1131;
  //   高度打印时缩小了1/3， 6mm再96dpi屏幕下是 22.67px 所以 打印的时候是68.01px
  const distance = 68;
  isDescVisible = isDrawDescription;
  let { width, height } = svgSize;
  width = isDrawDescription ? width + descriptionWidth + distance + 1 : width;

  const descriptionGroup = generateMetaGroup(
    isDrawDescription,
    "descriptionGroup"
  );

  d3.select("#svgPreview").style("width", width);
  d3.select(".jianxiu").attr(
    "style",
    `display:${isDescVisible ? "block" : "none"}`
  );

  if (!isDrawDescription) return;

  const x = svgSize.width + distance;
  const y = 1;

  const borderHeight = isDrawLenged ? height + nameTextSize + 20 : height - 1;

  descriptionGroup
    .append("path")
    .attr(
      "d",
      `M${x} ${y} L${x + descriptionWidth} ${y} L${
        x + descriptionWidth
      } ${borderHeight} L${x} ${borderHeight} z`
    )
    .attr("stroke", "#036EB8")
    .attr("stroke-width", "3")
    .attr("fill", "none");

  descriptionGroup
    .append("foreignObject")
    .attr("id", "svgDescription")
    .attr("width", descriptionWidth)
    .attr("height", height)
    .attr("x", svgSize.width + distance)
    .attr("y", 0)
    .append("xhtml:div")
    .attr("class", "editor-content-view")
    .style("color", "#000000")
    .style("font-family", "楷体_GB2312")
    .style("padding", "20px")
    .attr("xmlns", "http://www.w3.org/1999/xhtml")
    .html(description);
}

// 图标绘制
function drawStationIcon(xfmrMvarates = []) {
  xfmrMvarates = xfmrMvarates.map((ele) => parseInt(ele));
  const el = document.createDocumentFragment();
  const svg = d3
    .select(el)
    .append("svg")
    .attr("xmlns", "http://www.w3.org/2000/svg")
    .attr("viewBox", "-2 -2 204 204");

  const len = xfmrMvarates.length;

  const colors = {
    500: "#1b3b6b",
    750: "#0cb3e3",
    1000: "#c30d23",
    3000: "#8d06f2",
  };
  const colorBg = xfmrMvarates.includes(3000) ? "#8d06f2" : "#c30d23";
  if (len <= 3) {
    const dList = [
      "M32.82,32.82Q5,60.65,5,100t27.82,67.17L100,100Z",
      "M32.08,33.57Q59.91,5.74,99.25,5.75t67.18,27.82L99.25,100.75Z",
      "M165.68,32.82Q193.51,60.65,193.5,100t-27.82,67.17L98.5,100Z",
    ];

    // 生成内部填充色
    xfmrMvarates.reverse().forEach((ele, index) => {
      let d = dList[index];
      if (len === 2 && index === len - 1) {
        d = dList[index + 1];
      }
      svg
        .append("path")
        .attr("d", d)
        .style("fill", colors[ele] || "#c30d23");
    });

    // 生成外框
    svg
      .append("path")
      .attr(
        "d",
        "M100,0A100,100,0,1,0,200,100,100,100,0,0,0,100,0Zm7.07,100,60-60a90,90,0,0,1,0,120ZM160,32.93l-60,60-60-60a90,90,0,0,1,120,0ZM32.93,40l60,60-60,60a90,90,0,0,1,0-120ZM40,167.07l60-60,60,60a90,90,0,0,1-120,0Z"
      )
      .style("fill", colorBg)
      .style("stroke-width", 4)
      .style("stroke", colorBg);
  } else {
    // 四个的 目前只有紫色
    svg
      .append("path")
      .attr(
        "d",
        "M100,0A100,100,0,1,0,200,100,100,100,0,0,0,100,0ZM43.85,172A91.74,91.74,0,0,1,28,156.15l64.41-48.57ZM28,43.85A91.74,91.74,0,0,1,43.85,28L92.42,92.42ZM156.15,28A91.74,91.74,0,0,1,172,43.85L107.58,92.42Zm0,144-48.57-64.42L172,156.15A91.74,91.74,0,0,1,156.15,172Z"
      )
      .style("fill", colorBg)
      .style("stroke-width", 4)
      .style("stroke", colorBg);
  }
  const dataUrl =
    "data:image/svg+xml;charset=utf-8," + encodeURI(svg.node().outerHTML);

  return dataUrl;
}

export {
  drawSvgPrintData,
  clipSvg,
  convertImgToBase64,
  drawSvgDescription,
  drawStationIcon,
  getNextMonth,
};
