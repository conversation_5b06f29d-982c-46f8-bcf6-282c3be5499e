<template>
  <Modal
    v-model="isMapFileVisible"
    draggable
    scrollable
    title="文件列表"
    :closable="false"
    :mask="false"
    :footer-hide="true"
    width="350"
    :z-index="6"
    :styles="{ marginLeft: '0', top: '60px' }"
    class-name="base-modal"
  >
    <div class="topo-map-header">
      <div>
        <Icon
          class="click"
          type="md-add"
          size="25"
          @click.stop="showAddMapModal"
        />
        <Icon
          v-if="activeName === 'province'"
          class="click"
          type="md-folder-open"
          size="25"
          @click.stop="$refs.provinceListRef.addMenu()"
          style="margin-left: 15px"
        />
      </div>

      <Input
        suffix="ios-search"
        placeholder="查找图层"
        style="width: auto; margin-left: 15px; width: 135px"
        clearable
        @on-change="handleMapSearch"
      />
    </div>

    <!-- <Tabs v-model="activeName" style="height: 100%" @on-click="handleTabClick"> -->
    <!-- <TabPane label="省调" name="province"> -->
    <ProvinceList ref="provinceListRef"></ProvinceList>
    <!-- </TabPane>
      <TabPane label="地市" name="city">
        <CityList ref="cityListRef"></CityList>
      </TabPane>
    </Tabs> -->
  </Modal>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";

import ProvinceList from "./List/Province.vue";
// import CityList from "./List/City.vue";
import { Draggable } from "@/utils/directive/drag";

export default {
  components: { ProvinceList },
  directives: { Draggable },

  data() {
    return {
      nodeList: [],
      menuList: [],
      activeName: "province",
    };
  },
  mounted() {},
  beforeDestroy() {
    this.setVersionSelected();
  },

  computed: {
    ...mapState("topoStore", {
      mapList: "mapList",
      isMapFileVisible: "isMapFileVisible",
    }),
  },
  methods: {
    ...mapMutations("topoStore", ["setVersionSelected"]),
    addMenu() {
      this.$refs.addMenuModal.show();
    },
    // 查找图层
    handleMapSearch(e) {
      const val = e.target.value;
      if (this.activeName === "province") {
        this.$refs.provinceListRef.search(val);
        this.$refs.cityListRef.search();
      } else {
        this.$refs.provinceListRef.search();
        this.$refs.cityListRef.search(val);
      }
    },
    handleTabClick(val) {
      //   this.$refs.provinceListRef.search();
      //   this.$refs.cityListRef.search();
      if (val === "province") {
        this.$refs.provinceListRef.fetchMenuList();
      } else {
        this.$refs.cityListRef.fetchMenuList();
      }
    },
    showAddMapModal() {
      if (this.activeName === "province") {
        this.$refs.provinceListRef.addMap();
      } else {
        this.$refs.cityListRef.addMap();
      }
    },
  },
};
</script>
<style lang="scss">
.topo-map-header {
  border-bottom: 1px solid #ccc;
  padding: 10px 5px 5px 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.topo-map-list {
  padding: 0;
  overflow: auto;
  display: flex;
  flex: 1;
  flex-direction: column;
}
.topo-map-list-item {
  list-style: none;
  width: 100%;
  display: flex;
  align-items: center;
  text-align: left;
  padding: 5px 10px;
  cursor: pointer;
}
.topo-map-list-item-active {
  background-color: rgb(225, 241, 250);
}
.topo-el-list {
  height: 540px;
  text-align: left;
  padding-left: 5px;
  overflow: auto;
}
.topo-import {
  position: relative;
  color: #515a6e;
  text-indent: 0;
  padding: 0;
  cursor: pointer;
  &:hover {
    color: #515a6e;
  }
  input {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    opacity: 0;
    cursor: pointer;
  }
}
.topo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
