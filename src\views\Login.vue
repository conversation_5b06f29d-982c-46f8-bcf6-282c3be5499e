<template>
  <div class="login">
    <div class="login-container">
      <Form
        :model="loginForm"
        :rules="fieldRules"
        class="demo-ruleForm"
        label-position="left"
        ref="loginForm"
      >
        <h2 class="title" style="padding-left: 22px">系统登录</h2>
        <FormItem prop="account">
          <Input
            placeholder="账号"
            type="text"
            v-model="loginForm.username"
            @keyup.enter.native="login()"
          >
            <Icon slot="prepend" type="ios-person-outline"></Icon>
          </Input>
        </FormItem>
        <FormItem prop="password">
          <Input
            placeholder="密码"
            type="password"
            v-model="loginForm.password"
            @keyup.enter.native="login()"
          >
            <Icon slot="prepend" type="ios-lock-outline"></Icon>
          </Input>
        </FormItem>
        <!-- <el-checkbox v-model="checked" checked class="remember">记住密码</el-checkbox> -->
        <FormItem style="width: 100%">
          <Button @click.native.prevent="reset" style="width: 30%"
            >重 置</Button
          >
          <Button
            @click.native.prevent="login"
            style="width: 30%; margin-left: 5%"
            type="primary"
            >登 录</Button
          >
          <Button
            @click.native.prevent="login('preview')"
            style="width: 30%; margin-left: 5%"
            type="primary"
            >预 览</Button
          >
        </FormItem>
      </Form>
    </div>
    <MapListModal ref="mapListModal"></MapListModal>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import MapListModal from "@/components/Topo/Modal/MapListModal.vue";

export default {
  name: "Login",
  components: { MapListModal },
  data() {
    return {
      loading: false,
      loginForm: {
        username: "admin",
        password: "123456",
        role: 3,
      },
      fieldRules: {
        username: [{ required: true, message: "请输入账号", trigger: "    " }],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      checked: true,
      sessionID: "",
    };
  },
  methods: {
    login(type) {
      this.loading = true;
      let userInfo = {
        userName: this.loginForm.username,
        password: this.loginForm.password,
      };

      this.$post("/login", userInfo).then((res) => {
        if (res.code === "0000") {
          Cookies.set("token", res.data.sessionId); // 放置token到Cookie
          Cookies.set("displayName", res.data.displayName);
          this.loading = false;
          if (type === "preview") {
            this.$refs.mapListModal.show();
          } else {
            window.localStorage.setItem("routes", res.data.routes);
            this.$Message.success("登录成功");
            this.$router.push("/");
          }
        } else {
          this.loading = false;
        }
      });
    },
    reset() {
      this.$refs.loginForm.resetFields();
    },
  },
  mounted() {
    document.onclick = null;
    document.onkeydown = null;
  },
};
</script>

<style lang="scss" scoped>
.login {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("@/assets/images/login-bg.jpg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.login-container {
  position: absolute;
  top: 25%;
  right: 15%;
  border-radius: 5px;
  background-clip: padding-box;
  margin: 100px auto;
  width: 400px;
  padding: 35px 35px 15px 35px;
  background: $cardBg;
  border: 1px solid $borderColor;
  box-shadow: 0 0 25px #cac6c6;
  // opacity: 0.9;

  .title {
    margin: 0 auto 30px auto;
    text-align: center;
    color: #505458;
  }

  .remember {
    margin: 0 0 35px 0;
  }
}
</style>
