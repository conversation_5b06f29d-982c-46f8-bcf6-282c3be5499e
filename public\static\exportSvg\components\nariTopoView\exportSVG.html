<!--
 * @Author: 陆晓夫
 * @Date: 2022-03-01 10:22:52
 * @LastEditors: 陆晓夫
 * @LastEditTime: 2022-05-24 12:41:24
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <link rel="stylesheet" type="text/css" href="v1.0/nariTopoView.css" />
    <link rel="stylesheet" type="text/css" href="../baseComponents/css/fonts.css" />
  </head>
  <body style="background-color: #000000">
    <input id="exportX" class="exportInput" placeholder="x" value="0" />
    <input id="exportY" class="exportInput" placeholder="y" value="0" />
    <input id="exportW" class="exportInput" placeholder="w" value="9800" />
    <input id="exportH" class="exportInput" placeholder="h" value="3150" />
    <button onclick="exportSVG()">导出为svg</button>
    <div id="con"></div>
    <script src="https://unpkg.com/@svgdotjs/svg.js"></script>
    <script src="../../WisVisual/libs/d3.v5.min.js"></script>
    <script src="../../WisVisual/libs/anime.min.js"></script>
    <script src="../../WisVisual/libs/jquery.min.js"></script>
    <script src="../../WisVisual/libs/lodash.min.js"></script>
    <script src="../../WisVisual/Util/CompUtil.js"></script>
    <script src="../../WisVisual/Util/Util.js"></script>
    <script src="../../WisVisual/Util/Util.js"></script>
    <script src="../../WisVisual/libs/reconnecting-websocket.min.js"></script>
    <script src="../../WisVisual/libs/stomp.js"></script>
    <script src="../../WisVisual/API/API.js"></script>
    <script src="../base/optionType.js"></script>
    <script src="../base/componentBase.js"></script>
    <script src="../imageView/v1.0/imageView.js"></script>
    <script src="../powerPlantStateBoard/v1.0/powerPlantStateBoard.js"></script>
    <script src="../cylinderBoard/v1.0/cylinderBoard.js"></script>
    <script src="../digitGearBoard/v1.0/digitGearBoard.js"></script>
    <script src="v1.0/nariTopoView.js"></script>
    <script>
      let x = 0;
      let y = 0;
      let w = 0;
      let h = 0;

      $('.exportInput').on('change', () => {
        x = $('#exportX').val();
        y = $('#exportY').val();
        w = $('#exportW').val();
        h = $('#exportH').val();
        d3.select('.mainSVG').attr('viewBox', `${x} ${y} ${w} ${h}`);
      });

      function exportSVG() {
        x = $('#exportX').val();
        y = $('#exportY').val();
        w = $('#exportW').val();
        h = $('#exportH').val();
        d3.select('.mainSVG').attr('viewBox', `${x} ${y} ${w} ${h}`).style('background-color', 'black');
        let draw = SVG('.mainSVG');
        let str = draw.svg((node) => {
          // if (node.type === 'image') return false;
        });
        createAndDownloadFile('temp.svg', str);
      }

      let mapId = getUrlParam('mapId');
      // window.initAPIPromise.then(() => {
      draw();
      // });

      function getUrlParam(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
      }

      function draw() {
        window.currentSceneId = '225';
        console.log(mapId);
        if (mapId === null) {
          mapId = 'z7JNR0oFvv';
        }
        let obj  = new NariTopoView('858', '123', document.getElementById('con'), 0, {
          property: {
            basic: {
              frame: [0, 0, 9800, 3150],
            },
            viewSetting: {
              loadMapId: mapId,
              w: 9800,
              h: 3150,
            },
          },
        });
        obj._drawLinks(obj.topoData.links, true);
        obj._drawNodes(obj.topoData.nodes, true);
        for (const key in obj.powerPlantMap) {
          obj.powerPlantMap[key].setType('l');
        }
        for (const key in obj.transStationMap) {
          obj.transStationMap[key].setType('l');
        }
      }

      function createAndDownloadFile(fileName, content) {
        var aTag = document.createElement('a');
        var blob = new Blob([content]);
        aTag.download = fileName;
        aTag.href = URL.createObjectURL(blob);
        aTag.click();
        URL.revokeObjectURL(blob);
      }
    </script>
  </body>
</html>
