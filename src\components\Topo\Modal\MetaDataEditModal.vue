<template>
  <Modal v-model="isVisible" title="编辑MetaData" :width="1100" border>
    <Table border :columns="columns" :data="tableData" :height="500">
      <template #key="{ row, index }">
        <Input
          :value="row.key"
          @on-change="handleInoutChange($event, index, 'key')"
        ></Input>
      </template>
      <template #value="{ row, index }">
        <Input
          :value="formatValue(row.value)"
          @on-change="handleInoutChange($event, index, 'value')"
        ></Input>
      </template>
      <template #description="{ row }">
        <span>{{ getDescription(row) }}</span>
      </template>
      <template #action="{ index }">
        <Button
          type="error"
          size="small"
          @click="deleteRow(index)"
          style="margin-left: 10px"
          >删除</Button
        >
      </template>
    </Table>
    <div slot="footer">
      <Button type="primary" @click="addRow" style="float: left">新增</Button>
      <Button type="primary" @click="updateMetaData" style="float: left"
        >调整</Button
      >
      <Button type="primary" @click="editMultitMetaData" style="float: left"
        >批量修改</Button
      >
      <Button type="text" @click="closeModal">取消</Button>
      <Button type="primary" @click="submitForm">确定</Button>
    </div>
    <UpdateMetaDataModal ref="updateMetaDataModalRef"></UpdateMetaDataModal>
  </Modal>
</template>

<script>
import { mapState, mapActions } from "vuex";
import { nodeLinkDescriptionMap } from "../constant";
import { getHttpType } from "@/store/assistant/";
import UpdateMetaDataModal from "@/components/Topo/Modal/Multi/UpdateMetaDataModal.vue";

export default {
  name: "MetaDataEditModal",
  components: { UpdateMetaDataModal },
  props: {
    mapId: {
      type: String,
      default: "",
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      isVisible: false,
      tableData: [],
      columns: [
        {
          title: "Key",
          slot: "key",
          width: 350,
          resizable: true,
        },
        {
          title: "Value",
          slot: "value",
          width: 400,
          resizable: true,
        },
        {
          title: "描述",
          slot: "description",
        },
        {
          title: "操作",
          slot: "action",
          width: 100,
        },
      ],
    };
  },
  beforeDestroy() {
    this.$bus.off("onMapSlected");
  },
  computed: {
    ...mapState("topoStore", {}),
  },
  methods: {
    ...mapActions("topoStore", []),
    initEvent() {
      this.$bus.on("onMapSlected", () => {
        this.closeModal();
      });
    },
    show() {
      this.isVisible = true;

      const { metaData = {} } = this.info;
      this.generateTableData(metaData);
    },
    // 定义比较函数
    compare(a, b) {
      // 提取英文部分
      const aStr = a.key.replace(/[0-9]/g, "");
      const bStr = b.key.replace(/[0-9]/g, "");

      // 比较英文部分
      if (aStr < bStr) {
        return -1;
      }
      if (aStr > bStr) {
        return 1;
      }
      let aNum = a.key.replace(/[^0-9]/g, "");
      let bNum = b.key.replace(/[^0-9]/g, "");
      if (aNum === "" || aNum === null || aNum === undefined) {
        aNum = -1;
      }
      if (bNum === "" || aNum === null || aNum === undefined) {
        bNum = -1;
      }
      aNum = parseInt(aNum);
      bNum = parseInt(bNum);

      // 比较数字部分
      if (aNum < bNum) {
        return -1;
      }
      if (aNum > bNum) {
        return 1;
      }
    },
    generateTableData(metaData) {
      let list = [];
      for (const key in metaData) {
        list.push({
          key,
          value: metaData[key],
        });
      }
      this.tableData = list.sort(this.compare);
    },
    addRow() {
      this.tableData.push({
        key: "",
        value: "",
      });
    },
    editMultitMetaData() {
      const item = this.getNodeLinkInfo();
      this.$refs.updateMetaDataModalRef.show(item);
    },
    updateMetaData() {
      const params = {
        vtype: getHttpType(),
      };
      this.tableData.forEach((ele) => {
        if (ele.key) {
          params[ele.key] = ele.value;
        }
      });
      this.$post("/data/generateMetaData", params).then((result) => {
        this.generateTableData(result.data);
      });
    },
    deleteRow(index) {
      this.tableData.splice(index, 1);
    },
    getNodeLinkInfo() {
      let metaData = {};
      this.tableData.forEach((ele) => {
        if (ele.key) {
          metaData[ele.key] = ele.value;
        }
      });
      return {
        ...this.info,
        metaData,
      };
    },
    submitForm() {
      const item = this.getNodeLinkInfo();
      this.$bus.emit("updateNodeSelectedList", item);
      this.closeModal();
    },
    handleInoutChange(e, index, type) {
      const { value } = e.target;
      this.tableData[index][type] = value;
    },
    closeModal() {
      this.isVisible = false;
    },
    getDescription(row) {
      const { key, value } = row;
      if (key.includes("rtKeyId")) {
        const str = key.substring(7);
        return nodeLinkDescriptionMap.rtKeyId + ":" + str;
      } else if (key.includes("mvarate")) {
        return nodeLinkDescriptionMap.mvarate;
      } else if (key === "powerType" || key === "type") {
        return nodeLinkDescriptionMap[key][value];
      }
      const description = nodeLinkDescriptionMap[key];
      return description;
    },
    formatValue(val) {
      if (Array.isArray(val)) {
        return val.toString();
      } else {
        return val;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ivu-form-item {
  margin-bottom: 5px;
}
</style>
