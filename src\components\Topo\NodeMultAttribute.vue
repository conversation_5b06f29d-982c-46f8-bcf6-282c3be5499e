<template>
  <div class="topo-attribute-layout">
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">模型</Col>
      <Col span="20" class="text-left">
        <Button type="primary" size="small" @click="handleUpdateMetaData">
          覆盖
        </Button>
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">数据</Col>
      <Col span="20" class="text-left">
        <Button type="primary" size="small" @click="setLinkMetaType">
          设置线路类型
        </Button>
      </Col>
    </Row>

    <div v-show="!hasLink">
      <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
        <Col span="4" class="text-left">角度</Col>
        <Col span="20" class="text-left">
          <InputNumber
            size="small"
            v-model="rotate"
            :max="360"
            :min="-360"
            class="mr-5"
            @on-change="handleInputChange($event, 'rotate')"
          ></InputNumber>
          <Button type="primary" size="small" @click="handleRotateClick">
            设置
          </Button>
        </Col>
      </Row>
    </div>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">绑定</Col>
      <Col span="20" class="text-left">
        <InputNumber
          size="small"
          :min="0"
          class="mr-5"
          v-model="linkPositionValue"
        ></InputNumber>
        <Button type="primary" size="small" @click="handleBindLinkClick">
          设置
        </Button>
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">吸附</Col>
      <Col span="20" class="text-left">
        <Button
          type="primary"
          size="small"
          style="margin-right: 10px"
          @click="handleAdsorbClick('circle')"
        >
          圆形吸附
        </Button>
        <Button type="primary" size="small" @click="handleAdsorbClick('rect')">
          矩形吸附
        </Button>
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">文字</Col>
      <Col span="20" class="text-left">
        <Button type="primary" size="small" @click="handleDiatanceTextClick()">
          对齐
        </Button>
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">调整</Col>
      <Col span="20" class="text-left">
        <Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleAdjustTextClick('Paralle')"
        >
          平行
        </Button>
        <Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleAdjustTextClick('Distance')"
        >
          等距
        </Button>
        <Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleAdjustTextClick('AlignAndDistance')"
        >
          对齐
        </Button>
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left mb-5">发光</Col>
      <Col span="20" class="mb-5">
        <Row type="flex" align="middle">
          <Col span="6" class="mr-5">
            <ColorPicker
              class="link-drop-shadow-picker"
              size="small"
              alpha
              v-model="dropShadowColor"
              @on-change="handleStyleInputChange($event, 'dropShadowColor')"
            />
          </Col>
          <Col span="6">
            <InputNumber
              size="small"
              :min="0"
              @on-change="
                handleStyleInputChange($event, 'dropShadowBlurRadius')
              "
            ></InputNumber>
          </Col>
        </Row>
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left mb-5">尺寸</Col>
      <Col span="20" class="mb-5">
        <Row type="flex" align="middle">
          <Col span="6" class="mr-5">
            <InputNumber
              size="small"
              style="width: 100%"
              v-model="multiSize.w"
              :step="-1"
              type="number"
            ></InputNumber>
          </Col>
          <Col span="6" class="mr-5">
            <InputNumber
              size="small"
              style="width: 100%"
              v-model="multiSize.h"
              :step="-1"
              type="number"
            ></InputNumber>
          </Col>
          <Col span="6">
            <Button
              type="primary"
              size="small"
              @click="handleChangeSizeClick('AlignAndDistance')"
            >
              设置
            </Button>
          </Col>
        </Row>
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">节点</Col>
      <Col span="20" class="text-left" style="display: flex">
        <Button type="primary" size="small" @click="handleParallelLinkClick">
          平行
        </Button>
        <InputNumber
          style="width: 50px; margin-left: 10px"
          size="small"
          :value="rotationOffset"
          @on-change="handleRotationOffsetChange"
        ></InputNumber>
      </Col>
    </Row>

    <!-- 连线的批量操作 -->
    <div v-if="isLink">
      <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
        <Col span="4" class="text-left">等距</Col>
        <Col span="8" class="text-left">
          <InputNumber
            class="topo-muil-attr-input"
            size="small"
            v-model="distanceAlign"
            :min="0"
          ></InputNumber>
        </Col>
        <Col span="8" class="text-left pl-5">
          <Button type="primary" size="small" @click="handleAlignLink">
            设置
          </Button>
        </Col>
      </Row>

      <Row type="flex" align="middle" class="mt-5" style="padding: 0 16px">
        <Col span="4" class="text-left">线宽</Col>
        <Col span="8" class="text-left">
          <InputNumber
            class="topo-muil-attr-input"
            size="small"
            v-model="linkWidth"
            :min="0.01"
          ></InputNumber>
        </Col>
        <Col span="8" class="text-left pl-5">
          <Button type="primary" size="small" @click="handleSetLinkWidth">
            设置
          </Button>
        </Col>
      </Row>
      <Row type="flex" align="middle" class="mt-5" style="padding: 0 16px">
        <Col span="4" class="text-left">线型</Col>
        <Col span="8" class="text-left">
          <Select
            size="small"
            style="text-align: left"
            @on-change="handleLinkDashChange"
          >
            <Option
              v-for="(item, index) in dasharrayOptions"
              :value="index"
              :key="index"
              >{{ item.label }}</Option
            >
          </Select>
        </Col>
      </Row>
      <Row type="flex" align="middle" class="mt-5" style="padding: 0 16px">
        <Col span="4" class="pr-5">操作</Col>
        <Col span="20" style="text-align: left">
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleSetLinkClick('reverse')"
            >反向</Button
          >
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleSetLinkClick('dotted-line')"
            >虚线</Button
          >
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleSetLinkClick('line')"
            >实线</Button
          >
        </Col>
      </Row>
      <Row type="flex" align="middle" class="mt-5" style="padding: 0 16px">
        <Col span="4" class="pr-5">箭头</Col>
        <Col span="20" style="text-align: left">
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleSetLinkArrowClick('start')"
            >起点</Button
          >
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleSetLinkArrowClick('end')"
            >终点</Button
          >
          <Button type="primary" size="small" @click="handleSetLinkArrowClick()"
            >取消</Button
          >
        </Col>
      </Row>
      <!-- <Row type="flex" align="middle" class="mt-5" style="padding: 0 16px">
        <Col span="4" class="pr-5">变色</Col>
        <Col span="20" style="text-align: left">
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleChangeColorClick('green')"
            >绿</Button
          >
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleChangeColorClick('pink')"
            >粉</Button
          >
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleChangeColorClick('orange')"
            >橙</Button
          >
          <Button
            type="primary"
            size="small"
            class="mr-5"
            @click="handleChangeColorClick('blue')"
            >蓝</Button
          >
          <Button
            type="primary"
            size="small"
            @click="handleChangeColorClick('reset')"
            >重置</Button
          >
        </Col>
      </Row> -->
    </div>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">字号</Col>
      <Col span="7" class="text-left">
        <InputNumber
          size="small"
          v-model="fontSize"
          :min="0"
          @on-change="handleInputChange($event, 'fontSize')"
        ></InputNumber>
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">字色</Col>
      <Col span="7" class="text-left">
        <ColorPicker
          class="node-mult-font-color-picker"
          size="small"
          alpha
          v-model="fontColor"
          @on-change="handleInputChange($event, 'fontColor')"
      /></Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5" style="padding: 0 16px">
      <Col span="4" class="text-left">变色</Col>
      <Col span="20" class="text-left">
        <Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleChangeColorClick('green')"
          >绿</Button
        >
        <Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleChangeColorClick('pink')"
          >粉</Button
        >
        <Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleChangeColorClick('orange')"
          >橙</Button
        >
        <Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleChangeColorClick('blue')"
          >蓝</Button
        >
        <Button
          type="primary"
          size="small"
          @click="handleChangeColorClick('reset')"
          >重置</Button
        >
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5" style="padding: 0 16px">
      <Col span="4" class="text-left"> 分排 </Col>
      <Col span="7">
        <Tooltip content="长度" class="mr-5">
          <InputNumber
            size="small"
            v-model="line.length"
            :step="-1"
            style="width: 100%"
            type="number"
            :min="0"
            @on-change="handlLineStylesChange($event, 'length')"
          ></InputNumber>
        </Tooltip>
      </Col>
      <Col span="7" class="mr-5">
        <Tooltip content="角度">
          <InputNumber
            size="small"
            v-model="line.rotate"
            :step="-1"
            style="width: 100%"
            type="number"
            :max="360"
            :min="-360"
            @on-change="handlLineStylesChange($event, 'rotate')"
          ></InputNumber>
        </Tooltip>
      </Col>
      <Col span="5">
        <Button type="primary" size="small" @click="handleLineStylesClick()"
          >设置</Button
        >
      </Col>
    </Row>
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">对齐</Col>
      <Col span="20" class="text-left">
        <Button
          v-for="(item, index) in alignTextList"
          class="mr-5 mb-5"
          :key="item"
          @click="handleAlignClick(index)"
        >
          {{ item }}
        </Button>
      </Col>
    </Row>
    <Row
      type="flex"
      align="middle"
      class="gradient-content"
      style="padding: 0 16px"
    >
      <Col span="4" class="text-left mb-5">渐变</Col>
      <Col span="20" class="mb-5">
        <Row type="flex" align="middle">
          <Col span="6" class="text-right"
            ><Tooltip content="是否开启"> 开关 &nbsp;</Tooltip></Col
          >
          <Col span="8">
            <i-switch
              id="switchDrawBlock"
              v-model="gradient.isGradient"
              @on-change="handleStyleInputChange($event, 'isGradient')"
              size="small"
            />
          </Col>
        </Row>
      </Col>
      <Col span="20" offset="4" class="mb-5">
        <Row type="flex" align="middle">
          <Col span="6" class="text-right"> 角度 &nbsp;</Col>
          <Col span="8">
            <InputNumber
              size="small"
              :min="0"
              :max="360"
              :step="10"
              v-model="gradient.gradientAngle"
              @on-change="handleGradientAngleInputChange"
            ></InputNumber>
          </Col>
        </Row>
      </Col>
      <Col span="20" offset="4" class="mb-5">
        <Row type="flex" align="middle">
          <Col span="6" class="text-right"
            ><Tooltip content="颜色1设置"> 颜色1 &nbsp;</Tooltip></Col
          >
          <Col span="8">
            <ColorPicker
              class="node-mult-gradient-picker"
              size="small"
              alpha
              v-model="gradient.gradientColor1"
              @on-change="handleStyleInputChange($event, 'gradientColor1')"
            />
          </Col>
          <Col span="8" offset="2">
            <InputNumber
              size="small"
              :min="0"
              :max="1"
              :step="0.1"
              v-model="gradient.gradientOffset1"
              @on-change="handleStyleInputChange($event, 'gradientOffset1')"
            ></InputNumber>
          </Col>
        </Row>
      </Col>
      <Col offset="4" span="20" class="mb-5">
        <Row type="flex" align="middle">
          <Col span="6" class="text-right"
            ><Tooltip content="颜色2设置"> 颜色2 &nbsp;</Tooltip></Col
          >
          <Col span="8">
            <ColorPicker
              class="node-mult-gradient-picker"
              size="small"
              alpha
              v-model="gradient.gradientColor2"
              @on-change="handleStyleInputChange($event, 'gradientColor2')"
            />
          </Col>
          <Col span="8" offset="2">
            <InputNumber
              size="small"
              :min="0"
              :max="1"
              :step="0.1"
              v-model="gradient.gradientOffset2"
              @on-change="handleStyleInputChange($event, 'gradientOffset2')"
            ></InputNumber>
          </Col>
        </Row>
      </Col>
      <Col offset="4" span="20" class="mb-5">
        <Row type="flex" align="middle">
          <Col span="6" class="text-right"
            ><Tooltip content="颜色3设置"> 颜色3 &nbsp;</Tooltip></Col
          >
          <Col span="8">
            <ColorPicker
              class="node-mult-gradient-picker"
              size="small"
              alpha
              v-model="gradient.gradientColor3"
              @on-change="handleStyleInputChange($event, 'gradientColor3')"
            />
          </Col>
          <Col span="8" offset="2">
            <InputNumber
              size="small"
              :min="0"
              :max="1"
              :step="0.1"
              v-model="gradient.gradientOffset3"
              @on-change="handleStyleInputChange($event, 'gradientOffset3')"
            ></InputNumber>
          </Col>
        </Row>
      </Col>
    </Row>
    <UpdateMetaDataModal ref="updateMetaDataModalRef"></UpdateMetaDataModal>
  </div>
</template>
<script>
import { mapState, mapMutations } from "vuex";
import UpdateMetaDataModal from "@/components/Topo/Modal/Multi/UpdateMetaDataModal.vue";
import {
  getCoordinateByAngle,
  setLinkAdsorb,
  setText,
  alignAndDistanceText,
} from "./utils";

import { dasharrayOptions } from "./constant";

export default {
  components: { UpdateMetaDataModal },
  props: {
    nodeSelectedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      rotate: 0,
      linkWidth: 1,
      distanceAlign: 10,
      fontSize: 12,
      dropShadowColor: "#ffffff",
      fontColor: "#ffffff",
      gradient: {
        isGradient: false,
        gradientAngle: null,
        gradientColor1: "#ffffff",
        gradientOffset1: 0,
        gradientColor2: "#ffffff",
        gradientOffset2: 0,
        gradientColor3: "#ffffff",
        gradientOffset4: 0,
      },
      dasharrayOptions,
      linkPositionValue: 1,
      alignTextList: [
        "左",
        "右",
        "顶",
        "底",
        "水平居中",
        "垂直居中",
        "水平等间距",
        "垂直等间距",
      ],
      multiSize: {
        w: 0,
        h: 0,
      },
      dasharray: "",
      line: {
        length: 100,
        rotate: 0,
      },
      mapInfo: {},
      collapseVal: ["linkParallel"],
    };
  },
  mounted() {},
  watch: {},
  computed: {
    ...mapState("topoStore", {
      mapList: "mapList",
      rotationOffset: "rotationOffset",
    }),
    hasLink() {
      return this.nodeSelectedList
        .map((ele) => !!ele.linkId)
        .filter((ele) => !!ele).length;
    },
    isLink() {
      const len = this.nodeSelectedList
        .map((ele) => !!ele.linkId)
        .filter((ele) => !!ele).length;
      return len === this.nodeSelectedList.length;
    },
    isText() {
      const list = this.nodeSelectedList
        .map((ele) => ele.nodeType)
        .filter((ele) => ele === "text");
      return new Set(list).size === 1;
    },
  },
  methods: {
    ...mapMutations("topoStore", ["setSelectLinkObj", "setRotationOffset"]),
    handleGradientAngleInputChange(val) {
      const { x1, y1, x2, y2 } = getCoordinateByAngle(+val);
      this.nodeSelectedList.forEach((ele) => {
        if (ele.nodeId) {
          let { textStyles } = ele;
          textStyles = textStyles ? JSON.parse(textStyles) : {};
          textStyles.gradientX1 = x1;
          textStyles.gradientY1 = y1;
          textStyles.gradientX2 = x2;
          textStyles.gradientY2 = y2;
          textStyles.gradientAngle = val;
          ele.textStyles = JSON.stringify(textStyles);
        }
      });
      this.updateNodeInfo();
    },
    handleUpdateMetaData() {
      this.$refs.updateMetaDataModalRef.show(this.nodeSelectedList, "multi");
    },
    setLinkMetaType() {
      this.nodeSelectedList.forEach((ele) => {
        if (ele.linkId) {
          if (!ele.metaData) {
            ele.metaData = {};
          }
          ele.metaData.type = "ConnectLine";
        }
      });
      this.updateNodeInfo();
    },
    handleRotateClick() {
      this.handleInputChange(this.rotate, "rotate");
    },
    // input输入框改变  color选择器跟input的值要分开处理
    handleInputChange(val, type) {
      val = val instanceof Object ? val.target.value : val;
      type === "rotate" && (this.rotate = val || 0);
      this.nodeSelectedList.forEach((ele) => {
        ele[type] = val;
      });
      this.updateNodeInfo();
    },
    handleChangeSizeClick() {
      this.nodeSelectedList.forEach((ele) => {
        const { nodePosition, nodeSize } = ele;
        const [x, y] = nodePosition.split(",").map((ele) => +ele);
        const [w, h] = nodeSize.split("*").map((ele) => +ele);
        let width = this.multiSize.w;
        let height = this.multiSize.h;
        const x1 = x - (width - w) / 2;
        const y1 = y - (height - h) / 2;
        ele.w = width;
        ele.h = height;
        ele.x = x1;
        ele.y = y1;
        ele.nodeSize = `${width}*${width}`;
        ele.nodePosition = `${x1},${y1}`;
      });
      this.updateNodeInfo();
    },
    // 新增 变绿
    handleChangeColorClick(val) {
      const dynamicColor = val === "reset" ? "" : val;
      this.nodeSelectedList.forEach((ele) => {
        if (ele.linkId) {
          let { linkStyles } = ele;
          linkStyles = linkStyles ? JSON.parse(linkStyles) : {};
          linkStyles.dynamicColor = dynamicColor;
          ele.linkStyles = JSON.stringify(linkStyles);
        } else {
          let { nodeStyles } = ele;
          nodeStyles = nodeStyles ? JSON.parse(nodeStyles) : {};
          nodeStyles.dynamicColor = dynamicColor;
          ele.nodeStyles = JSON.stringify(nodeStyles);
        }
      });
      this.updateNodeInfo();
    },
    handlLineStylesChange(val, type) {
      this.nodeSelectedList.forEach((ele) => {
        if (ele.nodeId) {
          let { nodeStyles } = ele;
          nodeStyles = nodeStyles ? JSON.parse(nodeStyles) : {};
          if (!nodeStyles.line) {
            nodeStyles.line = {};
          }
          nodeStyles.line[type] = val;
          ele.nodeStyles = JSON.stringify(nodeStyles);
        }
      });
      this.updateNodeInfo();
    },
    handleLineStylesClick() {
      this.nodeSelectedList.forEach((ele) => {
        if (ele.nodeId) {
          let { nodeStyles } = ele;
          nodeStyles = nodeStyles ? JSON.parse(nodeStyles) : {};
          nodeStyles.line = this.line;
          ele.nodeStyles = JSON.stringify(nodeStyles);
        }
      });
      this.updateNodeInfo();
    },
    handleStyleInputChange(val, type) {
      val = val instanceof Object ? val.target.value : val;
      this.nodeSelectedList.forEach((ele) => {
        if (ele.linkId) {
          let { linkStyles } = ele;
          linkStyles = linkStyles ? JSON.parse(linkStyles) : {};
          linkStyles[type] = val;
          ele.linkStyles = JSON.stringify(linkStyles);
        } else {
          let { textStyles } = ele;
          textStyles = textStyles ? JSON.parse(textStyles) : {};
          textStyles[type] = val;
          ele.textStyles = JSON.stringify(textStyles);
        }
      });
      this.updateNodeInfo();
    },
    updateNodeInfo() {
      this.$bus.emit("updateNodeSelectedList", this.nodeSelectedList);
    },
    handleAlignLink() {
      this.$bus.emit("onAttributeLinkClick", {
        type: "alignLink",
        val: this.distanceAlign,
      });
    },
    handleParallelLinkClick() {
      this.$bus.emit("onAttributeLinkClick", {
        type: "paralleLink",
      });
    },
    // 文字绑定连线
    // bindLink和bindSubLink
    handleBindLinkClick() {
      const links = this.nodeSelectedList.filter((ele) => ele.linkId);
      const nodes = this.nodeSelectedList.filter(
        (ele) => ele.nodeId && ele.nodeType !== "text"
      );

      if (!links.length && !nodes.length) {
        this.$Message.warning("未选择绑定目标元素");
        return;
      }

      if (nodes.length >= 2 || links.length >= 2) {
        this.$Message.warning("不支持超过两个元素绑定");
        return;
      }

      if (links.length) {
        const link = links[0];

        this.nodeSelectedList.forEach((ele) => {
          if (ele.nodeType === "text") {
            ele.bindLink = link.linkId;
            ele.bindSubLink = this.linkPositionValue;
          }
        });
      } else if (nodes.length) {
        const node = nodes[0];

        this.nodeSelectedList.forEach((ele) => {
          if (ele.nodeType === "text") {
            ele.bindLink = node.nodeId;
            ele.bindSubLink = 0;
          }
        });
      }

      this.updateNodeInfo();
    },
    handleAdsorbClick(type) {
      const list = setLinkAdsorb(this.nodeSelectedList, type);
      this.$bus.emit("updateNodeSelectedList", list);
    },
    handleDiatanceTextClick() {
      const list = alignAndDistanceText(this.nodeSelectedList);
      this.$bus.emit("updateNodeSelectedList", list);
    },
    handleAdjustTextClick(type) {
      const list = setText(this.nodeSelectedList, type);
      this.$bus.emit("updateNodeSelectedList", list);
    },
    handleSetLinkWidth() {
      this.nodeSelectedList.forEach((ele) => {
        ele.linkWidth = this.linkWidth;
      });
      this.updateNodeInfo();
    },
    // 设置连线
    handleSetLinkClick(type) {
      switch (type) {
        case "reverse":
          this.nodeSelectedList.forEach((ele) => {
            let { fromObj, endObj } = ele;

            ele.fromObj = endObj;
            ele.endObj = fromObj;
            ele.pathPoints.reverse();
          });
          break;
        case "dotted-line":
          this.nodeSelectedList.forEach((ele) => {
            let { linkStyles } = ele;
            linkStyles = linkStyles ? JSON.parse(linkStyles) : {};

            ele.linkType = "虚线";
            ele.dashedLink = "10,10";
            ele.linkStyles = JSON.stringify(linkStyles);
          });
          break;
        case "line":
          this.nodeSelectedList.forEach((ele) => {
            let { linkStyles } = ele;
            linkStyles = linkStyles ? JSON.parse(linkStyles) : {};

            ele.linkType = "实线";
            ele.dashedLink = "";
            ele.linkStyles = JSON.stringify(linkStyles);
          });
          break;
        default:
          break;
      }
      this.updateNodeInfo();
    },
    handleSetLinkArrowClick(type) {
      this.nodeSelectedList.forEach((ele) => {
        let { linkStyles } = ele;
        linkStyles = linkStyles ? JSON.parse(linkStyles) : {};
        linkStyles.isArrow = !!type;
        type && (linkStyles.arrowDirection = type);
        ele.linkStyles = JSON.stringify(linkStyles);
      });
      this.updateNodeInfo();
    },
    handleLinkDashChange(val) {
      this.nodeSelectedList.forEach((ele) => {
        let { linkStyles } = ele;
        linkStyles = linkStyles ? JSON.parse(linkStyles) : {};
        linkStyles.isArrow = false;

        if (val === 0) {
          ele.linkType = "实线";
        } else {
          ele.linkType = "虚线";
          ele.dashedLink = dasharrayOptions[val].value;
        }
        ele.linkStyles = JSON.stringify(linkStyles);
      });
      this.updateNodeInfo();
    },
    handleAlignClick(index) {
      this.$bus.emit("alignClick", index + 1);
    },
    handleRotationOffsetChange(val) {
      console.log("🚀 ~ handleRotationOffsetChange ~ val:", val);
      this.setRotationOffset(val);
    },
  },
};
</script>
<style lang="scss">
.topo-point-index-active {
  background-color: #eee;
}
.topo-muil-attr-input {
  width: 100%;
}
.node-mult-font-color-picker .ivu-select-dropdown {
  left: -50px !important;
}
.node-mult-gradient-picker .ivu-select-dropdown {
  left: -95px !important;
}
</style>
