import Vue from "vue";
import VueRouter from "vue-router";
import Cookies from "js-cookie";
import store from "@/store/";
import Home from "@/views/Home.vue";
import { menuOptions } from "@/utils/menuOptions";

Vue.use(VueRouter);

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
const routes = [
  {
    path: "/",
    name: "home",
    component: Home,
    redirect: "/topo-edit",
    children: [
      {
        path: "/topo-edit",
        name: "topoEdit",
        component: () => import("../views/topoManage/topoEdit.vue"),
      },
      {
        path: "/object-manage",
        name: "objectManage",
        component: () => import("../views/topoManage/objectManage.vue"),
      },
      {
        path: "/permission-manage",
        name: "permissionManage",
        component: () => import("../views/systemManage/permissionManage.vue"),
      },
      {
        path: "/user-manage",
        name: "userManage",
        component: () => import("../views/systemManage/userManage.vue"),
      },
    ],
  },
  {
    path: "/404",
    name: "页面不存在",
    meta: {
      title: "页面不存在",
      isLogin: false,
    },
    component: () => import("@/views/404.vue"),
  },
  {
    path: "/401",
    name: "无权限访问",
    meta: {
      title: "无权限访问",
      isLogin: false,
    },
    component: () => import("@/views/401.vue"),
  },
  // 所有未定义路由，全部重定向到404页
  {
    path: "*",
    redirect: "/404",
  },
  {
    path: "/login",
    name: "login",
    component: () => import("../views/Login.vue"),
  },
];

const router = new VueRouter({
  routes,
});

const routeUrl = [];

router.beforeEach((to, from, next) => {
  const pathStrByLocal = window.localStorage.getItem("routes") || "";
  const pathByLocal = pathStrByLocal.split(",");
  let menuList = [];
  menuOptions.forEach((item) => {
    let menu = { children: [] };
    let key = "/" + item.key;

    if (pathByLocal.includes(key)) {
      menu = Object.assign(menu, item);
      routeUrl.push(...item.children.map((ele) => ele.url));
      menuList.push(menu);
    }
    //   else {
    //     item.children.forEach((child) => {
    //       if (pathByLocal.includes(key + "/" + child.id)) {
    //         menu.children.push(child);
    //         routeUrl.push(child.url);
    //       }
    //     });
    //   }
  });
  store.commit("topoStore/setMenuList", menuList);

  let token = Cookies.get("token");
  if (to.path === "/login") {
    // 如果是访问登录界面，如果用户会话信息存在，代表已登录过，跳转到主页
    if (token) {
      next({ path: routeUrl[0] });
    } else {
      next();
    }
  } else {
    if (!token) {
      next({ path: "/login" });
      window.localStorage.clear();
    } else {
      if (to.path === "/404" || to.path === "/401") {
        next();
      } else if (routeUrl.indexOf(to.path) >= 0) {
        // 有权限
        next();
      } else {
        // 无topo-edit访问权限，跳转到第一个页面
        if (to.path === "topo-edit") {
          next(routeUrl[0]);
        } else {
          // 无权限
          next("/401");
        }
      }
    }
  }
});

export default router;
