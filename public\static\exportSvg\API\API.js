/**
 * name: 公共接口
 * author: jiangguoxing
 * createTime: 2020-04-28
 */
(() => {
  //获取当前API.js的文件路径
  let currentJsPath = $("script").last().attr("src");
  let isRenderComponents = true;
  let url = new URL(window.location.href || "");
  if (
    JSON.parse(url.searchParams.get("r")) !== null &&
    !JSON.parse(url.searchParams.get("r"))
  ) {
    isRenderComponents = false;
  }
  currentJsPath = currentJsPath.substring(0, currentJsPath.lastIndexOf("/"));
  //let currentJsPath = (window.WisUtil && window.WisUtil.scriptPath) ? window.WisUtil.scriptPath("API") : "API";

  //后台服务路径, websocket服务地址, 数据同步服务地址, APNS ws 服务地址， 组件数据监测
  let [
    BASE_URL,
    CONTROL_CENTER,
    DATA_CENTER,
    VIDEO_CENTER,
    DEBUG_MODEL,
    DATA_WS_NUM,
  ] = ["", "", "", "", "", 1];
  let heartBeatTime = 5000;

  //单个stomp最大订阅数
  let maxStompSubscribeNum = 1500;
  //保存组件与stompClient对应关系
  let componentsStompClient = {};
  window.componentsStompClient = componentsStompClient;

  let resetConsoleLog = (debugModel) => {
    if (debugModel === "0") {
      console.log = () => {};
    }
  };

  let initAPI = () => {
    return new Promise((resolve, reject) => {
      window.wsProtocol =
        window.location.protocol.indexOf("https") != -1 ? "wss" : "ws";

      d3.json("/action/env/getSysEnvList").then((resp) => {
        window.serviceAddressConfig = {};
        if (resp.code === "0000") {
          let envData = resp.data || [];
          envData.forEach((ed) => {
            window.serviceAddressConfig[ed.envKey] = ed.envValue;
          });
          serviceAddressConfig.CONTROL_CENTER =
            window.location.host.split(":")[0];
          serviceAddressConfig.HTTP_PROTOCOL =
            window.location.protocol.indexOf("https") != -1 ? "https" : "http";
          serviceAddressConfig.IP_AND_PORT = `${window.location.host}`;
          serviceAddressConfig.BASE_URL = "/action";
          serviceAddressConfig.DATA_CENTER = `${window.wsProtocol}://${serviceAddressConfig.IP_AND_PORT}/dataServer`;
          serviceAddressConfig.VIDEO_CENTER = "";
          [
            BASE_URL,
            CONTROL_CENTER,
            DATA_CENTER,
            VIDEO_CENTER,
            DEBUG_MODEL,
            DATA_WS_NUM,
            // COMPONENT_DATA_BIND_CHECK
          ] = [
            serviceAddressConfig.BASE_URL,
            serviceAddressConfig.CONTROL_CENTER || "",
            serviceAddressConfig.DATA_CENTER,
            serviceAddressConfig.VIDEO_CENTER || "",
            serviceAddressConfig.DEBUG_MODEL || 0,
            serviceAddressConfig.DATA_WS_NUM || 1,
          ];

          //重写console.log
          resetConsoleLog(DEBUG_MODEL);

          //全局实例注册
          window.asyncHttp = new AsyncHttp();
          window.wiscomWebSocket = new WiscomWebSocket();
          window.centralManager = new CentralManager();
          if (isRenderComponents) {
            for (let wsI = 0; wsI < DATA_WS_NUM; wsI++) {
              window.wiscomWebSocket.getDataSourceWS(wsI);
            }
            window.wiscomWebSocket.getAnimateSyncWS();
          }
          //统一组件发送心跳至控制中心
          setInterval(() => {
            window.wiscomWebSocket.webSocketList.forEach((ws) => {
              //ws处于连接状态，数据订阅不需要发送心跳
              if (ws.readyState === 1 || ws.state === "OPEN") {
                ws.send(ws.keepAlive);
              }
            });
          }, heartBeatTime);
          resolve();
        }
      });
    });
  };

  /**
   * 基本http服务
   */
  class AsyncHttp {
    constructor(time = 30000, user = {}) {
      this.time = time;
      //当前用户
      this.user = user;
      //身份认证
      this.token = null;
      //终止请求对象
      this.controller = null;
      //解析文件方法
      this.resolveFileMethods = {
        json: d3.json,
        text: d3.text,
        html: d3.html,
        svg: d3.html,
        xml: d3.xml,
      };
      //匹配url文件后缀名
      this.suffixReg = /[^\.]\w*$/; ///\.[^\.]+$/带点后缀名;/[^\/\\]+$/文件全称
    }

    /**
     * 加密
     * @param {*} info
     * @param {*} key
     */
    encrypt(info, key) {}

    /**
     * 解密
     * @param {*} info
     * @param {*} key
     */
    decrypt(info, key) {}

    /**
     * 公共JSON数据接口(兼容旧代码调用方式)
     * @param {string} action
     * @param {json} data
     * @param {function} callback
     * @param {string} paramType value: 'x-www-form-urlencoded'|'json'
     */
    _requestJSON(param = {}, paramType = "json") {
      let requestData = {};
      //如果该参数不为空,则为POST请求; GET请求的方式参数直接跟在action后面
      if (param.data) {
        requestData = {
          headers: {
            "Content-Type": `application/${paramType}`,
          },
          body: JSON.stringify(param.data),
          method: "POST",
        };
      }
      d3.json(`${BASE_URL}/${param.action}`, requestData).then((resp) => {
        //为了兼容之前接口请求写法
        param.callback && param.callback(resp);
      });
    }

    /**
     * 公共JSON数据接口(兼容旧代码调用方式)
     * @param {string} action
     * @param {*} data
     * @param {function} callback
     * @param {string} paramType value: 'x-www-form-urlencoded'|'json'
     */
    sRequestJSON(param = {}, paramType = "json") {
      let requestData = {};
      //如果该参数不为空,则为POST请求; GET请求的方式参数直接跟在action后面
      if (param.data) {
        requestData = {
          headers: {
            "Content-Type": `application/${paramType}`,
          },
          body:
            typeof paramType === "object" && paramType === "json"
              ? JSON.stringify(param.data)
              : param.data,
          method: "POST",
        };
      }
      d3.json(param.action, requestData).then((resp) => {
        //为了兼容之前接口请求写法
        param.callback && param.callback(resp);
      });
    }

    /**
     * 请求超时方法
     * @param {*} timeout
     */
    _timeoutPromise(timeout = this.time) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(
            new Response("timeout", {
              status: 504,
              statusText: "request timeout!",
            })
          );
          //请求超时，并且放弃该次ajax请求
          this.controller.abort();
        }, timeout);
      });
    }

    /**
     * 接口请求新方法
     * @param {*} action
     * @param {*} param
     * @param {*} sessionID
     * @param {*} paramType
     */
    _requestPromise(action = "", param = null, paramType = "json") {
      this.controller = new AbortController();
      let requestData = {
        signal: this.controller.signal,
        headers: {
          "Content-Type": `application/${paramType}`,
          Authorization: sessionStorage.getItem("sessionID"),
        },
      };
      //如果该参数不为空,则为POST请求; GET请求的方式参数直接跟在action后面
      if (param) {
        requestData.body =
          typeof paramType === "object" && paramType === "json"
            ? JSON.stringify(param)
            : param;
        requestData.method = "POST";
      }
      return d3.json(`${BASE_URL}/${action}`, requestData);
    }

    /**
     * 公共JSON数据接口(新接口调用方法) 设置超时请求时间，放弃请求
     * @param {String} param.action
     * @param {JSON|JSONString|key1=value1&key1=value2} param.data
     * @param {Function} param.callback
     * @param {String} paramType value: 'json'|'x-www-form-urlencoded'
     * 注(data格式):
     * json: JSON.stringify({key: value}) or {key: value};
     * x-www-form-urlencoded: key1=value1&key2=value2... (1.0版本: JSON.stringify({}));
     */
    requestJSON(param = {}, paramType = "json") {
      //返回最快完成的请求
      Promise.race([
        this._timeoutPromise(), //设置时间的延迟请求
        this._requestPromise(param.action, param.data, paramType), //真实数据请求
      ]).then((resp) => {
        if (resp.code === "0105") {
          //登录超时
          notify.warn(resp.message, () => {
            sessionStorage.removeItem("sessionID");
            window.location.href = "controlLogin.html";
          });
        } else if (resp.code === "0000") {
          //正常请求
          //为了兼容之前接口请求写法
          param.callback && param.callback(resp);
        } else {
          notify.error(resp.message);
        }
      });
    }

    /**
     * 解析文件
     * @param {String} fileUrl
     */
    requestFile(fileUrl = "") {
      let fUrl = fileUrl.split("?")[0];
      let fileType = this.suffixReg.exec(fUrl)[0];
      let resFunc = this.resolveFileMethods[fileType];
      //如果没有找到对应的解析方法则返回解析失败
      if (!resFunc) {
        return new Promise((resolve, reject) => {
          resolve(
            new Response("resolveFailed", {
              status: 500,
              statusText: "not find resolve function",
            })
          );
        });
      }
      return new Promise((resolve, reject) => {
        resFunc(fileUrl).then(
          (resp) => {
            if (resp) {
              resolve(resp);
            } else {
              reject(
                new Response("resolveFailed", {
                  status: 500,
                  statusText: "file resolve failed",
                })
              );
            }
          },
          (err) => {
            reject(err);
          }
        );
      });
    }

    /**
     * 解析多个文件
     * @param {Array} fileUrls
     */
    requestFiles(fileUrls = []) {
      let resolveFuncs = [];
      fileUrls.forEach((url) => {
        let fileType = this.suffixReg.exec(url)[0];
        let resFunc = this.resolveFileMethods[fileType];
        //如果文件有对应的解析方法则添加到数组
        resFunc && resolveFuncs.push(resFunc(url));
      });
      return new Promise((resolve, reject) => {
        Promise.all(resolveFuncs).then(
          (results) => {
            if (results && results.length > 0) {
              resolve(results);
            } else {
              reject(
                new Response("resolveFailed", {
                  status: 500,
                  statusText: "file resolve failed",
                })
              );
            }
          },
          (err) => {
            reject(err);
          }
        );
      });
    }
  }

  /**
   * websocket服务
   */
  class WiscomWebSocket {
    constructor() {
      //websocket服务列表
      this.webSocketList = [];
      //重连时间
      this.reconnectTime = 2000;
      //topicClientMap
      this.topicClientMap = new Map();

      //心跳检测
      this.heartCheck = {
        timeout: 60000, //心跳响应时间60s
        timeoutObj: null,
        serverTimeoutObj: null,
        reset: () => {
          clearTimeout(this.timeoutObj);
          clearTimeout(this.serverTimeoutObj);
          return this;
        },
        start: (ws) => {
          let self = this;
          this.timeoutObj = setTimeout(() => {
            //发送心跳，后端返回心跳消息; onmessage拿到返回的心跳就说明连接正常
            ws.send("HeartBeat");
            self.serverTimeoutObj = setTimeout(() => {
              //如果超过一定时间还没重置，说明后端主动断开了
              ws.close();
            }, self.timeout);
          }, this.timeout);
        },
      };
    }

    /**
     * 组件数据同步getDataSourceWS
     * @param {*} appName
     * @param {*} serviceHost
     * @param {*} port
     * @param {*} url
     */
    _getDataSourceWS(ws = {}) {
      if (typeof WebSocket !== "function") {
        console.log("当前浏览器不支持HTML5 WebSocket");
        return;
      }
      let isConnectError = false;
      let afreshSubscribe = document.createEvent("HTMLEvents");
      afreshSubscribe.initEvent("afreshSubscribe", true, true);

      let webSocket = new ReconnectingWebSocket(DATA_CENTER);
      //保存WebSocket应用
      webSocket.appName = "dataSource";
      //this.webSocketList.push(webSocket);
      let stompClient = Stomp.over(webSocket);
      stompClient.connect(
        {},
        (success) => {
          console.log("jw_debug web connection successfully", success);
          if (!isConnectError) {
            return;
          }
          document.dispatchEvent(afreshSubscribe);
          isConnectError = false;
        },
        (error) => {
          console.log("jw_debug web connection failed", error);
          isConnectError = true;
        }
      );
      window.dataSourceStompClient = stompClient;
    }

    /**
     * 组件数据同步getDataSourceWS
     * @param {*} appName
     * @param {*} serviceHost
     * @param {*} port
     * @param {*} url
     */
    getDataSourceWS(wsIndex) {
      if (typeof WebSocket !== "function") {
        console.log("当前浏览器不支持HTML5 WebSocket");
        return;
      }
      let isConnectError = false;
      let stompIndex = wsIndex;
      //let afreshSubscribe = document.createEvent('HTMLEvents');
      //afreshSubscribe.initEvent("afreshSubscribe", true, true);
      let afreshSubscribe = new CustomEvent("afreshSubscribe", {
        detail: {
          stompIndex: stompIndex,
        },
      });

      let webSocket = new ReconnectingWebSocket(DATA_CENTER);
      //保存WebSocket应用
      webSocket.appName = "dataSource";
      let stompClient = Stomp.over(webSocket);
      stompClient.connect(
        {},
        (success) => {
          console.log("jw_debug web connection successfully", success);
          if (!isConnectError) {
            return;
          }
          document.dispatchEvent(afreshSubscribe);
          isConnectError = false;
        },
        (error) => {
          console.log("jw_debug web connection failed", error);
          isConnectError = true;
        }
      );
      if (!window.dataSourceStompClients) {
        window.dataSourceStompClients = [stompClient];
      } else {
        window.dataSourceStompClients.push(stompClient);
      }
    }

    /**
     * 数据订阅
     * @param {*} stompClient
     * @param {*} componentCode
     * @param {*} callback
     */
    _subscribeData(componentCode, callback) {
      let stompClient = window.dataSourceStompClient;
      if (!stompClient.connected) {
        setTimeout(() => {
          this._subscribeData(componentCode, callback);
        }, 2000);
      } else {
        console.log(
          `jw_debug subscribe: ${window.currentSceneId}, ${componentCode}`
        );
        let topicClient = stompClient.subscribe(
          `/topic/TableData/${window.currentSceneId}/${componentCode}`,
          callback
        );
        this.topicClientMap.set(
          `${window.currentSceneId}_${componentCode}`,
          topicClient
        );
      }
    }

    /**
     * 获取当前组件需要使用哪个stompClient订阅数据
     */
    getDataStompClientIndex() {
      let topicLength = this.topicClientMap.size || 0;
      let stompIndex = Math.ceil(topicLength / maxStompSubscribeNum) - 1;
      return stompIndex < 0 ? 0 : stompIndex;
    }

    /**
     * 数据订阅
     * @param {*} stompClient
     * @param {*} componentCode
     * @param {*} callback
     */
    subscribeData(componentCode, callback) {
      let stompClient = null;
      let stompIndex = 0;
      //通过组件对应stompClient关系获取，用于断开重连重新订阅时获取stompClient
      let compStompIndex =
        componentsStompClient[`${window.currentSceneId}_${componentCode}`];
      if (compStompIndex !== undefined && compStompIndex !== null) {
        stompIndex = compStompIndex;
        stompClient = window.dataSourceStompClients[stompIndex];
        //将上一次订阅的组件code清空
        //stompClient.subscribeComponents = [];
      } else {
        //如果只创建了一个stomp则直接获取
        if (window.dataSourceStompClients.length === 1) {
          stompClient = window.dataSourceStompClients[0];
        } else {
          stompIndex = this.getDataStompClientIndex();
          stompClient = window.dataSourceStompClients[stompIndex];
        }
      }
      if (!stompClient.connected) {
        setTimeout(() => {
          this.subscribeData(componentCode, callback);
        }, 2000);
      } else {
        console.log(
          `jw_debug subscribe: ${window.currentSceneId}, ${componentCode}`
        );
        let topicClient = stompClient.subscribe(
          `/topic/TableData/${window.currentSceneId}/${componentCode}`,
          callback
        );
        //保存该stompClient订阅的组件
        if (!stompClient.subscribeComponents) {
          stompClient.subscribeComponents = [componentCode];
        } else {
          stompClient.subscribeComponents.push(componentCode);
        }
        let topicKey = `${window.currentSceneId}_${componentCode}`;
        //保存当前组件订阅使用的stompClient下标,用于反订阅时获取订阅时使用的stompClient
        componentsStompClient[topicKey] = stompIndex;
        this.topicClientMap.set(topicKey, topicClient);
      }
    }

    /**
     * 取消映射订阅
     * @param {*} componentCode
     */
    _unsubscribeData(componentCode) {
      let stompClient = window.dataSourceStompClient;
      console.log(
        `jw_debug unsubscribe: ${window.currentSceneId}, ${componentCode}`
      );
      stompClient.connected &&
        stompClient.send(
          `/topic/DisTableData/${window.currentSceneId}/${componentCode}`
        );
      /*let topicKey = window.currentSceneId + "_" + componentID;
                if (this.topicClientMap.has(topicKey)) {
                    //this.topicClientMap.get(topicKey).unsubscribe();
                    this.topicClientMap.delete(topicKey);
                }*/
    }

    /**
     * 取消映射订阅
     * @param {*} componentCode
     */
    unsubscribeData(componentCode) {
      //根据code获取stompClient;
      let stompIndex =
        componentsStompClient[`${window.currentSceneId}_${componentCode}`];
      let stompClient = window.dataSourceStompClients[stompIndex];
      console.log(
        `jw_debug unsubscribe: ${window.currentSceneId}, ${componentCode}`
      );
      stompClient.connected &&
        stompClient.send(
          `/topic/DisTableData/${window.currentSceneId}/${componentCode}`
        );
    }

    /**
     * 取消数据订阅
     * @param {*} componentCode
     */
    unsubscribeDataByClient(componentCode) {
      let topicKey = `${window.currentSceneId}_${componentCode}`;
      if (this.topicClientMap.has(topicKey)) {
        this.topicClientMap.get(topicKey).unsubscribe();
        this.topicClientMap.delete(topicKey);
      }
    }

    /**
     * 清空stompClientMap
     */
    emptyStompClient() {
      let stompClients = window.dataSourceStompClients;
      this.topicClientMap.forEach((item, key, map) => {
        //根据映射关系key获取stomp下标
        let stompIndex = componentsStompClient[key];
        //如果订阅使用的stompClient正常
        if (stompClients[stompIndex].connected) {
          try {
            item && item.unsubscribe();
          } catch (error) {
            console.error(error);
          }
        }
      });
      this.topicClientMap.clear();
    }

    /**
     * @description 动画同步订阅Stomp
     */
    getAnimateSyncWS() {
      if (typeof WebSocket !== "function") {
        console.log("当前浏览器不支持HTML5 WebSocket");
        return;
      }
      let isConnectError = false;
      let afreshSubscribe = document.createEvent("HTMLEvents");
      afreshSubscribe.initEvent("afreshSubscribe", true, true);

      let webSocket = new ReconnectingWebSocket(
        `${window.wsProtocol}://${serviceAddressConfig.IP_AND_PORT}/p6804`
      );
      //保存WebSocket应用
      webSocket.appName = "animateSync";
      let stompClient = Stomp.over(webSocket);
      stompClient.connect(
        {},
        (success) => {
          console.log("jw_debug web connection successfully", success);
          if (!isConnectError) {
            return;
          }
          document.dispatchEvent(afreshSubscribe);
          isConnectError = false;
        },
        (error) => {
          console.log("jw_debug web connection failed", error);
          isConnectError = true;
        }
      );
      window.animateSyncStompClient = stompClient;
    }

    /**
     * @description 动画同步实现
     * @param {string} 组件code
     * @param {function} callback 回调函数
     */
    animateSync(componentCode, callback) {
      let stompClient = window.animateSyncStompClient;
      if (!stompClient.connected) {
        setTimeout(() => {
          this.animateSync(componentCode, callback);
        }, 2000);
      } else {
        console.log(
          `jw_debug subscribe: ${window.currentSceneId}, ${componentCode}`
        );
        let topicClient = stompClient.subscribe(
          `/sync/topic/${window.currentSceneId}/${componentCode}`,
          callback
        );
        // this.topicClientMap.set(`${window.currentSceneId}_${componentCode}`, topicClient);
      }
    }

    /**
     * @description 发送动画同步所需参数
     * @param {string} componentCode 组件code
     * @param {string} param 动画参数json字符串
     */
    sendAnimateParam(componentCode, param) {
      let stompClient = window.animateSyncStompClient;
      if (stompClient.connected) {
        stompClient.send(
          `/sync/topic/${window.currentSceneId}/${componentCode}`,
          {},
          JSON.stringify(param)
        );
      } else {
        setTimeout(() => {
          this.sendAnimateParam(componentCode, param);
        }, 2000);
      }
    }

    /**
     * 获取websocket服务
     * @param {*} appName: 应用名称
     * @param {*} port: 服务端口
     * @param {*} url: websocket服务地址
     * @param {*} token: 身份认证(可以为空)
     * @return {WebSocket} webSocket
     */
    getWebSocket(appName = "ws", port = 6800, url = "", token, ws = {}) {
      if (typeof WebSocket !== "function") {
        console.log("当前浏览器不支持HTML5 WebSocket");
        return;
      }
      //判断ws管理列表内是否已经存在初始化的ws
      let wsIndex = this.webSocketList.indexOf(ws);

      //支持身份认证，断点重连
      let webSocket = new ReconnectingWebSocket(
        `${window.wsProtocol}://${serviceAddressConfig.IP_AND_PORT}${
          url ? `/${url}` : ""
        }${token ? `?token=${token}` : ""}`
      );
      //应用名称
      webSocket.appName = appName;
      //避免重复连接
      webSocket.lockReconnect = false;
      //事件队列
      webSocket.eventList = ws.eventList || [];
      //如果ws列表中已经存在ws
      if (wsIndex != -1) {
        this.webSocketList[wsIndex] = webSocket;
      } else {
        this.webSocketList.push(webSocket);
      }
      return webSocket;
    }

    /**
     * 获取APNS websocket服务
     * @param {*} name: ws名称
     */
    getAPNSWebsocket(name = "APNS") {
      if (typeof WebSocket !== "function") {
        console.log("当前浏览器不支持HTML5 WebSocket");
        return;
      }

      //支持身份认证，断点重连
      let apns_webSocket = new ReconnectingWebSocket(
        `${window.wsProtocol}://${VIDEO_CENTER}`
      );
      //应用名称
      apns_webSocket.appName = name;
      apns_webSocket.keepAlive = "702:K_A";
      this.webSocketList.push(apns_webSocket);
      apns_webSocket.onopen = () => {
        apns_webSocket.state = "OPEN";
      };
      apns_webSocket.onerror = (e) => {
        console.log("jw_debug apns_webSocket on error", e, e.data);
      };
      apns_webSocket.onclose = (e) => {
        console.log("jw_debug apns_webSocket on close", e, e.code);
      };
      return apns_webSocket;
    }

    /**
     * 重连websocket服务
     * @param {*} appName: 应用名称
     * @param {*} port: 服务端口
     * @param {*} url: websocket服务地址
     * @param {*} token: 身份认证(可以为空)
     * @param {WebSocket} ws
     */
    // reconnect(appName = "ws", port = 6800, url = "", token, ws = {}) {
    //     if (ws.lockReconnect) return;
    //     ws.lockReconnect = true;

    //     //连接失败继续重连，设置延迟避免请求过多
    //     setTimeout(() => {
    //         this.getWebSocket(appName, port, url, token, ws);
    //         ws.lockReconnect = false;
    //     }, this.reconnectTime);
    // };

    /**
     * 关闭websocket服务
     * @param {WebSocket} ws
     */
    disconnect(ws = {}) {
      ws.close();
      ws = null;
    }

    /**
     * 关闭所有websocket服务
     */
    disconnectAll() {
      this.webSocketList.forEach((ws) => {
        this.disconnect(ws);
      });
      this.webSocketList = [];
    }

    /**
     * 获取所有webSocket应用
     * @return {Array} webSocketList
     */
    getAllWebSocket() {
      return this.webSocketList;
    }

    /**
     * 获取特定的WS
     * @param {*} appName
     */
    resetWebSocketList(appName) {
      let saveWebSocketList = [];
      for (let index in this.webSocketList) {
        for (let i in appName) {
          if (this.webSocketList[index].appName === appName[i]) {
            saveWebSocketList.push(this.webSocketList[index]);
          }
        }
      }
      this.webSocketList = saveWebSocketList;
    }
  }

  /**
   * 中心管理
   */
  class CentralManager {
    constructor() {
      //通信码
      this.communicationCode = {
        //浏览器注册
        registerBrowser: 1,
        //组件注册
        registerComponent: 101,
        //发送消息
        sendMessage: 103,
        //接收消息
        receiveMessage: 104,
        //控制器注册
        registerControl: 201,
        //场景配置文件
        syncConfig: 203,
        //场景code
        updateScene: 204,
        //命令行事件
        event: 205,
        //修改全局变量事件，只触发一次
        singleEvent: 212,
        //白板
        draw: 206,
        //未用
        command: 301,
      };
    }

    /**
     * 创建ws回调
     * @param {*} appName: 应用名称
     * @param {*} executeFunc: 执行函数
     * @param {*} port: 端口
     * @param {*} url: websocket服务地址
     * @param {*} token: 身份认证
     * @param {WebScoket} ws
     * @param {*} register
     */
    _initEventHandle(
      appName = "ws",
      executeFunc = () => {},
      port = 6800,
      url = "",
      token,
      ws = {},
      register
    ) {
      let communicationCode = this.communicationCode;
      ws.onopen = (e) => {
        ws.state = "OPEN";

        //组件注册
        let registerMsg = `pane=${paneId},component=${ws.appName}`;
        console.log("注册=====" + registerMsg);
        ws.send(
          communicationCode[register ? register : "registerComponent"] +
            ":" +
            registerMsg
        );

        //判断是否存在需要发送的消息
        if (ws.eventList.length > 0) {
          ws.eventList.forEach((event) => {
            ws.send(event);
          });
          ws.eventList = [];
        }
      };
      ws.onmessage = (e) => {
        if (e && e.data) {
          let codeIndex = e.data.indexOf(":");
          if (codeIndex < 1) {
            console.log(`incorrected message format:${e.data}`);
            return;
          }
          let code = parseInt(e.data.substring(0, codeIndex));
          let message = e.data.substring(codeIndex + 1);

          //接收返回消息执行相关操作
          executeFunc(message, code);
        }
      };
    }

    /**
     * 发送消息
     * @param {WebSocket} ws
     * @param {*} message
     */
    sendMessage(ws = {}, message = "default", code) {
      //判断当前webSocket是否处于连接状态
      if (ws.readyState === WebSocket.OPEN || ws.state === "OPEN") {
        ws.send(
          this.communicationCode[code ? code : "sendMessage"] + ":" + message
        );
      } else {
        console.log("websocket 未创建成功!");
        ws.eventList.push(
          this.communicationCode[code ? code : "sendMessage"] + ":" + message
        );
      }
    }

    /**
     * 组件注册
     * @param {WebSocket} ws
     * @param {*} message
     */
    registerComponent(ws = {}, message = "default", register) {
      //判断当前webSocket是否处于连接状态
      if (ws.readyState === WebSocket.OPEN || ws.state === "OPEN") {
        ws.send(
          this.communicationCode[register ? register : "registerComponent"] +
            ":" +
            message
        );
      } else {
        console.log("websocket 未创建成功!");
        ws.eventList.push(
          this.communicationCode[register ? register : "registerComponent"] +
            ":" +
            message
        );
      }
    }

    /**
     * 实时监听端口
     * @param {*} port: 6800, 6801, 6802
     */
    listenToPortEvents(port) {
      console.log("*****" + port);
    }

    /**
     * 大屏客户端注册服务
     * @param {*} appName: 应用名称
     * @param {*} executeFunc: 执行函数
     * @param {*} port: 端口
     * @param {*} url: websocket服务地址
     * @param {*} token: 身份认证
     * @return {WebScoket} connectWS
     */
    centralConnect(
      appName = "connectWS",
      executeFunc = () => {},
      port = 6800,
      url = "p6800",
      token
    ) {
      let connectWS = wiscomWebSocket.getWebSocket(appName, port, url, token);
      connectWS.keepAlive = "2:K_A";

      this._initEventHandle(
        appName,
        executeFunc,
        port,
        url,
        token,
        connectWS,
        "registerBrowser"
      );

      return connectWS;
    }

    /**
     * 组件事件同步服务
     * @param {*} appName: 应用名称
     * @param {*} executeFunc: 执行函数
     * @param {*} port: 端口
     * @param {*} url: websocket服务地址
     * @param {*} token: 身份认证
     * @return {WebScoket} synchWS
     */
    eventSynchronization(
      appName = "synchWS",
      executeFunc = () => {},
      port = 6801,
      url = "p6801",
      token
    ) {
      let synchWS = wiscomWebSocket.getWebSocket(appName, port, url, token);
      synchWS.keepAlive = "102:K_A";

      this._initEventHandle(appName, executeFunc, port, url, token, synchWS);

      return synchWS;
    }

    /**
     * 控制客户端注册服务
     * @param {*} appName: 应用名称
     * @param {*} executeFunc: 执行函数
     * @param {*} port: 端口
     * @param {*} url: websocket服务地址
     * @param {*} token: 身份认证
     * @return {WebScoket} controlWS
     */
    controlCenter(
      appName = "controlWS",
      executeFunc = () => {},
      port = 6802,
      url = "p6802",
      token
    ) {
      let controlWS = wiscomWebSocket.getWebSocket(appName, port, url, token);
      controlWS.keepAlive = "202:K_A";

      this._initEventHandle(
        appName,
        executeFunc,
        port,
        url,
        token,
        controlWS,
        "registerControl"
      );

      return controlWS;
    }
  }
  //初始化API
  //   window.initAPIPromise = initAPI();
})();
