<template>
  <div class="topo-attribute-layout" v-show="!!linkInfo.linkId">
    <Row type="flex" align="middle" class="mt-5 mb-5" style="padding: 0 16px">
      <Col span="4" class="text-left">编码 </Col>
      <Col span="20" class="text-left"
        ><span class="topo-text-select">{{ linkInfo.linkId }}</span>
      </Col>
      <Col span="4" class="text-left">数据</Col>
      <Col span="20" class="text-left"
        ><Button
          type="primary"
          size="small"
          class="mr-5"
          @click="handleMetaEditClick"
          >编辑</Button
        >
      </Col>
    </Row>

    <Collapse v-model="value1">
      <Panel name="1">
        样式
        <div slot="content">
          <Row type="flex">
            <Col span="4" class="pr-5">路径</Col>
            <Col span="20" class="pr-5"
              ><div class="path-point-list">
                <div
                  class="path-point-item"
                  v-for="(item, index) in linkInfo.pathPoints"
                  :key="index"
                >
                  <div
                    :class="[
                      activePointIndex === index
                        ? 'topo-point-index-active'
                        : '',
                      'path-point-index',
                    ]"
                    @click="selectPathPoint(index)"
                  >
                    {{ index + 1 }}
                  </div>
                  <div>
                    <Icon
                      type="md-add"
                      size="12px"
                      class="click"
                      style="margin-right: 3px"
                      @click="addPoint(index)"
                    />
                    <Icon
                      type="md-remove"
                      size="12px"
                      class="click"
                      @click="delPoint(index)"
                    />
                  </div>
                </div></div
            ></Col>
          </Row>
          <p style="padding: 5px; text-align: left">连接点：</p>
          <div class="path-link-point-list">
            <Row
              type="flex"
              align="middle"
              class="topo-node-property-item"
              v-for="(item, index) in linkInfo.pathPoints"
              :key="index"
            >
              <Col
                span="4"
                :class="[
                  activePointIndex === index ? 'topo-point-index-active' : '',
                ]"
                style="padding-right: 5px"
                >{{ index + 1 }}</Col
              >
              <Col span="8" offset="1">
                <InputNumber
                  :value="item.x"
                  :step="-1"
                  @on-change="handlePointChange($event, index, 'x')"
                ></InputNumber>
              </Col>
              <Col span="8" offset="1">
                <InputNumber
                  :value="item.y"
                  :step="-1"
                  @on-change="handlePointChange($event, index, 'y')"
                ></InputNumber>
              </Col>
            </Row>
          </div>

          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="4" class="pr-5">线宽</Col>
            <Col span="6"
              ><InputNumber
                style="width: 100%"
                size="small"
                :value="linkInfo.linkWidth"
                :min="0.01"
                @on-change="handleInputChange($event, 'linkWidth')"
              ></InputNumber>
            </Col>
            <Col span="4" class="pl-5">线型</Col>
            <Col span="8">
              <Select
                size="small"
                style="text-align: left"
                @on-change="handleLinkDashChange"
              >
                <Option
                  v-for="(item, index) in dasharrayOptions"
                  :value="index"
                  :key="index"
                  >{{ item.label }}</Option
                >
              </Select>
            </Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="4" class="pr-5">填充</Col>
            <Col span="6" style="text-align: left">
              <ColorPicker
                class="link-color-picker"
                size="small"
                :value="linkStyles.color || ''"
                :alpha="linkStyles.isBlock"
                @on-change="handlLinkStylesChange($event, 'color')"
            /></Col>

            <Col span="4" class="pr-5" v-if="linkStyles.isBlock">色块</Col>
            <Col span="8" style="text-align: left" v-if="linkStyles.isBlock">
              <ColorPicker
                class="link-fill-picker"
                size="small"
                :value="linkStyles.fill || ''"
                alpha
                @on-change="handlLinkStylesChange($event, 'fill')"
            /></Col>
          </Row>
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="4" class="text-left mb-5">发光</Col>
            <Col span="20" class="mb-5">
              <Row type="flex" align="middle">
                <Col span="6" class="mr-5">
                  <ColorPicker
                    class="link-drop-shadow-picker"
                    size="small"
                    alpha
                    :value="linkStyles.dropShadowColor || '#ffffff'"
                    @on-change="
                      handlLinkStylesChange($event, 'dropShadowColor')
                    "
                  />
                </Col>
                <Col span="6">
                  <InputNumber
                    size="small"
                    :min="0"
                    :value="linkStyles.dropShadowBlurRadius"
                    @on-change="
                      handlLinkStylesChange($event, 'dropShadowBlurRadius')
                    "
                  ></InputNumber>
                </Col>
              </Row>
            </Col>
          </Row>
          <Row
            type="flex"
            align="middle"
            class="topo-node-property-item"
            style="margin-bottom: 10px"
          >
            <Col span="4" class="pr-5">操作</Col>
            <Col span="20" style="text-align: left">
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleSetLinkClick('reverse')"
                >反向</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleSetLinkClick('dotted-line')"
                >虚线</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleSetLinkClick('line')"
                >实线</Button
              >
            </Col>
          </Row>
          <Row
            type="flex"
            align="middle"
            class="topo-node-property-item"
            style="margin-bottom: 10px"
          >
            <Col span="4" class="pr-5">箭头</Col>
            <Col span="20" style="text-align: left">
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleSetLinkArrowClick('start')"
                >起点</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleSetLinkArrowClick('end')"
                >终点</Button
              >
              <Button
                type="primary"
                size="small"
                @click="handleSetLinkArrowClick()"
                >取消</Button
              >
            </Col>
          </Row>
          <Row
            type="flex"
            align="middle"
            class="topo-node-property-item"
            style="margin-bottom: 10px"
          >
            <Col span="4" class="pr-5">变色</Col>
            <Col span="20" style="text-align: left">
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleChangeColorClick('green')"
                >绿</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleChangeColorClick('pink')"
                >粉</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleChangeColorClick('orange')"
                >橙</Button
              >
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="handleChangeColorClick('blue')"
                >蓝</Button
              >
              <Button
                type="primary"
                size="small"
                @click="handleChangeColorClick('reset')"
                >重置</Button
              >
            </Col>
          </Row>

          <Divider />
          <Row type="flex" align="middle" class="topo-node-property-item">
            <Col span="4">自定义</Col>
            <Col span="12" offset="8" class="text-right">
              <Icon
                type="md-add"
                class="click"
                @click="$refs.customAttribute.show()"
            /></Col>
          </Row>
        </div>
      </Panel>
      <Panel name="data">
        数据
        <div slot="content">
          <Row
            type="flex"
            align="middle"
            class="topo-node-property-item"
            style="margin-bottom: 10px"
          >
            <Col span="4" class="pr-5">类型</Col>
            <Col span="20" style="text-align: left">
              <Button
                type="primary"
                size="small"
                class="mr-5"
                @click="setLinkMetaType"
                >设置线路类型</Button
              >
            </Col>
          </Row>
        </div>
      </Panel>
      <Panel name="2">
        关联
        <div slot="content">
          <Row type="flex" align="middle">
            <Col span="4" class="pr-5 mb-5">起点 </Col>
            <Col
              span="14"
              class="text-left mb-5"
              style="display: flex; align-item: center"
            >
              <Button
                size="small"
                style="width: 100%; overflow: hidden"
                class="mr-5"
                @click="locateNode(linkInfo.fromObj)"
                :title="linkInfo.fromObj"
                >{{ linkInfo.fromObj }}</Button
              >
            </Col>
            <Col
              span="5"
              offset="1"
              class="mb-5"
              style="display: flex; justify-content: space-between"
            >
              <img
                src="../../assets/images/topo/select.png"
                class="mr-5 topo-attribute-icon"
                @click="handleSelectLink('from')"
              />
              <img
                src="../../assets/images/topo/delete.png"
                class="mr-5 topo-attribute-icon"
                @click="handleClearLink('from')"
              />
            </Col>

            <Col span="4" class="pr-5">终点 </Col>
            <Col
              span="14"
              class="text-left"
              style="display: flex; align-item: center"
            >
              <Button
                size="small"
                style="width: 100%; overflow: hidden"
                class="mr-5"
                @click="locateNode(linkInfo.endObj)"
                :title="linkInfo.endObj"
                >{{ linkInfo.endObj }}</Button
              >
            </Col>

            <Col
              span="5"
              offset="1"
              style="display: flex; justify-content: space-between"
            >
              <img
                src="../../assets/images/topo/select.png"
                class="mr-5 topo-attribute-icon"
                @click="handleSelectLink('end')"
              />
              <img
                src="../../assets/images/topo/delete.png"
                class="mr-5 topo-attribute-icon"
                @click="handleClearLink('end')"
              />
            </Col>
            <Divider />
            <Col span="4" class="pr-5">组件 </Col>
            <Col span="20">
              <Select
                size="small"
                :value="linkInfo.compClass"
                style="text-align: left"
                @on-change="handleInputChange($event, 'compClass')"
                clearable
              >
                <OptionGroup label="内部对象">
                  <Option
                    v-for="(item, index) in compontentsList.internalBindList"
                    :value="item.value"
                    :key="item.label + index + '-internal'"
                    >{{ item.label }}</Option
                  >
                </OptionGroup>
                <OptionGroup label="外部对象">
                  <Option
                    v-for="(item, index) in compontentsList.externalBindList"
                    :value="item.value"
                    :key="item.label + index + '-external'"
                    >{{ item.label }}</Option
                  >
                </OptionGroup>
              </Select>
            </Col>
            <Divider />
            <Col span="4" class="pr-5">数据</Col>
            <Col span="12" offset="8" class="text-right">
              <Icon type="md-add" class="click" @click="handleAddDataClick"
            /></Col>
            <Col span="24">
              <div style="text-align: left; padding: 10px 10px 0 10px">
                <Tag v-for="item in dataKeys" :key="item">{{ item }}</Tag>
              </div></Col
            >
            <Divider />
            <Col span="4" class="pr-5">图层</Col>
            <Col span="12" offset="8" class="text-right">
              <Icon type="md-add" class="click" @click="handleSelectMapClick"
            /></Col>
            <Col span="24">
              <div style="text-align: left; padding: 10px 10px 0 10px">
                <Tag v-for="item in mapNames" :key="item">{{ item }}</Tag>
              </div></Col
            >
            <Divider />
            <Col span="4" class="pr-5">动画</Col>
            <Col span="12" offset="8" class="text-right">
              <Icon type="md-add"
            /></Col>
          </Row>
        </div>
      </Panel>
      <Panel name="3"> 扩展 </Panel>
    </Collapse>
    <DataBindModal
      ref="dataBind"
      :bindData="linkInfo.bindData"
      @on-change="handleDataChange"
    ></DataBindModal>
    <MapSelectModal
      ref="selectMap"
      :bindMap="linkInfo.bindMap"
      @on-select="handleMapSelect"
    ></MapSelectModal>
    <CustomStylesModal
      ref="customAttribute"
      :styles="linkStyles.customStyles"
      @handleChange="handleCustomStyleChange"
    ></CustomStylesModal>
    <MetaDataEditModal ref="metaDataEdit" :info="linkInfo"></MetaDataEditModal>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import DataBindModal from "./Modal/DataBindModal.vue";
import MapSelectModal from "./Modal/MapSelectModal.vue";
import CustomStylesModal from "./Modal/CustomStylesModal.vue";
import MetaDataEditModal from "./Modal/MetaDataEditModal.vue";

import { dasharrayOptions } from "./constant";

export default {
  props: {
    compontentsList: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    DataBindModal,
    MapSelectModal,
    CustomStylesModal,
    MetaDataEditModal,
  },
  data() {
    return {
      value1: [1, 2, "data"],
      linkStyles: {},
      linkInfo: {
        bindMap: {},
        bindData: {},
      },
      activePointIndex: null,
      dasharrayOptions,
      linkObjType: "",
      dasharray: "2262",
    };
  },
  mounted() {
    this.initEventBus();
  },
  beforeDestroy() {
    this.$bus.off("handlePointSelected");
    this.$bus.off("bindObj");
    this.$bus.off("handleSelectLinkObj");
    this.$bus.off("onNodeLinkSelected");
  },
  computed: {
    ...mapState("topoStore", {
      mapList: "mapList",
      isSelectLinkObj: "isSelectLinkObj",
    }),
    mapNames() {
      const mapIdList = this.linkInfo?.bindMap?.mapId || [];
      return this.mapList
        .map((ele) => {
          if (mapIdList.includes(ele.mapId)) {
            return ele.mapName;
          }
        })
        .filter((ele) => !!ele);
    },
    dataKeys() {
      const keys = this.linkInfo?.bindData?.keys || [];
      return keys;
    },
  },
  methods: {
    ...mapMutations("topoStore", ["setSelectLinkObj"]),
    initData() {
      const { linkStyles, bindData, bindMap } = this.linkInfo;

      this.linkInfo.bindData = bindData ? bindData : {};
      this.linkInfo.bindMap = bindMap ? bindMap : {};

      this.linkStyles = linkStyles ? JSON.parse(linkStyles) : {};
    },
    initEventBus() {
      this.$bus.on("onNodeLinkSelected", (val = []) => {
        if (val.length === 1) {
          this.linkInfo = val[0];
          this.initData();
        }
      });
      // Path上的点监听
      this.$bus.on("handlePointSelected", ({ type, index }) => {
        // this.pointStyle = type;
        this.activePointIndex = index;
      });
      this.$bus.on("handleSelectLinkObj", (node) => {
        if (node.nodeId && this.isSelectLinkObj) {
          if (this.linkObjType === "from") {
            this.linkInfo.fromObj = node.nodeId;
          } else if (this.linkObjType === "end") {
            this.linkInfo.endObj = node.nodeId;
          }
          this.setSelectLinkObj(false);
          this.updateLinkInfo();
        }
      });
    },
    handleMetaEditClick() {
      this.$refs.metaDataEdit.show();
    },
    setLinkMetaType() {
      if (!this.linkInfo.metaData) {
        this.linkInfo.metaData = {};
      }
      this.linkInfo.metaData.type = "ConnectLine";
      this.updateLinkInfo();
    },
    handleAddDataClick() {
      this.$refs.dataBind.show();
    },
    handleSelectMapClick() {
      this.$refs.selectMap.show();
    },
    // input输入框改变  color选择器跟input的值要分开处理
    handleInputChange(val, type) {
      val = val instanceof Object ? val.target.value : val;
      this.linkInfo[type] = val;
      this.updateLinkInfo();
    },
    // 点击->绑定的节点，定位节点
    locateNode(nodeId) {
      this.$bus.emit("locateNode", nodeId);
    },
    // 点击node 绑定link
    handleSelectLink(type) {
      this.linkObjType = type;
      this.setSelectLinkObj(true);
    },
    handleClearLink(type) {
      this.setSelectLinkObj(false);
      if (type === "from") {
        if (!this.linkInfo.fromObj) return;
        this.linkInfo.fromObj = "";
      } else if (type === "end") {
        if (!this.linkInfo.endObj) return;
        this.linkInfo.endObj = "";
      }
      this.updateLinkInfo();
    },
    // 选择图层
    handleMapSelect(val) {
      this.linkInfo.bindMap.mapId = val;
      this.updateLinkInfo();
    },
    // 自定义样式
    handleCustomStyleChange(val) {
      this.linkStyles.customStyles = val;
      this.updateLinkInfo();
    },
    // 数据绑定
    handleDataChange(val) {
      this.linkInfo.bindData.keys = val;
      this.updateLinkInfo();
    },
    handlLinkStylesChange(val, key) {
      this.linkStyles[key] = val;
      this.updateLinkInfo();
    },
    // dash选择
    handleLinkDashChange(val) {
      if (val === 0) {
        this.linkInfo.linkType = "实线";
      } else {
        this.linkInfo.linkType = "虚线";
        this.linkStyles.dashedLink = dasharrayOptions[val].value;
      }

      this.linkStyles.isArrow = false;
      this.updateLinkInfo();
    },
    // 设置连线
    handleSetLinkClick(type) {
      //   let { fromObj, endObj } = this.linkInfo;

      switch (type) {
        case "reverse":
          {
            let { fromObj, endObj } = this.linkInfo;

            this.linkInfo.pathPoints.reverse();
            this.linkInfo.fromObj = endObj;
            this.linkInfo.endObj = fromObj;
          }

          break;
        case "dotted-line":
          this.linkInfo.linkType = "虚线";
          this.linkInfo.dashedLink = "10,10";
          break;
        case "line":
          this.linkInfo.linkType = "实线";
          this.linkInfo.dashedLink = "";
          break;
        default:
          break;
      }
      this.updateLinkInfo();
    },
    handleSetLinkArrowClick(type) {
      this.linkStyles.isArrow = !!type;
      type && (this.linkStyles.arrowDirection = type);
      this.updateLinkInfo();
    },
    handleChangeColorClick(val) {
      this.linkStyles.dynamicColor = val === "reset" ? "" : val;

      this.updateLinkInfo();
    },
    selectPathPoint(index) {
      this.activePointIndex = index;
      this.$bus.emit("selectAttributePathPoint", index);
    },
    addPoint(index) {
      if (this.linkInfo.pathPoints.length >= 20) {
        this.$Message.warning("超过最大点数量");
        return;
      }
      const currentPoint = this.linkInfo.pathPoints[index];
      const nextPoint = this.linkInfo.pathPoints[index + 1];
      const pointAdd = {
        x: nextPoint ? (currentPoint.x + nextPoint.x) / 2 : currentPoint.x + 50,
        y: nextPoint ? (currentPoint.y + nextPoint.y) / 2 : currentPoint.y,
      };
      this.linkInfo.pathPoints.splice(index + 1, 0, pointAdd);
      this.updateLinkInfo();
    },
    delPoint(index) {
      if (this.linkInfo.pathPoints.length <= 2) {
        this.$Message.warning("至少保留两个点");
        return;
      }
      this.linkInfo.pathPoints.splice(index, 1);
      this.updateLinkInfo();
    },
    // path的点位置修改
    handlePointChange(val, index, type) {
      this.generatePoints(val, index, type);
      this.updateLinkInfo();
    },
    generatePoints(value, index, type) {
      this.linkInfo.pathPoints[index][type] = +value;
      const points = this.linkInfo.pathPoints[index];
      switch (type) {
        case "L":
          points[index] = {
            x: points[index].x,
            y: points[index].y,
          };
          break;
        case "Q":
          points[index] = {
            x: points[index].x,
            y: points[index].y,
            q: {
              x: (points[index].x + points[index - 1].x) / 2 - 50,
              y: (points[index].y + points[index - 1].y) / 2 - 50,
            },
          };
          break;
        case "C":
          points[index] = {
            x: points[index].x,
            y: points[index].y,
            c: [
              {
                x: (points[index].x + points[index - 1].x - 50) / 2 - 50,
                y: (points[index].y + points[index - 1].y) / 2 - 50,
              },
              {
                x: (points[index].x + points[index - 1].x + 50) / 2 - 50,
                y: (points[index].y + points[index - 1].y) / 2 - 50,
              },
            ],
          };
          break;
        case "A":
          points[index] = {
            x: points[index].x,
            y: points[index].y,
            a: {
              rx: 50,
              ry: 50,
              rot: 0,
              laf: 1,
              sf: 1,
            },
          };
          break;
      }
    },
    updateLinkInfo() {
      this.linkInfo.linkStyles = JSON.stringify(this.linkStyles);
      this.$bus.emit("updateNodeSelectedList", this.linkInfo);
    },
  },
};
</script>
<style lang="scss">
.topo-point-index-active {
  background-color: #94a6cc;
  color: #fff;
}
.path-point-list {
  max-height: 100px;
  overflow: auto;
  margin-bottom: 10px;
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}
.path-point-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 16px;
  width: 63px;
  background-color: $inputBg;
  margin-bottom: 5px;
  margin-right: 5px;
  padding: 0 5px 0 2px;
  .path-point-index {
    width: 16px;
    height: 16px;
    cursor: pointer;
    border-radius: 6px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
    &:hover {
      background-color: #94a6cc;
      color: #fff;
    }
  }
}
.path-link-point-list {
  max-height: 200px;
  overflow: auto;
  width: 100%;
  margin-bottom: 10px;
}
.link-color-picker .ivu-select-dropdown {
  left: -50px !important;
}
.link-fill-picker .ivu-select-dropdown {
  left: -135px !important;
}
.link-drop-shadow-picker .ivu-select-dropdown {
  left: -50px !important;
}
</style>
