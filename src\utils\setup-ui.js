import {
  Upload,
  Spin,
  Avatar,
  Badge,
  Button,
  ButtonGroup,
  Card,
  Checkbox,
  Transfer,
  Col,
  Collapse,
  ColorPicker,
  Content,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Footer,
  Form,
  FormItem,
  Header,
  Icon,
  Input,
  InputNumber,
  Layout,
  Menu,
  MenuItem,
  MenuGroup,
  Submenu,
  Modal,
  Notice,
  Option,
  Page,
  Panel,
  Row,
  Select,
  Sider,
  Slider,
  Table,
  TabPane,
  Tabs,
  Tooltip,
  Tree,
  List,
  ListItem,
  Drawer,
  Split,
  Timeline,
  TimelineItem,
  RadioGroup,
  Radio,
  Steps,
  Step,
  Cascader,
  Divider,
  OptionGroup,
  Switch,
  Tag,
  CheckboxGroup,
  AutoComplete,
  LoadingBar,
  DatePicker,
  Message,
} from "view-design";
// import "view-design/dist/styles/iview.css";
import "../assets/my-theme/index.less";
// import { message } from "@/utils/resetMessage";

export const setupUI = (app) => {
  // eslint-disable-next-line vue/multi-word-component-names
  app.component("Tree", Tree);
  app.component("Upload", Upload);
  app.component("Layout", Layout);
  app.component("Row", Row);
  app.component("Col", Col);
  app.component("Header", Header);
  app.component("Footer", Footer);
  app.component("Content", Content);
  app.component("Sider", Sider);
  app.component("Button", Button);
  app.component("ButtonGroup", ButtonGroup);
  app.component("Icon", Icon);
  app.component("Input", Input);
  app.component("Select", Select);
  app.component("Option", Option);
  app.component("Transfer", Transfer);
  app.component("Spin", Spin);
  app.component("Checkbox", Checkbox);
  app.component("Form", Form);
  app.component("FormItem", FormItem);
  app.component("Dropdown", Dropdown);
  app.component("DropdownMenu", DropdownMenu);
  app.component("DropdownItem", DropdownItem);
  app.component("Avatar", Avatar);
  app.component("Badge", Badge);
  app.component("Modal", Modal);

  app.component("Menu", Menu);
  app.component("MenuItem", MenuItem);
  app.component("MenuGroup", MenuGroup);
  app.component("Submenu", Submenu);

  app.component("Card", Card);
  app.component("Tabs", Tabs);
  app.component("TabPane", TabPane);
  app.component("Slider", Slider);
  app.component("Table", Table);
  app.component("ColorPicker", ColorPicker);
  app.component("InputNumber", InputNumber);
  app.component("Page", Page);
  app.component("Tooltip", Tooltip);
  // app.component('Tree', Tree)
  app.component("Collapse", Collapse);
  app.component("Panel", Panel);
  app.component("List", List);
  app.component("ListItem", ListItem);
  app.component("Drawer", Drawer);
  app.component("Split", Split);
  app.component("Timeline", Timeline);
  app.component("TimelineItem", TimelineItem);
  app.component("RadioGroup", RadioGroup);
  app.component("Radio", Radio);
  app.component("Steps", Steps);
  app.component("Step", Step);
  app.component("Cascader", Cascader);
  app.component("Divider", Divider);
  app.component("OptionGroup", OptionGroup);
  app.component("ISwitch", Switch);
  app.component("Tag", Tag);
  app.component("CheckboxGroup", CheckboxGroup);
  app.component("AutoComplete", AutoComplete);
  app.component("LoadingBar", LoadingBar);
  app.component("DatePicker", DatePicker);

  LoadingBar.config({
    height: 3,
  });
  app.prototype.$Message = Message;
  app.prototype.$Notice = Notice;
  app.prototype.$Modal = Modal;
  app.prototype.$Spin = Spin;
  app.prototype.$Loading = LoadingBar;
  window.$Modal = Modal;
  window.$Message = Message;
};
