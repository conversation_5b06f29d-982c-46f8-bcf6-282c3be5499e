body {
  color: $textColor !important;
  background-color: transparent;
}

.ivu-tree-title,
.ivu-tooltip-inner {
  color: $textColor !important;
}

.ivu-tree-title-selected,
.ivu-tree-title-selected:hover,
.ivu-tree-title:hover {
  background-color: $titleColor !important;
}
.ivu-btn-default,
.ivu-btn-default:hover {
  background-color: transparent !important;
}
.ivu-btn-text:hover {
  background-color: $colorHover !important;
}
.ivu-modal-content,
.ivu-collapse,
.ivu-collapse-content {
  background-color: $baseBgColor !important;
}
.ivu-checkbox-inner {
  background-color: transparent !important;
}
.ivu-checkbox-inner:hover {
  border-color: $primaryColor !important;
}
.ivu-checkbox-checked .ivu-checkbox-inner {
  background-color: $primaryColor !important;
}
input,
.ivu-input-number-handler-wrap,
.ivu-page-item,
.ivu-page-prev,
.ivu-page-next,
.ivu-select-selection,
.ivu-select-dropdown,
.ivu-message-notice-content {
  background-color: $inputBg !important;
}
input {
  color: $inputColor !important;
}

.ivu-table,
.ivu-table td,
.ivu-notice-notice {
  background-color: $cardBg !important;
}

.ivu-table-row-highlight td {
  background-color: $selectBgHover !important;
}

.ivu-table-row-hover td {
  background-color: $colorHover !important;
}

.ivu-loading-bar {
  z-index: 3001 !important;
}
::placeholder {
  color: $inputPlacholderColor !important;
}

.ivu-form-item-error .ivu-input-group-prepend,
.ivu-form-item-error .ivu-input-group-append {
  background-color: $inputBg !important;
}

.ivu-date-picker-cells-month .ivu-date-picker-cells-cell-focused {
  color: $baseBgColor !important;
}

.ivu-date-picker-cells-cell:hover {
  color: $selectBgHover !important;
}
// .ivu-select-input {
//   height: 30px !important;
// }

// .ivu-select-small .ivu-select-input > .ivu-select-input {
//   height: 18px !important;
// }
.ivu-spin-fix {
  background-color: rgba(255, 255, 255, 0.5) !important;
}

.base-modal {
  overflow: hidden !important;
  .ivu-modal-content {
    border: 1px solid $borderColor !important;
  }
  .ivu-modal-body {
    padding: 0 !important;
  }
}

.ivu-select-multiple .ivu-select-item-selected {
  background-color: $selectBgHover !important;
}
