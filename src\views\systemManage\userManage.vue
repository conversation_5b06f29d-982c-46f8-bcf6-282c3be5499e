<template>
  <div class="userConfig">
    <div class="header">
      <div class="button">
        <div style="flex: 1; text-align: left">
          <Button
            @click="setUser()"
            class="addNewUser"
            type="primary"
            icon="ios-add"
            >新增</Button
          >
        </div>
        <!-- <div style="display:flex;align-items: center;margin-right: 5px">用户名</div> -->
        <div>
          <Input
            v-model="searchUserName"
            style="width: 300px"
            placeholder="用户名"
            clearable
            icon="ios-search-outline"
            @on-change="getSearchUser"
          />
        </div>
        <!-- <div>
          <Button type="primary" style="margin: 0 5px" @click="getSearchUser"><Icon type="md-search" />查询</Button>
        </div> -->
      </div>
    </div>
    <div class="body">
      <div class="userLeft">
        <Table
          :columns="columns7"
          :content="self"
          :data="tableList"
          :height="tableHeight"
          border
        >
          <template slot="action" slot-scope="{ row, index }">
            <Tooltip title="编辑" :disabled="true">
              <i
                class="iconfont user-icon icon-bianjisekuai"
                @click="edit(row, index)"
              ></i>
            </Tooltip>
            <Tooltip title="删除" :disabled="true">
              <i
                class="iconfont user-icon icon-shanchu"
                @click="remove(index)"
              ></i>
            </Tooltip>
          </template>
          <template slot="status" slot-scope="{ row }">
            <i-switch
              v-model="row.userStateTrans"
              size="small"
              @on-change="changeState(row, row.userStateTrans)"
            ></i-switch>
          </template>
        </Table>
        <Page
          style="margin-top: 1%; font-size: 15px"
          :total="totalOfUsers"
          :page-size="userPerLen"
          :current="userCurPage"
          show-total
          show-sizer
          @on-change="getUserCurPage"
          @on-page-size-change="getUserPerLen"
        ></Page>
      </div>
    </div>
    <Modal
      v-model="modal"
      :footer-hide="true"
      @on-cancel="cancel"
      title="用户信息"
    >
      <div class="userRight">
        <Form :label-width="100" :model="user" label-position="left">
          <FormItem label="用户名">
            <Input
              v-model="user.userName"
              maxlength="20"
              :disabled="updateFlag"
            />
          </FormItem>
          <FormItem label="展示名称">
            <Input v-model="user.displayName" maxlength="100" />
          </FormItem>
          <FormItem label="密码">
            <Input type="password" v-model="user.password" />
          </FormItem>
          <FormItem label="校验密码" v-if="user.password != ''">
            <Input type="password" v-model="user.passwordVerify" />
          </FormItem>
          <FormItem label="是否启用" v-if="updateFlag">
            <checkbox v-model="user.userStateTrans" disabled>启用</checkbox>
          </FormItem>
          <FormItem>
            <Button
              @click="handleSubmit('formValidate')"
              type="primary"
              style="float: right"
              >保存</Button
            >
          </FormItem>
        </Form>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: "user",
  data() {
    return {
      modal: false,
      self: this,
      lenArr: [5, 10, 20], // 每页显示长度设置
      pageLen: 5, // 可显示的分页数
      user: {
        id: 0,
        userName: "", //角色名称
        displayName: "", //角色描述
        password: "", //密码
        userState: "",
        passwordVerify: "", //校验密码
      },
      deleteUser: "",
      changeUser: {
        userId: "",
        userState: 0,
      },
      updateFlag: false,
      addNewUserFlag: false,
      param: { ROLE: "3" }, // 传递参数
      lists: [], // 表格原始数据
      tableList: [], // 分页组件传回的分页后数据
      selectedData: "",
      tableHeight: 0,
      columns7: [
        {
          title: "序号",
          align: "center",
          width: 100,
          type: "index",
        },
        {
          title: "用户名",
          align: "center",
          key: "userName",
        },
        {
          title: "显示名",
          align: "center",
          key: "displayName",
        },
        {
          title: "启用",
          align: "center",
          key: "userStateTrans",
          slot: "status",
          width: 180,
        },

        {
          title: "操作",
          slot: "action",
          width: 180,
          align: "center",
        },
      ],
      totalOfUsers: 0,
      userPerLen: 20,
      userCurPage: 1,
      searchUserName: "",
      allUserName: [],
    };
  },
  methods: {
    // 添加用户
    setUser() {
      this.modal = true;
      this.addNewUserFlag = true;
      this.updateFlag = false;
      this.user = {
        name: "admin",
        description: "admin",
        code: "123456",
        password: "",
      };
    },
    //删除判断
    remove(index) {
      this.deleteUser = this.tableList[index].id;
      this.modal = false;
      this.$Modal.confirm({
        title: "确定要删除该用户?",
        onOk: () => {
          this.deleteThis();
        },
        onCancel: () => {
          this.$Message.info("取消删除");
        },
      });
    },
    //改变用户状态判断
    changeState(data, userState) {
      this.changeUser = {
        userId: data.id,
        userState: userState ? 0 : 1,
      };
      this.modal = false;
      this.$Modal.confirm({
        title: "确定要改变用户状态?",
        onOk: () => {
          this.changeUserState(data);
        },
        onCancel: () => {
          this.$Message.info("取消修改");
          this.getdata();
        },
      });
    },
    cancel() {
      this.modal = false;
    },
    /**
     *删除用户信息
     */
    deleteThis() {
      let deleteData = {
        userId: this.deleteUser,
      };
      let that = this;
      this.$post(`/systemManage/deleteUser`, deleteData).then(
        (data) => {
          if (data.code == "0000") {
            this.$Message.success("删除成功！");
            this.deleteUser;
            this.changeUser = {
              userId: "",
              userState: 0,
            };
            if (this.searchUserName !== "") {
              for (let i = 0; i < that.tableList.length; i++) {
                if (that.tableList[i].id === that.deleteUser) {
                  that.tableList.splice(i, 1);
                }
              }
            } else {
              that.getdata();
            }
          } else {
            this.$Message.error("删除失败！");
          }
        },
        () => {
          this.$Message.error("删除失败！");
        }
      );
    },
    /**
     *提交用户信息
     */
    handleSubmit() {
      let that = this;
      let reg2 =
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,}/; //匹配密码正则
      //判空验证
      if (
        this.addNewUserFlag &&
        (this.user.userName == "" ||
          this.user.displayName == "" ||
          this.user.password == "")
      ) {
        this.$Message.warning("请确认表单已填写完成");
        return;
      }
      //两次密码验证
      if (this.user.password != "") {
        if (this.user.passwordVerify == "") {
          this.$Message.warning("请在校验密码中再次输入密码");
          return false;
        } else {
          if (this.user.password != this.user.passwordVerify) {
            this.$Message.warning("两次密码输入值不同，请重新输入");
            return false;
          }
        }
      }
      if (!reg2.test(this.user.password)) {
        this.$Message.warning(
          "密码长度不小于 8 个字符，必须同时包含大写字母（A-Z）、小写字母（a-z）、数字（0-9）、特殊字符及标点符号"
        );
        return false;
      }

      let total = {
        id: this.user.id,
        userName: this.user.userName,
        password: this.user.password,
        displayName: this.user.displayName,
      };
      if (this.updateFlag) {
        this.$post(`/systemManage/updateUser`, total).then((data) => {
          if (data.code == "0000") {
            this.$Message.success("修改成功！");
            this.getdata();
          } else {
            this.$Message.error(`${data.errorInfo}`);
          }
        });
      } else if (this.addNewUserFlag) {
        this.$post(`/systemManage/insertUser`, total).then((data) => {
          if (data.code == "0000") {
            this.$Message.success("新增成功！");
            if (
              that.searchUserName !== "" &&
              total.userName.indexOf(that.searchUserName) !== -1
            ) {
              let temp = _.cloneDeep(that.allUserName[0]);
              for (let one in total) {
                temp[one] = total[one];
              }
              that.tableList.splice(that.tableList.length, 0, temp);
            } else {
              that.getdata();
            }
          } else {
            this.$Message.error("新增失败！");
          }
        });
      }
      this.modal = false;
    },
    /**
     *获取用户列表信息
     */
    getdata() {
      this.$get(`/systemManage/getUserList`).then((data) => {
        if (data.data.code == "0000") {
          this.tableList = [];
          this.allUserName = [];
          data.data.data.forEach((item) => {
            this.allUserName.push({
              id: item.id,
              userName: item.userName,
              displayName: item.displayName,
              userState: item.userState,
              userStateTrans: item.userState == 0,
              password: "",
            });
          });
          this.totalOfUsers = this.allUserName.length;
          for (
            var i = (this.userCurPage - 1) * this.userPerLen;
            i < (this.userCurPage - 1) * this.userPerLen + this.userPerLen;
            i++
          ) {
            if (i < this.totalOfUsers) {
              this.tableList.push(this.allUserName[i]);
            }
          }
        } else {
          this.$Message.error(data.errorInfo);
        }
      });
    },
    //修改用户状态
    changeUserState(data1) {
      let that = this;
      this.$post(`/systemManage/changeUserState`, this.changeUser).then(
        (data) => {
          if (data.code == "0000") {
            this.$Message.success("修改用户状态成功");
            // for(let i = 0; i < that.allUserName.length; i++) {
            //     if(data1.id === that.allUserName[i].id) {
            //         let temp = that.allUserName[i]
            //         temp.userState = temp.userState === 0?1: 0
            //         that.$set(that.allUserName, i, temp)
            //     }
            // }
            this.getdata();
          } else {
            this.$Message.error("修改用户状态失败");
          }
        }
      );
    },
    edit(data, index) {
      this.modal = true;
      this.user.userName = data.userName;
      this.user.displayName = data.displayName;
      this.user.password = "";
      this.user.passwordVerify = "";
      this.user.userStateTrans = data.userState == 0 ? true : false;
      this.user.id = data.id;
      this.addNewUserFlag = false;
      this.updateFlag = true;
    },
    // 高亮图标
    highLight(id) {
      document.getElementById(id).style.opacity = 0.6;
    },
    // 还原
    backOrigi(id) {
      document.getElementById(id).style.opacity = 1;
    },
    //获取用户管理当前页码
    getUserCurPage(value) {
      this.userCurPage = value;
      if (this.searchUserName !== "") {
        this.getSearchUser();
      } else {
        this.getdata();
      }
    },
    //获取用户管理当前每页条数
    getUserPerLen(value) {
      this.userPerLen = value;
      if (this.searchUserName !== "") {
        this.getSearchUser();
      } else {
        this.getdata();
      }
    },
    //查询用户
    getSearchUser() {
      if (this.searchUserName !== "") {
        this.tableList = [];
        let cacheList = [];
        for (var i = 0; i < this.allUserName.length; i++) {
          if (
            this.allUserName[i].userName.indexOf(this.searchUserName) !== -1
          ) {
            cacheList.push(this.allUserName[i]);
          }
        }
        this.totalOfUsers = cacheList.length;
        if (cacheList.length > this.userPerLen) {
          for (
            var j = (this.userCurPage - 1) * this.userPerLen;
            j < (this.userCurPage - 1) * this.userPerLen + this.userPerLen;
            j++
          ) {
            if (j < cacheList.length) {
              this.tableList.push(cacheList[j]);
            }
          }
        } else {
          this.tableList = cacheList;
        }
      } else {
        this.getdata();
      }
    },
  },
  created() {
    let windowHeight = window.screen.height;
    this.tableHeight = 0.7 * windowHeight;
    this.getdata();
  },
};
</script>

<style lang="scss" scoped>
.userConfig {
  height: 96%;
  margin: 1% 2% 0 2%;
  // font-size: 30px;

  .header {
    //height: 5%;

    .button {
      height: 32px;
      display: flex;
      flex-direction: row;
      //display: flex;
      //justify-content: flex-end;
    }
  }

  .body {
    margin-top: 1%;
    height: 90%;
    width: 100%;
    display: flex;
    flex-direction: row;

    .userLeft {
      width: 100%;
    }
  }

  .user-icon {
    font-size: 20px;
    margin-right: 10px;
    color: #70c0e8;
    object-fit: contain;
    cursor: pointer;

    &:hover {
      opacity: 0.6;
    }
  }
}
</style>
