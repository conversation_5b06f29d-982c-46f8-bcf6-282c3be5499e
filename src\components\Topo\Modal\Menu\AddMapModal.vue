<template>
  <div>
    <Modal
      v-model="isVisible"
      :title="isEdit ? '编辑图层' : '添加图层'"
      @on-ok="ok"
      @on-cancel="hide"
    >
      <Form :model="formMap" label-position="right" :label-width="100">
        <FormItem label="名称">
          <Input v-model="formMap.mapName"></Input>
        </FormItem>
        <FormItem label="宽高">
          <Row>
            <Col span="11">
              <Input v-model="formMap.mapWidth"></Input>
            </Col>
            <Col span="11" offset="2">
              <Input v-model="formMap.mapHeight"></Input>
            </Col>
          </Row>
        </FormItem>
        <FormItem label="背景图">
          <Input v-model="formMap.background"></Input>
        </FormItem>
        <FormItem label="层级">
          <Input v-model="formMap.mapIndex"></Input>
        </FormItem>
        <FormItem label="外部对象">
          <Button @click="showCodeModal('externalBind')">添加外部数据</Button>
        </FormItem>
        <FormItem label="内部对象">
          <Button @click="showCodeModal('internalBind')">添加内部数据</Button>
        </FormItem>
        <!--TODO 必填 -->
        <FormItem label="目录">
          <Cascader
            :data="menuData"
            v-model="formMap.menuList"
            change-on-select
          ></Cascader>
        </FormItem>
      </Form>
    </Modal>

    <Modal
      v-model="isJScodeVisible"
      :title="this.codeType === 'externalBind' ? '外部对象' : '内部对象'"
      @on-ok="handleCodeSubmit"
      @on-cancel="handleCodeCancel"
    >
      <CodeMirror
        v-if="isJScodeVisible"
        :value="jsCode"
        ref="CodeMirror"
      ></CodeMirror>
      <div slot="footer">
        <Button type="error" @click="testCode"> 执行 </Button>
        <Button type="info" @click="handleCodeSubmit"> 确定 </Button>
        <Button @click="handleCodeCancel"> 取消 </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapState } from "vuex";
import CodeMirror from "@/components/CodeMirror/CodeMirror";

export default {
  name: "AddLayerModal",
  props: {
    menuData: {
      type: Array,
      default: () => [],
    },
  },
  components: { CodeMirror },
  data() {
    return {
      isVisible: false,
      formMap: {
        mapName: "",
        mapWidth: "",
        mapHeight: "",
        background: "",
        mapIndex: "",
        externalBind: "",
        internalBind: "",
        menuList: [],
      },
      jsCode: "",
      isJScodeVisible: false,
      codeType: "",
      isEdit: false,
      mapId: false,
    };
  },
  methods: {
    show(mapInfo) {
      this.isVisible = true;
      if (mapInfo) {
        this.isEdit = true;
        const {
          mapId,
          mapName,
          mapSize,
          background,
          mapIndex,
          externalBind,
          internalBind,
          menuId,
        } = mapInfo;

        // const menuList = getSelectedMenuList(this.menuData, menuId);
        // console.log("🚀 ~ show ~ menuList:", menuList);
        const [w, h] = mapSize.split("*").map((ele) => +ele);
        this.mapId = mapId;
        this.formMap = {
          mapName,
          mapWidth: w,
          mapHeight: h,
          background,
          mapIndex,
          externalBind,
          internalBind,
          menuList: [menuId],
        };
      }
    },
    testCode() {
      const string = this.$refs.CodeMirror.code;
      if (string.length == 0) {
        this.$Message.error(`请编辑需要执行的代码`);
        return;
      }
      try {
        if (
          ["[object Object]"].includes(
            Object.prototype.toString.call(JSON.parse(string))
          )
        ) {
          this.$Message.success(`代码格式无错误`);
        }
      } catch (e) {
        this.$Message.error(`代码异常，出现以下错误${e}`);
      }
    },
    hide() {
      this.isVisible = false;
      this.isEdit = false;
      this.formMap = {
        mapName: "",
        mapWidth: "",
        mapHeight: "",
        background: "",
        mapIndex: "",
        externalBind: "",
        internalBind: "",
        menuList: [],
      };
    },
    ok() {
      const {
        mapName,
        mapWidth,
        mapHeight,
        background,
        mapIndex,
        externalBind,
        internalBind,
        menuList,
      } = this.formMap;
      let params = {
        mapName,
        mapSize: `${mapWidth}*${mapHeight}`,
        background,
        mapIndex: +mapIndex,
        externalBind: externalBind || {},
        internalBind: internalBind || {},
        menuId: menuList.pop() || "0",
      };
      if (this.isEdit) {
        // 修改
        params.mapId = this.mapId;
        this.$post(`/topoEdit/updateMap`, params).then((data) => {
          if (data.code == "0000") {
            this.hide();
            this.$Message.success("修改成功");
            this.$emit("updateSuccess", params);
          }
        });
      } else {
        // 新增
        this.$post(`/topoEdit/insertMap`, params).then((data) => {
          if (data.code == "0000") {
            this.hide();
            this.$Message.success("添加成功");
            this.$emit("addSuccess");
          }
        });
      }
    },
    showCodeModal(type) {
      this.codeType = type;
      this.isJScodeVisible = true;
      const code = this.formMap[this.codeType];
      this.jsCode = typeof code === "object" ? JSON.stringify(code) : code;
    },
    handleCodeSubmit() {
      const { code } = this.$refs.CodeMirror;
      try {
        this.formMap[this.codeType] = code ? JSON.parse(code) : {};
        this.handleCodeCancel();
      } catch (e) {
        this.$Message.warning("JSON格式不正确");
      }
    },
    handleCodeCancel() {
      this.isJScodeVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
