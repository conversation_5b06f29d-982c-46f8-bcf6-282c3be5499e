function formatData({ nodes, links }) {
  const linksRes = links.map((ele) => {
    return generateNode({
      ...ele,
      type: "path",
      position: {},
    });
  });
  const nodesRes = nodes.map((ele) => {
    const [x, y] = ele.nodePosition.split(",");
    return generateNode({
      ...ele,
      type: ele.nodeType,
      position: { x: +x, y: +y },
    });
  });

  return [...linksRes, ...nodesRes];
}

function generatePathPoint(d) {
  let points = [];
  if (d) {
    const arr = d.split(" ").map((ele) => (isNaN(+ele) ? ele : +ele));
    for (let i = 0; i < arr.length; i++) {
      if (arr[i] === "M" || arr[i] === "L") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          type: arr[i],
        });
      } else if (arr[i] === "Q") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          q: {
            x: arr[i + 3],
            y: arr[i + 4],
          },
        });
      } else if (arr[i] === "C") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          c: [
            {
              x: arr[i + 3],
              y: arr[i + 4],
            },
            {
              x: arr[i + 5],
              y: arr[i + 6],
            },
          ],
        });
      } else if (arr[i] === "A") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          a: {
            rx: arr[i + 3],
            ry: arr[i + 4],
            rot: arr[i + 5],
            laf: arr[i + 6],
            sf: arr[i + 7],
          },
        });
      }
    }

    return points;
  }
}

function generateNode(options) {
  const { nodeType, linkPath, nodePosition, nodeSize, nodeText, linkId } =
    options;
  let newNode = {};
  if (linkId) {
    newNode = {
      type: "CustomPath",
      pathPoints: generatePathPoint(linkPath),
      tx: 0,
      ty: 0,
    };
  } else {
    const [x, y] = nodePosition.split(",").map((ele) => +ele);
    let [w, h] = nodeSize.split("*").map((ele) => +ele);
    h = h === Infinity ? w : h;

    switch (nodeType) {
      case "rect":
        newNode = {
          type: "CustomRect",
        };
        break;
      case "circle":
        newNode = {
          type: "CustomCircle",
        };
        break;
      case "image":
        newNode = {
          type: "CustomImage",
        };
        break;
      case "text":
        newNode = {
          type: "CustomText",
          nodeText: nodeText || " ",
        };
        break;
      default:
        newNode = {
          type: "CustomImage",
        };
        break;
    }
    newNode = {
      ...newNode,
      h,
      w,
      x,
      y,
      tx: 0,
      ty: 0,
      middleRotatePoint: {
        x: x + w / 2,
        y: y + h / 2,
      },
    };
  }

  newNode = Object.assign({}, options, newNode);

  return newNode;
}

self.onmessage = (ev) => {
  const list = formatData(ev.data);
  self.postMessage(list);
};
