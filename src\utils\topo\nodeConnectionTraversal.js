/**
 * Node Connection Traversal Utility
 *
 * This utility provides functions to traverse node connections in the topology editor.
 * It helps find connected nodes, links, and switch nodes to enable moving them together.
 */

import * as d3 from "d3";
import { formatId } from "@/utils/tools/format";

/**
 * Find connected elements (nodes and links) starting from a given node
 * Only gets the elements in the path: selected node -> connecting link -> switch node -> connecting link
 *
 * @param {Object} startNode - The node to start traversal from
 * @param {Array} nodeLinkList - The full list of nodes and links in the topology
 * @returns {Array} - Array of connected elements (nodes and links) in the limited path
 */
export function findConnectedElements(startNode, nodeLinkList) {
  if (!startNode || !startNode.nodeId) {
    return [];
  }

  // Result array to store connected elements
  const connectedElements = [startNode];

  // Find links directly connected to the start node
  const connectedLinks = nodeLinkList.filter(
    (item) =>
      item.linkId &&
      (item.fromObj === startNode.nodeId || item.endObj === startNode.nodeId)
  );

  // Process each connected link
  connectedLinks.forEach((link) => {
    // Add the link to the result
    connectedElements.push(link);

    // Find the node on the other end of the link
    let otherNodeId = null;
    if (link.fromObj === startNode.nodeId) {
      otherNodeId = link.endObj;
    } else {
      otherNodeId = link.fromObj;
    }

    if (otherNodeId) {
      const otherNode = nodeLinkList.find(
        (item) => item.nodeId === otherNodeId
      );

      // If the other node is a switch, add it and find its connected link
      if (otherNode && otherNode.nodeType === "switch") {
        connectedElements.push(otherNode);

        // Find links connected to the switch node
        // const switchLinks = nodeLinkList.filter(
        //   (item) =>
        //     item.linkId &&
        //     (item.fromObj === otherNode.nodeId ||
        //       item.endObj === otherNode.nodeId) &&
        //     item.linkId !== link.linkId // Exclude the link we already added
        // );

        // // Add the first link connected to the switch (if any)
        // if (switchLinks.length > 0) {
        //   connectedElements.push(switchLinks[0]);
        // }
      }
    }
  });

  return connectedElements;
}

/**
 * Move all connected elements together
 *
 * @param {Object} startNode - The node being moved
 * @param {Array} nodeLinkList - Full list of nodes and links
 * @param {Number} tx - X-axis translation
 * @param {Number} ty - Y-axis translation
 * @param {Function} updatePathPoint - Function to update path points
 * @returns {Array} - Array of elements that were moved
 */
export function moveConnectedElements(
  startNode,
  nodeLinkList,
  tx,
  ty,
  updatePathPoint
) {
  // Find all connected elements
  const connectedElements = findConnectedElements(startNode, nodeLinkList);

  // Track which elements were moved
  const movedElements = [];

  // Apply movement to all connected elements
  connectedElements.forEach((element) => {
    if (element.nodeId) {
      // Move node
      element.tx = tx;
      element.ty = ty;
      movedElements.push(element);
    } else if (element.linkId && element.pathPoints) {
      // Move link points
      const transformDiff = { tx, ty };
      element.pathPoints = element.pathPoints.map((point) => {
        return updatePathPoint(point, transformDiff);
      });
      element.tx = tx;
      element.ty = ty;
      movedElements.push(element);
    }
  });

  return movedElements;
}

/**
 * Highlight connected elements in the visualization
 *
 * @param {Object} startNode - The node to start from
 * @param {Array} nodeLinkList - Full list of nodes and links
 * @returns {Array} - Array of connected elements that were highlighted
 */
export function getConnectedElements(startNode, nodeLinkList) {
  // Find connected elements
  const connectedElements = findConnectedElements(startNode, nodeLinkList);
  // Highlight each element
  connectedElements.forEach((ele) => {
    d3.select(`#group-${formatId(ele.linkId || ele.nodeId)}`)
      .attr(
        "filter",
        "brightness(0) saturate(100%) invert(45%) sepia(38%) saturate(3355%) hue-rotate(353deg) brightness(98%) contrast(106%)"
      )
      .attr("class", "connected");
  });

  return connectedElements;
}

export function clearConnectedElementsHighlight() {
  d3.selectAll(".connected").attr("filter", null).classed("connected", false);
}
