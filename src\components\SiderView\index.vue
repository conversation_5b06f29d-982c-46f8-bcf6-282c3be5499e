<template>
  <aside
    class="sider-layout"
    :style="{
      left: siderLeft + 'px',
    }"
  >
    <Sider hide-trigger>
      <Menu
        :active-name="activeName"
        ref="mainMenu"
        theme="dark"
        :open-names="openNames"
        width="auto"
      >
        <template v-for="menu in menuList">
          <MenuItem
            v-if="!menu.children"
            :key="menu.id"
            :name="menu.id"
            :to="menu.url"
          >
            <Icon :type="menu.icon" />
            {{ menu.name }}
          </MenuItem>
          <Submenu v-else :name="menu.id" :Key="menu.id">
            <template #title>{{ menu.name }}</template>
            <MenuItem
              v-for="item in menu.children"
              :key="item.id"
              :name="item.id"
              :to="item.url"
              >{{ item.name }}</MenuItem
            >
          </Submenu>
        </template>
      </Menu>
    </Sider>
    <span class="sider-trigger" @click="handleMenuTrigger">
      <Icon type="md-menu" size="30" />
    </span>
    <div class="logout-layout">
      <span class="mb-5">{{ userName }}</span>
      <Button @click="logout">注销</Button>
    </div>
  </aside>
</template>

<script>
import Cookies from "js-cookie";

export default {
  props: {
    menuList: {
      type: Array,
      require: true,
    },
  },
  data() {
    return {
      activeName: "topo-edit",
      openNames: [],
      siderLeft: -200,
      userName: "",
    };
  },
  mounted() {
    this.userName = Cookies.get("displayName");
  },
  watch: {
    // 解决menu没有自动回显的问题
    $route: {
      handler({ name }) {
        this.activeName = name;
        this.siderLeft = -200;
      },
      immediate: true,
    },
  },
  methods: {
    updateActiveName() {
      this.$nextTick(() => {
        this.$refs.mainMenu.updateOpened();
        this.$refs.mainMenu.updateActiveName();
      });
    },
    handleMenuTrigger() {
      this.siderLeft = this.siderLeft === -200 ? 0 : -200;
    },
    logout() {
      this.$post("/logout").then((res) => {
        if (res.code === "0000") {
          this.$Message.success("注销成功");
          Cookies.remove("token");
          Cookies.remove("displayName");
          window.localStorage.clear();
          this.$router.replace("/login");
        }
      });
    },
  },
};
</script>

<style lang="scss">
.sider-layout {
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  left: -200px;
  top: 0;
  height: 100vh;
  z-index: 1001;
  transition: left 0.3s ease-in-out;
  background-color: $siderBgColor;
}
.ivu-layout-sider {
  flex: 1;
}
.sider-trigger {
  position: absolute;
  width: 36px;
  height: 42px;
  line-height: 48px;
  color: #fff;
  font-size: 18px;
  right: -36px;
  bottom: 0px;
  //   transform: translateY(-100%);
  background-color: $siderBgColor;
  border-top-right-radius: $borderRadius;
  border-bottom-right-radius: $borderRadius;
  cursor: pointer;
}
.logout-layout {
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
