<template>
  <div
    v-if="data.isVisible"
    :style="{
      transform: `translate(${data.position.x}px, ${data.position.y + 10}px)`,
    }"
    class="topo-dbclick-input-layout"
  >
    <Input
      type="textarea"
      class="topo-dbclick-input"
      v-model="inputVal"
    ></Input>
    <div style="display: flex; justify-content: space-between; margin-top: 5px">
      <Button type="primary" @click="handleSave">保存</Button>
      <Button @click="handleCancel">取消</Button>
    </div>
  </div>
</template>

<script>
export default {
  name: "NodeTexteditor",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      inputVal: "",
    };
  },
  mounted() {},
  methods: {
    initData(val) {
      this.inputVal = val;
    },
    handleSave() {
      this.$emit("onSave", this.inputVal);
      this.inputVal = "";
    },
    handleCancel() {
      this.$emit("onCancel");
      this.inputVal = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.topo-dbclick-input-layout {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  width: 150px;
}

.topo-dbclick-input {
}
</style>
