<template>
  <Modal
    v-model="isVisible"
    title="移除子节点"
    @on-ok="handleRemoveSublayer"
    @on-cancel="closeSublayerNodeRemoveModal"
  >
    <!-- <span>选择移除的选项：</span>
    <Checkbox v-model="isSelectLink">连线</Checkbox>
    <Checkbox v-model="isSelectNode">节点</Checkbox> -->
    <span>选择移出的子图层：</span>
    <CheckboxGroup
      v-model="sublayerRemoveSelectedList"
      @on-change="checkAllGroupChange"
    >
      <Checkbox
        v-for="item in sublayerCheckList"
        :key="item.sublayerId"
        :label="item.sublayerId"
        >{{ item.sublayerName }}</Checkbox
      >
    </CheckboxGroup>
  </Modal>
</template>

<script>
import { mapState, mapMutations } from "vuex";
export default {
  name: "SublayerNodeRemoveModal",
  props: {
    mapId: {
      type: String,
      default: "",
    },
    nodeSelectedList: {
      type: Array,
      default: () => [],
    },
    sublayerId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      isVisible: false,
      sublayerRemoveSelectedList: [],
    };
  },
  computed: {
    ...mapState("topoStore", {
      sublayerList: "sublayerList",
      sublayerSelectedList: "sublayerSelectedList",
    }),
    nodeLinkList() {
      const nodes = [];
      const links = [];
      this.nodeSelectedList.forEach((ele) => {
        ele.nodeId && nodes.push(ele);
        ele.linkId && links.push(ele);
      });

      return {
        nodes,
        links,
      };
    },
    // 获取多个元素公共的子图层
    sublayerCheckList() {
      if (!this.nodeSelectedList.length) return [];
      const list = this.sublayerSelectedList
        .map((ele) => {
          const item = this.sublayerList.find(
            (eleChild) => ele === eleChild.sublayerId
          );
          return item;
        })
        .filter((ele) => !!ele);
      return list;
    },
  },
  methods: {
    ...mapMutations("topoStore", ["setSublayerSelectedList"]),
    show() {
      this.isVisible = true;
    },
    checkAllGroupChange() {},
    handleRemoveSublayer() {
      if (!this.sublayerSelectedList.length) {
        return this.$Message.warning("请选择子图层");
      }

      const sublayerObj = {};

      this.sublayerRemoveSelectedList.forEach((sublayerId, index) => {
        sublayerObj[sublayerId] = { nodeIdList: [], linkIdList: [] };
        this.nodeSelectedList.forEach((nodeLink) => {
          if (nodeLink.sublayerList) {
            const isInclude = nodeLink.sublayerList.some((eleChild) => {
              return eleChild.sublayerId === sublayerId;
            });
            if (isInclude) {
              if (nodeLink.nodeId) {
                sublayerObj[sublayerId].nodeIdList.push(nodeLink.nodeId);
              } else {
                sublayerObj[sublayerId].linkIdList.push(nodeLink.linkId);
              }
            }
          }
        });
      });

      const fetchData = (list, sublayerId, type) => {
        const params = {
          mapId: this.mapId,
          sublayerId,
          objType: type,
          objIdList: list,
        };
        return this.$post(`/topoEdit/deleteTopoSublayer`, params);
      };

      const promiseList = [];

      for (const key in sublayerObj) {
        const { nodeIdList, linkIdList } = sublayerObj[key];

        if (nodeIdList.length) {
          promiseList.push(fetchData(nodeIdList, key, 1));
        }
        if (linkIdList.length) {
          promiseList.push(fetchData(linkIdList, key, 2));
        }
      }

      Promise.all(promiseList).then(() => {
        this.$Message.success("删除成功");
        // this.$bus.emit("removeNodeLinkSublayerSuccess");
        this.$bus.emit("deleteSublayerSuccess");
        this.$bus.emit("handleSingleNodeSelected", {});
        this.$bus.emit("onGetSublayerList");
        this.closeSublayerNodeRemoveModal();
      });
    },
    // 将删除的元素从屏幕中筛选出去
    removeNodeLink() {
      const newSublayerSelectedList = [];
      this.sublayerSelectedList.forEach((ele) => {
        if (!this.sublayerRemoveSelectedList.includes(ele)) {
          newSublayerSelectedList.push(ele);
        }
      });

      this.setSublayerSelectedList(newSublayerSelectedList);

      const list = this.nodeSelectedList.filter((ele) => {
        let isShow = false;
        newSublayerSelectedList.forEach((sublayerId) => {
          isShow =
            isShow ||
            ele.sublayerList.some(
              (eleChild) => eleChild.sublayerId === sublayerId
            );
        });

        return isShow;
      });

      console.log(list);
    },
    closeSublayerNodeRemoveModal() {
      this.isVisible = false;
      this.sublayerRemoveSelectedList = [];
    },
  },
};
</script>

<style lang="scss" scoped></style>
