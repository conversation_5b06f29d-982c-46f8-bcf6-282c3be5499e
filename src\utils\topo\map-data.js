import store from "@/store/";
import { getImageUrl } from "@/utils/assistant";

export const mapDataBase = {
  groupId: "base",
  groupName: "基本图元",
  objList: [
    {
      objType: "path",
      objName: "直线",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/line.svg"),
    },
    {
      objType: "text",
      objName: "文字",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/text.svg"),
    },
    {
      objType: "rect",
      objName: "矩形",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/rect.svg"),
    },
    {
      objType: "circle",
      objName: "圆形",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/circle.svg"),
    },
    {
      objType: "image",
      objName: "图片",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/image.svg"),
    },
    {
      objType: "jianxiu",
      objName: "检",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/jianxiu.svg"),
    },
    {
      objType: "ji",
      objName: "基",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/ji.svg"),
    },
    {
      objType: "t",
      objName: "T",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/t.svg"),
    },
    {
      objType: "switch",
      objName: "电路开关",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/switch.svg"),
    },
  ],
};

export const mapDataPower = {
  groupId: "power",
  groupName: "电站/电厂",
  objList: [
    {
      objType: "220kv_bdz",
      objName: "220kv",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/220kVbdz.svg"),
      metaData: {
        type: "TransStation",
      },
    },
    {
      objType: "220kv_xdy",
      objName: "220kv(小电源)",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/220kVxdy.svg"),
      metaData: {
        type: "TransStation",
        stationType: "小电源",
      },
    },
    {
      objType: "220kv-p_lsfd",
      objName: "风电(陆)",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/lsfd.svg"),
      metaData: {
        type: "PowerStation",
        powerType: "WIND",
        powerTypeByCustom: "陆",
      },
    },
    {
      objType: "220kv-p_hsfd",
      objName: "风电(海)",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/hsfd.svg"),
      metaData: {
        type: "PowerStation",
        powerType: "WIND",
        powerTypeByCustom: "海",
      },
    },
    {
      objType: "220kv-p_fdc",
      objName: "火电",
      groupId: "base",
      type: "PowerStation",
      objImg: require("../../assets/images/meta-icons/power/fdc.svg"),
      metaData: {
        type: "PowerStation",
        powerType: "THERMAL",
      },
    },
    {
      objType: "220kv-p_water",
      objName: "水电",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/water.svg"),
      metaData: {
        type: "PowerStation",
        powerType: "WATER",
      },
    },
    {
      objType: "220kv-p_gfdz",
      objName: "光伏",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/gfdz.svg"),
      metaData: {
        type: "PowerStation",
        powerType: "PV",
      },
    },
    {
      objType: "220kv-p_csxn",
      objName: "抽蓄",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/csxn.svg"),
      metaData: {
        type: "PowerStation",
        powerType: "PUMP",
      },
    },
    {
      objType: "220kv-p_energy",
      objName: "储能",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/storage.svg"),
      metaData: {
        type: "PowerStation",
        powerType: "ENERGY_STORAGE",
      },
    },
    {
      objType: "220kv-p_gas",
      objName: "天然气",
      groupId: "base",
      objImg: require("../../assets/images/meta-icons/power/gas.svg"),
      metaData: {
        type: "PowerStation",
        powerType: "Gas",
      },
    },
  ],
};

const generatePath = ({ x, y }) => {
  let path = [{ x, y }];
  for (let i = 1; i <= 2; i++) {
    path.push({ x: x + i * 100, y });
  }
  return path;
};

/**
 *
 * @param {*} type String
 * @param {*} position {x: number, y: number}
 * @param {*} mapId String
 */
export const generateNodeLink = (type, position, mapId) => {
  let params = {};

  const { x, y } = position;
  if (mapDataBase.objList.find((obj) => obj.objType === type)) {
    if (type === "path") {
      params = {
        mapId, //图层id
        linkType: "实线", //连线类型
        dashedLink: "5,5", //虚线类型
        compClass: "", //关联组件
        pathPoints: generatePath(position),
        linkWidth: 5, //线宽
        linkStyles: '{"color":"#a1a2a2"}', //线样式
        linkAnimations: { fadeOut: "12" }, //线动效
        fromObj: "", //起始对象id
        endObj: "", //终止对象id
        bindData: {}, //关联数据
        bindMap: {}, //关联图层
      };
    } else {
      const t = ["ji", "jianxiu", "switch"].includes(type) ? "image" : type;

      const { objImg } =
        store.state.topoStore.objectList.find((ele) => ele.objType === type) ||
        {};

      params = {
        mapId,
        nodeType: t,
        compClass: "",
        nodePosition: `${x},${y}`,
        nodeSize: type === "text" ? "120*30" : "100*100",
        rotate: 0,
        nodeStyles: `{"fill": "#19be6b","image":"${objImg}"}`,
        nodeText: type === "text" ? "双击修改文本" : "",
        fontSize: "16",
        fontColor: "#ffffff",
        textPosition: "0,0",
        textStyles: `{"color": "#ffffff"}`,
        bindData: {},
        bindMap: {},
      };
    }
  } else {
    const power = mapDataPower.objList.find((obj) => obj.objType === type);
    const {
      compClass,
      imgScale = 1,
      objImg,
      objType,
    } = store.state.topoStore.objectList.find((ele) => ele.objType === type) ||
    {};

    const nodeType = power ? power.objType.split("_")[0] : objType;
    const imgUrl = power ? power.objImg : objImg;

    params = {
      nodeType,
      mapId,
      compClass,
      position,
      nodePosition: `${x},${y}`,
      nodeSize: `100*${Math.round(100 / (imgScale || 1))}`,
      rotate: 0,
      nodeStyles: `{"fill": "#19be6b", "image": "${imgUrl}"}`,
      nodeText: "",
      fontSize: "16",
      fontColor: "#ffffff",
      textPosition: "0,0",
      textStyles: `{"color": "#19be6b"}`,
      metaData: power ? power.metaData : {},
      bindData: {},
      bindMap: {},
    };
  }

  return params;
};
