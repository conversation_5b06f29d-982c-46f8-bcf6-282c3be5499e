<template>
  <Modal v-model="isVisible" title="添加分组" @on-ok="ok" @on-cancel="hide">
    <Form :model="groupForm" label-position="right" :label-width="100">
      <FormItem label="分组名称">
        <Input v-model="groupForm.groupName"></Input>
      </FormItem>
    </Form>
  </Modal>
</template>

<script>
export default {
  name: "AddCompositionModal",
  props: {},
  data() {
    return {
      isVisible: false,
      groupForm: {
        groupName: "",
      },
    };
  },
  methods: {
    show() {
      this.isVisible = true;
    },
    hide() {
      this.isVisible = false;
      this.groupForm = {
        groupName: "",
      };
    },
    ok() {
      const { groupName } = this.groupForm;
      const params = {
        groupName,
      };
      this.$post(`/topoEdit/insertGroup`, params).then((data) => {
        if (data.code == "0000") {
          this.hide();
          this.$Message.success("添加成功");
          this.$emit("addSuccess");
        }
      });
    },
    cancel() {
      this.$Message.info("Clicked cancel");
    },
  },
};
</script>

<style lang="scss" scoped></style>
