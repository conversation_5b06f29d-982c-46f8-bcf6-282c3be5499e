import * as d3 from "d3";
import { formatId } from "@/utils/tools/format";

export const clearHighlight = () => {
  d3.selectAll(".highlight-rect").remove();
  d3.selectAll(".highlight-path").remove();
};
/**
 * @param {Array} nodeIds
 */
export const highlightNode = (nodes) => {
  nodes.forEach((node) => {
    const el = d3.select(`#group-${formatId(node.nodeId)}`);
    let rect = el.select(".highlight-rect");

    if (rect.empty()) {
      rect = el.append("rect").attr("class", "highlight-rect");
      rect
        .attr("width", node.w)
        .attr("height", node.h)
        .attr("x", node.x)
        .attr("y", node.y)
        .attr("fill", "#ffe17a80")
        .attr("pointer-events", "none")
        .classed("highlight", true);
    }
  });
};

/**
 * @param {Array} linkIds
 */
export const highlightLink = (links) => {
  links.forEach((link) => {
    const el = d3.select(`#group-${formatId(link.linkId)}`);
    let path = el.select(".highlight-path");

    if (path.empty()) {
      path = el
        .append("path")
        .attr("class", "highlight-path")
        .attr("d", link.linkPath)
        .attr("stroke", "#ffe17a80")
        .attr("stroke-width", link.linkWidth * 1.5)
        .attr("fill", "none")
        .attr("pointer-events", "none");
    }
  });
};
