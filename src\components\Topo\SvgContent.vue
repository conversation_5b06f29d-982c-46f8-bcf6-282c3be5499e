<template>
  <div
    id="topoSvgEditorLayout"
    class="topo-svg-editor-layout"
    ref="topoEditor"
    @click.self="cancelSelectNode()"
  >
    <span id="textWidth"></span>
    <NodeTextEditor
      ref="nodeTextEditor"
      :data="editInput"
      @onCancel="editInput.isVisible = false"
      @onSave="handleEditorSave"
    ></NodeTextEditor>
    <HotspotArea
      :svgSizePosition="svgSizePosition"
      :scale="+scale"
    ></HotspotArea>
    <div
      v-if="svgBgData.isOpen && svgBgData.path"
      class="topo-svg-bg"
      :style="{
        ...topoStyle,
        backgroundImage: svgBgData.path ? `url(${svgBgData.path})` : 'none',
        opacity: svgBgData.opacity || 1,
      }"
    ></div>
    <!-- <svg
        src="https://pic.imgdb.cn/item/60bf3228844ef46bb2c840fe.png"
        :style="{ ...topoStyle, width: '1907px', height: '921px' }"
        alt=""
        srcset=""
      ></svg> -->
    <svg
      ref="topoEdit"
      id="topoEdit"
      v-show="svgInfo.mapName"
      :style="topoStyle"
      tabindex="0"
      :viewBox="viewBox.content"
      @drop.prevent="drop"
      @dragover.prevent="() => {}"
      @contextmenu.prevent="handleContextmenuClick"
      @mousedown="handleSvgMousedown"
      @mousemove="handleSvgMousemove"
      @mouseover="handleSvgMouseover"
      @mouseenter="handleSvgMouseenter"
      @mouseleave="handleSvgMouseleave"
      @mouseup="handleSvgMouseup"
      @click="handleSvgClick"
      @dblclick="handleSvgDblclick"
      @keydown.stop="handleSvgKeydown"
      @keyup="listenerKeyupEvent"
    >
      <defs>
        <filter id="drop-shadow">
          <feDropShadow
            dx="0"
            dy="0"
            stdDeviation="14"
            flood-color="rgb(255, 226, 0)"
          />
        </filter>
      </defs>

      <path
        v-show="linkDrawPoints.length"
        :d="linkDrawD"
        fill="none"
        stroke-width="3"
        stroke="#a1a2a2"
      ></path>
      <!-- 动态生成不同类型的组件 -->
      <g
        :style="{ transform: `scale(${compScale})` }"
        class="topo-component-list"
      >
        <!-- 后续修改为统一组件，方便维护 -->
        <component
          v-for="(item, index) in nodeLinkList"
          :key="item.nodeId || item.linkId"
          :is="item.type"
          :data="item"
          :scale="+scale"
          :isBindNode="item.nodeId && nodeLocationId === item.nodeId"
          @cusmousedown="handleMousedown($event, index, item)"
          @cusmouseup="handleMouseup($event, index, item)"
          @cusdbclick="handleDbclick"
          class="topo-custom-component"
        ></component>
      </g>
      <g id="rotateLayout" style="display: none" :transform="transform">
        <line
          x1="0"
          y1="0"
          x2="0"
          y2="15"
          stroke-dasharray="3 3"
          stroke-width="2"
          :stroke="colorSelected"
        ></line>
        <circle
          r="4"
          id="rotatePoint"
          :fill="colorSelected"
          style="cursor: grab"
        ></circle>
      </g>
      <g id="dragLayout" style="display: none" :transform="transform">
        <circle
          v-for="item in dragPointDirection"
          :key="item.id"
          r="4"
          fill="transparent"
          :id="item.id"
          :style="{
            cursor: item.duration,
          }"
        ></circle>
      </g>
      <path
        v-show="pathGuidePoint.length"
        :d="pathGuideD"
        fill="none"
        stroke-width="3"
        stroke-dasharray="5,5"
        stroke="#57a3f3"
      ></path>
      <g v-show="isLocationLineVisible">
        <line
          :x1="viewBox.vX"
          :y1="mousePosition.y"
          :x2="viewBox.vW"
          :y2="mousePosition.y"
          style="stroke: rgb(255, 0, 0)"
          :stroke-width="1 / scale"
          stroke-dasharray="5,5"
          pointer-events="none"
        />
        <line
          :x1="mousePosition.x"
          :y1="viewBox.vY"
          :x2="mousePosition.x"
          :y2="viewBox.vH"
          style="stroke: rgb(255, 0, 0)"
          :stroke-width="1 / scale"
          stroke-dasharray="5,5"
          pointer-events="none"
        />
      </g>
      <rect
        v-for="item in groupPostionList"
        :key="item.groupId"
        v-show="nodeSelectedList.some((ele) => ele.groupId === item.groupId)"
        :id="item.groupId"
        :width="item.position.w"
        :height="item.position.h"
        :x="item.position.x"
        :y="item.position.y"
        :style="{
          transform: `translate(${item.tx}px, ${item.ty}px)`,
        }"
        fill="none"
        stroke="#00a8ff"
        stroke-width="4"
        stroke-dasharray="3 3"
      ></rect>
      <PathPoint
        v-for="item in getLinkSelectedList()"
        :key="item.linkId"
        :data="item"
        :mousePosition="mousePosition"
        :adsorbPosition="adsorbPosition"
        :isShiftKeyDown="isShiftKeyDown"
        :style="{
          transform: `translate(${item.tx}px, ${item.ty}px)`,
        }"
        @pointChange="pointChange"
      ></PathPoint>
      <ellipse
        v-if="this.isDrawBlock && linkDrawPoints.length > 1"
        id="topo-block-start"
        :rx="10"
        :ry="10"
        :cx="linkDrawPoints[0].x"
        :cy="linkDrawPoints[0].y"
        fill="#ffffff"
      />
    </svg>
    <SelectView
      :scale="+scale"
      :isSpaceDown="isSpaceDown"
      @onSelect="handleSelectNode"
    ></SelectView>
    <MouseRight
      :position="rightMenuPosition"
      :isVisible.sync="isMouseRightVisible"
      :nodeSelectedList="nodeSelectedList"
      :contextmenuSelectedLinkId="contextmenuSelectedLinkId"
      :mapId="svgInfo.mapId"
      @handleCloneClick="handleCloneClick"
      @handlePasteClick="handlePasteClick"
      @handleDeleteClick="handleDeleteClick"
      @handleCompositionClick="handleCompositionClick"
      @handleBreakClick="handleBreakClick"
      @handleLockClick="handleLockClick"
      @handleUnlockClick="handleUnlockClick"
      @onMenuClick="handleMenuClick"
    ></MouseRight>
  </div>
</template>
<script>
import { SVG } from "@svgdotjs/svg.js";
import "./svg.draggable.js";
import _ from "lodash";
import { mapState, mapMutations } from "vuex";
import SelectView from "@/components/Topo/SelectView.vue";
import CustomRect from "@/components/Topo/CustomRect.vue";
import CustomText from "@/components/Topo/CustomText.vue";
import CustomPath from "@/components/Topo/CustomPath.vue";
import CustomImage from "@/components/Topo/CustomImage.vue";
import CustomCircle from "@/components/Topo/CustomCircle.vue";
import MouseRight from "@/components/Topo/MouseRight.vue";
import NodeTextEditor from "@/components/Topo/NodeTextEditor.vue";
import PathPoint from "@/components/Topo/PathPoint.vue";
import HotspotArea from "@/components/Topo/HotspotArea.vue";
import { dragPointDirection } from "./constant";
import { scaleSvgY } from "@/utils/scaleSvg";
import { scaleSvgX } from "@/utils/scaleSvg1";
import { getImageUrl } from "@/utils/assistant";
import { generateNodeLink } from "@/utils/topo/map-data";
import {
  transformRotate,
  getPointAndOpposite,
  getNewDraw,
  updatePathPoint,
  generatePathD,
  getNodesInScope,
  getLeftPosition,
  getNodeLinkList,
  getGroupAreaSize,
  getEdgePosition,
  getNodeAndGroupPosition,
  getIntersectPoint,
  rotatePoint,
  getTransformPosition,
  distanceOfPointAndLine,
  checkPointLeftOrRight,
  getIntersectionPoint,
} from "./utils";
import {
  clearConnectedElementsHighlight,
  getConnectedElements,
} from "@/utils/topo/nodeConnectionTraversal";

let historyList = []; // 记录选择的元素（初始 无变化的）
let nodeLinkBeforeMoveList = [];

export default {
  components: {
    SelectView,
    CustomRect,
    CustomText,
    CustomPath,
    CustomImage,
    CustomCircle,
    MouseRight,
    NodeTextEditor,
    PathPoint,
    HotspotArea,
  },
  data() {
    return {
      scale: 1,
      scaleStep: 0.1,
      isCtrlKeyDown: false,
      isShiftKeyDown: false,
      draw: null,
      dragPointGroup: null,
      rotatePointGroup: null,
      isDrag: false,
      isMove: false, // 用于区分点击和鼠标按下
      isRotate: false,
      dragStartPoint: {},
      svgNodeList: [],
      dragPointDirection,
      nodeLinkList: [],
      allNodeLinkList: [], // 所有的数据
      activeIndex: null,
      activeType: "",
      mouseStartPoint: {
        x: 0,
        y: 0,
      },
      mousePosition: {
        x: 0,
        y: 0,
      },
      rotateStartPoint: {},
      currentRotate: 0,
      editInput: {
        isVisible: false,
        position: { x: 0, y: 0 },
      },
      selectedMapInfo: {},
      pointLinkNode: null,
      svgInfo: {},
      updateNodeAndLink: null,
      // 处理点击组件以外的地方，进行隐藏和清空
      isSvgMousedown: false,
      nodeMouseover: {},
      // 获取node绑定的path,可能有多个
      pathBindFromList: [],
      pathBindEndList: [],
      transformPreStep: { tx: 0, ty: 0 },
      rightMenuPosition: { x: 0, y: 0 },
      pastePosition: { x: 0, y: 0 },
      isMouseRightVisible: false,
      cloneNodeList: [],
      isPointDragMove: false,
      linkDrawPoints: [],
      drawLinkFromObj: "",
      // 鼠标点击绘制的path对象 draw.path
      mouseDrawPath: null,
      pathGuidePoint: [],
      // 每次点击，将元素拿到最上面，结束以后放到最下面，存储之前的位置
      tempIndex: null,
      adsorbPosition: {},
      nodeSelectedList: [],
      isLocationLineVisible: false,
      groupPostionList: [],
      svgSizePosition: { tx: 0, ty: 0, w: 0, h: 0 },
      svgSizeBackupPosition: {},

      nodeLocationId: "", //定位的节点
      // compScale: 1,
      isSvgDrag: false, // svg 拖动
      isSpaceDown: false,
      contextmenuSelectedLinkId: "",
      pointActiveIndex: 0,
      connectedElements: [],
    };
  },
  mounted() {
    this.initEventBus();
    this.initSVG();
    this.initScaleEvent();
    this.bindEvent();
    this.initPoint();
    this.initDebounce();
  },
  beforeDestroy() {
    this.$bus.off("onMapSlected");
    this.$bus.off("handlePointStyleChange");
    this.$bus.off("updateNodeSelectedList");
    this.$bus.off("handleTreeNodeSelected");
    this.$bus.off("handleTreeNodeDelete");
    this.$bus.off("alignClick");
    this.$bus.off("locateNode");
    this.$bus.off("deleteSublayerSuccess");
    this.$bus.off("insertTopoSublayerSuccess");
    this.$bus.off("onAttributeLinkClick");
    this.$bus.off("onMultiAddNode");
    this.$bus.off("onCompScaleChange");
    this.$bus.off("onCompScaleClick");
    this.$bus.off("handlePointSelected");
    this.$bus.off("onSvgVerticalScaleChange");
    this.$bus.off("onSublayerChange");
    this.$bus.off("onClearSvg");
    this.$bus.off("onMultSetting");
    this.$bus.off("onFormatTextWidth");
    this.$bus.off("onMultiChange");
    this.$bus.off("onModelCheck");
  },
  computed: {
    ...mapState("topoStore", {
      pointSelectedInfo: "pointSelectedInfo",
      objectList: "objectList",
      isLinkDraw: "isLinkDraw",
      svgBgData: "svgBgData",
      isLinkAlign: "isLinkAlign",
      isSelectLinkObj: "isSelectLinkObj",
      isGridBgShow: "isGridBgShow",
      isDrawBlock: "isDrawBlock",
      compScale: "compScale",
      colorSelected: "colorSelected",
      sublayerSelectedList: "sublayerSelectedList",
      versionSelected: "versionSelected",
      extraMetaData: "extraMetaData",
      allList: "allList",
      viewBoxScale: "viewBoxScale",
      rotationOffset: "rotationOffset",
      isSwitchMove: "isSwitchMove",
      isConnectionMove: "isConnectionMove",
    }),
    transform() {
      if (this.nodeSelectedList.length >= 2) return;
      if (
        this.nodeLinkList[this.activeIndex] &&
        this.activeType &&
        this.activeType !== "CustomPath"
      ) {
        const { tx, ty, rotate, middleRotatePoint } =
          this.nodeLinkList[this.activeIndex];
        if (middleRotatePoint) {
          return `translate(${tx}, ${ty})
              rotate(${rotate} ${middleRotatePoint.x} ${middleRotatePoint.y})`;
        }
      } else {
        return "translate(0,0)";
      }
    },
    pathGuideD() {
      let d = "";
      if (this.pathGuidePoint[0]) {
        this.pathGuidePoint.forEach((p, i) => {
          if (i === 0) {
            d += "M ";
          } else {
            d += "L ";
          }
          // d += `${p.x + 5} ${p.y + 5} `;
          d += `${p.x} ${p.y} `;
        });
      }
      return d;
    },
    linkDrawD() {
      let d = "";
      if (this.linkDrawPoints[0]) {
        this.linkDrawPoints.forEach((p, i) => {
          if (i === 0) {
            d += "M ";
          } else {
            d += "L ";
          }
          d += `${p.x} ${p.y} `;
        });
      }
      return d;
    },
    topoStyle() {
      const { w, h, tx, ty } = this.svgSizePosition;
      let background = {};
      if (this.svgBgData.isOpen && this.svgBgData.path) {
        background = {
          backgroundColor: "transparent",
        };
      } else if (this.isGridBgShow) {
        background = {
          background: `linear-gradient(to right,#ccc 1px,transparent 1px),
       linear-gradient(to bottom,#ccc 1px,transparent 1px)`,
          backgroundRepeat: "repeat",
          backgroundSize: "50px 50px",
        };
      } else if (this.svgInfo.background) {
        background = {
          backgroundImage: `url(${this.svgInfo.background})`,
        };
      } else {
        background = {
          background: "#272727",
        };
      }
      return {
        width: w + "px",
        height: h + "px",
        transform: `translate3d(${tx}px, ${ty}px, 0px) scale(${this.scale})`,
        ...background,
      };
    },
    viewBox() {
      const { w, h } = this.svgSizePosition;

      const vW = w / this.viewBoxScale;
      const vH = h / this.viewBoxScale;

      const vX = this.viewBoxScale === 1 ? 0 : -(vW - w) * 0.5;
      const vY = this.viewBoxScale === 1 ? 0 : -(vH - h) * 0.5;
      return {
        content: `${vX} ${vY} ${vW} ${vH}`,
        vX,
        vY,
        vW,
        vH,
      };
    },
  },
  watch: {
    "svgInfo.mapId"(val) {
      if (!val) return;
      const { mapSize } = this.svgInfo;
      const [w, h] = mapSize.split("*").map((ele) => +ele);
      this.svgSizePosition.w = w;
      this.svgSizePosition.h = h;
      // if (w > 7000 || h > 7000) {
      // this.scaleStep = 0.0;
      // } else {
      this.scaleStep = 0.06;
      this.setMapInfo(this.svgInfo);
      // }
      this.getSvgSizePosition({ w, h });
    },
    isLinkDraw(val) {
      // 在绘制过程中取消绘制
      // !val && this.finishDrawLink({});
      if (val) {
        this.cancelSelectNode();
      } else {
        this.linkDrawPoints = [];
        this.pathGuidePoint = [];
      }
    },
    nodeSelectedList(val) {
      this.nodeLinkList.forEach((ele, index) => {
        const id = ele.type === "CustomPath" ? "linkId" : "nodeId";
        const i = val.findIndex((eleChild) => eleChild[id] === ele[id]);
        if (i >= 0) {
          this.$set(this.nodeLinkList[index], "selected", true);
        } else {
          this.$set(this.nodeLinkList[index], "selected", false);
        }
      });
      this.$bus.emit("onNodeLinkSelected", val);
    },
    isCtrlKeyDown(val) {
      console.log("🚀 ~ file: SvgContent.vue:517 ~ isCtrlKeyDown ~ val:", val);
      // crtl释放 并且 compScale 不为1，说明页面元素进行了缩放
      if (val) {
        // console.log("list", list);
        // const list = [];
        // this.allNodeLinkList.forEach((ele) => {
        //   if (ele.nodeId) {
        //     const { metaData = {}, nodePosition, nodeSize } = ele;
        //     const [x, y] = nodePosition.split(",").map((ele) => +ele);
        //     const [w, h] = nodeSize.split("*").map((ele) => +ele);
        //     let width = w;
        //     let height = h;
        //     if (metaData.volt === "_220KV") {
        //       width = 43;
        //       height = 43;
        //     } else if (metaData.volt === "_1000KV") {
        //       width = 106;
        //       height = 106;
        //     } else if (metaData.volt === "_500KV") {
        //       if (metaData.type === "PowerStation") {
        //         width = 73;
        //         height = 73;
        //       } else if (metaData.type === "TransStation") {
        //         width = 86;
        //         height = 86;
        //       } else if (metaData.type === "ExchStation") {
        //         width = 79;
        //         height = 79;
        //       }
        //     }
        //     const x1 = x - (width - w) / 2;
        //     const y1 = y - (height - h) / 2;
        //     ele.w = width;
        //     ele.h = height;
        //     ele.x = x1;
        //     ele.y = y1;
        //     ele.nodeSize = `${width}*${width}`;
        //     ele.nodePosition = `${x1},${y1}`;
        //     list.push(ele);
        //   }
        // });
        // this.updateNodeAndLink(this.nodeLinkList);
      }
    },
  },
  methods: {
    ...mapMutations("topoStore", [
      "setSelectLinkObj",
      "setCompScale",
      "setSublayerSelectedList",
      "setSpinContent",
      "setSpinShow",
      "setAllNodeLinkList",
      "setMapInfo",
      "setNodeLinkBysyblayerList",
      "setViewBoxScale",
      "setNodeLinkSelected",
    ]),
    initEventBus() {
      this.$bus.on("onMapSlected", this.handleMapSlected);
      this.$bus.on("handlePointStyleChange", (type) => {
        this.setPathPoint(type);
      });
      this.$bus.on(
        "updateNodeSelectedList",
        this.updateNodeSelectedListByAttribute
      );
      // 左侧树状图点击选择
      this.$bus.on("handleTreeNodeSelected", ({ key }) => {
        for (let i = 0; i < this.nodeLinkList.length; i++) {
          const { linkId, nodeId } = this.nodeLinkList[i];
          if ((linkId || nodeId) === key) {
            this.selectNode(i, this.nodeLinkList[i]);
            break;
          }
        }
      });
      this.$bus.on("handleTreeNodeDelete", this.delNodeOrLink);
      this.$bus.on("alignClick", this.alignNodeLink);
      this.$bus.on("groupClick", (isGroup) => {
        isGroup ? this.handleCompositionClick() : this.handleBreakClick();
      });
      this.$bus.on("lockClick", (isLocked) => {
        isLocked ? this.handleLockClick() : this.handleUnlockClick();
      });
      this.$bus.on("showwholeSvgClick", () => {
        this.getSvgSizePosition(this.svgSizePosition);
      });
      // 修改层级
      this.$bus.on("tierClick", this.handleTierClick);
      // 连线绑定元素
      this.$bus.on("locateNode", (nodeId) => {
        this.nodeLocationId = nodeId;
      });
      this.$bus.on("deleteSublayerSuccess", this.fetchNodeLinkList);
      this.$bus.on("insertTopoSublayerSuccess", this.fetchNodeLinkList);
      this.$bus.on("onAttributeLinkClick", this.handleMenuClick);
      this.$bus.on("onMultiAddNode", this.multiAddNode);
      // this.$bus.on("onCompScaleChange", (val) => {
      //   this.compScale = val;
      // });
      this.$bus.on("onCompScaleClick", () => {
        this.scaleMapComponents();
      });
      this.$bus.on("handlePointSelected", ({ index }) => {
        this.pointActiveIndex = index;
      });
      this.$bus.on("onSvgVerticalScaleChange", this.scaleVerticalSvg);

      this.$bus.on("onSublayerChange", this.filterNodeLinkList);

      this.$bus.on("onClearSvg", () => {
        this.delNodeOrLink(this.nodeLinkList);
      });
      this.$bus.on("on:CancelAttach", this.cancelAttach);
      this.$bus.on("onMultSetting", (val) => {
        this.nodeLinkList = val;
      });

      this.$bus.on("onFormatTextWidth", this.formatTextWidth);
      this.$bus.on("onMultiChange", this.setNodeLink);
      this.$bus.on("onModelCheck", this.checkModel);
    },
    // 初始化SVG
    initSVG() {
      this.draw = SVG("#topoEdit");
    },
    // 初始化拖拽的点
    initPoint() {
      this.rotatePointGroup = this.draw.findOne("g#rotateLayout");
      this.dragPointGroup = this.draw.findOne("g#dragLayout");
      this.initDragPotint();
      this.initRotatePotint();
    },
    initDebounce() {
      this.updateNodeAndLink = _.debounce((list, isRecord) => {
        // 返回 不记录到recordList中
        !isRecord && this.setHistoryList(list);

        const { nodeList, linkList } = getNodeLinkList(list);
        let p1,
          p2 = null;
        if (nodeList.length) {
          p1 = this.$post(`/topoEdit/updateNode`, { nodeList });
        }
        if (linkList.length) {
          p2 = this.$post(`/topoEdit/updateLink`, { linkList });
        }
        list.length >= 500 && this.$Spin.show();
        Promise.all([p1, p2]).finally(() => {
          this.$Spin.hide();
          this.$Message.success("更新成功");
          //   每次更新后，将当前的数据存储到recordList中
          nodeLinkBeforeMoveList = _.cloneDeep(list);
          this.$bus.emit("onAllNodeLinkList", this.allNodeLinkList);
          this.setAllNodeLinkList(this.allNodeLinkList);
        });
      }, 300);
    },
    // 回到上一步时  刷新当前的数据
    refreshSelectNodeAndLink(list) {
      this.nodeSelectedList = list;
    },
    // 队列，最新的放后面，用的时候拿最新的
    // 选中和移动的数据 做对比 把选中的数据中 移动的元素替恢复到之前的状态
    setHistoryList(list = []) {
      if (historyList.length > 15) {
        historyList.shift();
      }

      const res = [];
      console.log("---", nodeLinkBeforeMoveList);
      list.forEach((ele) => {
        const el = nodeLinkBeforeMoveList.find(
          (eleChild) =>
            (ele.nodeId && ele.nodeId === eleChild.nodeId) ||
            (ele.linkId && ele.linkId === eleChild.linkId)
        );

        if (el) {
          el.tx = 0;
          el.ty = 0;
          res.push(el);
        }
      });

      historyList.push(_.cloneDeep(res));
    },
    // 回到上一步
    returnPrevStep() {
      if (!historyList.length) return;
      this.cancelSelectNode();
      const list = historyList.pop();

      list.forEach((ele) => {
        ele &&
          this.nodeLinkList.forEach((eleChild, index) => {
            if (
              (ele.nodeId && ele.nodeId === eleChild.nodeId) ||
              (ele.linkId && ele.linkId === eleChild.linkId)
            ) {
              this.$set(this.nodeLinkList, index, ele);
            }
          });
      });
      this.refreshSelectNodeAndLink([]);
      this.updateNodeAndLink(list, true);
      this.allNodeLinkList.forEach((ele, index) => {
        const item = list.find(
          (eleChild) =>
            (ele.nodeId && ele.nodeId === eleChild.nodeId) ||
            (ele.linkId && ele.linkId === eleChild.linkId)
        );
        if (item) {
          this.allNodeLinkList[index] = item;
        }
      });
    },
    // 保存所有的数据
    saveAllData() {
      this.updateNodeAndLink(this.allNodeLinkList);
    },
    setNodeLink(type) {
      if (type === "save") {
        this.updateNodeAndLink(this.nodeLinkList);
      } else {
        // this.nodeLinkList.forEach((ele) => {
        //   if (ele.nodeId) {
        //     if (ele.nodeType === "text") {
        //       if (
        //         ele.fontColor === "rgb(255,255,255)" ||
        //         ele.fontColor === "rgb(0,255,0)"
        //       ) {
        //         ele.x = ele.x + 23;
        //         ele.y = ele.y + 23;
        //       } else if (ele.fontColor === "#FFFF33") {
        //         ele.x = ele.x + 22;
        //         ele.y = ele.y + 22;
        //       }
        //     } else if (
        //       ele.nodeType === "500kv" ||
        //       ele.nodeType === "电网组件1"
        //     ) {
        //       ele.x = ele.x + 47.5;
        //       ele.y = ele.y + 47.5;
        //     } else if (ele.nodeType === "500kv-p") {
        //       ele.x = ele.x + 37.5;
        //       ele.y = ele.y + 37.5;
        //     }
        //   }
        //   ele.nodePosition = `${ele.x},${ele.y}`;
        //   ele.middleRotatePoint = {
        //     x: ele.x + ele.w / 2,
        //     y: ele.y + ele.h / 2,
        //   };
        // });
      }
    },
    checkModel(val) {
      console.log(val);
      this.nodeLinkList = val;
    },
    handleMapSlected(mapItem) {
      if (!mapItem || !mapItem.mapId) {
        this.svgInfo = {};
        return;
      }
      this.setSpinShow(true);
      this.$bus.emit("handleSingleNodeSelected", {});
      this.setNodeLinkSelected({});

      this.setViewBoxScale(1);
      this.activeIndex = null;
      this.groupPostionList = [];
      this.nodeSelectedList = [];
      this.allNodeLinkList = [];
      this.setSublayerSelectedList([]);

      // this.cloneNodeList = [];
      historyList = [];
      this.svgInfo = mapItem;
      if (!mapItem.mapId) return;
      this.selectedMapInfo = mapItem;
      this.fetchNodeLinkList();
      this.dragPointGroup.hide();
      this.rotatePointGroup.hide();
      this.nodeLocationId = "";
    },
    // 修改属性 更新列表
    updateNodeSelectedListByAttribute(val) {
      if (Array.isArray(val)) {
        this.nodeLinkList.forEach((ele, index) => {
          const item = val.find(
            (eleChild) =>
              (ele.nodeId && ele.nodeId === eleChild.nodeId) ||
              (ele.linkId && ele.linkId === eleChild.linkId)
          );
          if (item) {
            this.nodeLinkList[index] = item;
            this.$set(this.nodeLinkList, index, item);
          }
        });
        this.updateNodeAndLink(val);
        val.forEach((ele) => {
          const index = this.allNodeLinkList.findIndex(
            (eleChild) =>
              (ele.nodeId && ele.nodeId === eleChild.nodeId) ||
              (ele.linkId && ele.linkId === eleChild.linkId)
          );
          this.allNodeLinkList[index] = ele;
        });
        this.nodeSelectedList.length && (this.nodeSelectedList = val);
        this.$bus.emit("onNodeLinkSelected", this.nodeSelectedList);
      } else {
        this.$set(this.nodeLinkList, this.activeIndex, val);
        const index = this.allNodeLinkList.findIndex(
          (eleChild) =>
            (val.nodeId && val.nodeId === eleChild.nodeId) ||
            (val.linkId && val.linkId === eleChild.linkId)
        );
        this.allNodeLinkList[index] = val;
        // 更新右侧的值
        this.$bus.emit("handleSingleNodeSelected", val);
        this.setNodeLinkSelected(val);
        this.updateNodeAndLink([val]);
        this.setDragPointPosition(val);
      }
      this.setNodeLinkBysyblayerList(this.nodeLinkList);
    },
    getSvgSizePosition({ w, h }) {
      const containerWidth = this.$refs.topoEditor.offsetWidth;
      const containerHeight = this.$refs.topoEditor.offsetHeight;
      const xScale = w / containerWidth;
      const yScale = h / containerHeight;
      if (xScale > yScale) {
        // 以宽度填充
        this.scale = +(containerWidth / w);
        const scaleHeight = h * this.scale;

        this.svgSizePosition.tx = (w / 2) * (this.scale - 1);

        this.svgSizePosition.ty =
          (h / 2) * (this.scale - 1) +
          containerHeight * 0.5 -
          scaleHeight * 0.5;
      } else {
        // 以高度填充
        this.scale = +(containerHeight / h);
        const scaleWidth = w * this.scale;

        // 距离左边为0 + 布局宽度/2 - 实际上宽度/2 = 居中
        this.svgSizePosition.tx =
          (w / 2) * (this.scale - 1) + containerWidth * 0.5 - scaleWidth * 0.5;
        this.svgSizePosition.ty = (h / 2) * (this.scale - 1);
      }
      this.svgSizeBackupPosition = {
        size: _.cloneDeep(this.svgSizePosition),
        scale: this.scale,
      };
    },
    handleSvgMousedown(e) {
      //   if (this.versionSelected.versionId) return;
      this.isMouseRightVisible = false;
      if (e.button === 2) return;
      this.mouseStartPoint = this.getMousePoint(e);
      this.editInput.isVisible = false;
      this.isSvgDrag = true;
      if (e.target.nodeName === "svg" && e.target.id === "topoEdit") {
        this.isSvgMousedown = true;
      }
    },
    handleSvgMousemove(e) {
      //   if (this.versionSelected.versionId) return;
      this.mousePosition = this.getMousePoint(e);
      if (this.isSpaceDown && this.isSvgDrag) {
        // 移动画布
        this.moveSvg();
      } else {
        // 移动元素
        this.pointLinkNode = e.target;
        if (this.isDrag) {
          this.isMove = true;
        }
        this.setGuidePath(this.mousePosition);
        this.rotateNode(e);
        this.moveNode();
      }
    },
    handleSvgMouseover(e) {
      //   if (this.versionSelected.versionId) return;
      const { id } = e.target;
      if (!id) return;
      if (id === "topoEdit") {
        this.nodeMouseover = {};
        this.adsorbPosition = {};
      } else {
        this.nodeMouseover = this.nodeLinkList.find(
          (ele) => `rect-${ele.nodeId}` === id
        );
        if (
          this.nodeMouseover &&
          this.nodeMouseover.nodeId &&
          this.isPointDragMove
        ) {
          // this.setAdsorbPosition(e);
        } else {
          this.adsorbPosition = {};
        }
      }
    },
    handleSvgMouseenter() {
      //   if (this.versionSelected.versionId) return;
      this.isLocationLineVisible = true;
    },
    handleSvgMouseleave() {
      //   if (this.versionSelected.versionId) return;
      this.isLocationLineVisible = false;
      this.isSvgDrag = false;
    },
    handleSvgMouseup(e) {
      //   if (this.versionSelected.versionId) return;
      // button===2 表示鼠标右键抬起，不做处理
      if (e.button === 2) return;
      // 确定鼠标点击和释放都在SVG上面
      if (!this.isMove && this.isSvgMousedown && !this.isShiftKeyDown) {
        this.cancelSelectNode();
        clearConnectedElementsHighlight();
      } else {
        if (
          this.activeIndex !== null &&
          this.nodeLinkList[this.activeIndex].locked
        )
          return;
        // link属性->点击元素 选择绑定
        if (this.isSelectLinkObj) {
          this.nodeLocationId = this.nodeLinkList[this.activeIndex].nodeId;
          this.$bus.emit(
            "handleSelectLinkObj",
            this.nodeLinkList[this.activeIndex]
          );
          return;
        }
        if (this.isMove) {
          this.updateData();
          this.updateGroupLayout();
          this.connectedElements = [];
        }

        if (this.isPointDragMove) {
          // this.setLinkBind();
        } else {
          // 设置拖拽点的位置
          // 多选的话，不进行缩放和旋转操作
          if (
            this.activeIndex !== null &&
            [
              "CustomRect",
              "CustomCircle",
              "CustomImage",
              "CustomText",
            ].includes(this.activeType) &&
            this.nodeSelectedList.length <= 1
          ) {
            this.dragPointGroup.show();
            this.rotatePointGroup.show();
            this.setDragPointPosition(this.nodeLinkList[this.activeIndex]);
          }
        }
        this.$bus.emit("onNodeLinkSelected", this.nodeSelectedList);
        this.nodeLocationId = "";
      }
      this.isSvgDrag = false;
      this.isDrag = false;
      this.isRotate = false;
      this.isMove = false;
    },
    handleSvgClick(e) {
      //   if (this.versionSelected.versionId) return;
      const { x, y } = this.getMousePoint(e);
      this.drawLink(x, y, e.target);
    },
    handleSvgDblclick(e) {
      //   if (this.versionSelected.versionId) return;
      this.finishDrawLink(e.target, true);
    },
    updateNodeWithoutDebounce(params) {
      if (!params.mapId) return;
      const url =
        params.type === "CustomPath" ? "updateLink" : "updateNodeAndLink";
      if (params.type === "CustomPath") {
        params.linkPath = generatePathD(params, this.isDrawBlock);
      }
      this.$post(`/topoEdit/${url}`, params).then((data) => {});
    },
    // 获取节点和连线的列表
    fetchNodeLinkList() {
      const { mapId } = this.svgInfo;
      return new Promise((resolve, reject) => {
        this.$get(`/topoEdit/getNodeLinkListByMapId`, {
          mapId,
          versionId: this.versionSelected.versionId,
        })
          .then(({ data }) => {
            this.allNodeLinkList = [];
            this.nodeLinkList = [];
            this.dragPointGroup.hide();
            this.rotatePointGroup.hide();
            this.formatNodeList(data.data, () => {
              this.filterNodeLinkList();
            });
            resolve();
            //   const { links, nodes } = _.cloneDeep(data.data);
          })
          .catch(() => {
            this.setSpinShow(false);
          });
      });
    },
    formatTextWidth() {
      const textEl = document.getElementById("textWidth");
      function getTextWidth(text, size) {
        textEl.style.fontSize = `${size}px`;
        textEl.style["font-family"] = "楷体, 楷体_GB2312";

        const pattern = new RegExp("[\u4E00-\u9FA5]+");
        if (pattern.test(text)) {
          textEl.style["letter-spacing"] = "-3px";
        } else {
          textEl.style["letter-spacing"] = "0";
        }

        textEl.textContent = text;
        return {
          w: textEl.clientWidth,
          h: textEl.clientHeight,
        };
      }

      const textList = this.allNodeLinkList.filter(
        (ele) => ele.nodeType === "text"
      );
      const promiseList = textList.map((ele) => {
        return new Promise((resolve) => {
          let { nodeText, fontSize } = ele;
          fontSize = +fontSize;
          this.$nextTick(async () => {
            const { w, h } = await getTextWidth(nodeText, fontSize);
            ele.w = w;
            ele.h = h;
            ele.nodeSize = `${w}*${h}`;
            console.log(w, h);
            resolve();
          });
        });
      });
      Promise.all(promiseList).then(() => {
        this.updateNodeAndLink(textList);
      });
    },
    /**
     * 将后端返回的数据进行处理
     * Tips： 将Path放在最底层，方便吸附其他元素
     * SVG的层级：最后添加放在最上层
     */
    formatNodeList(data, cb) {
      const { pathname } = window.location;
      const worker = new Worker(`${pathname}static/formatNodeLink.js`);

      worker.postMessage(data);
      worker.onmessage = (e) => {
        this.allNodeLinkList = e.data;
        this.$bus.emit("onAllNodeLinkList", this.allNodeLinkList);
        this.setAllNodeLinkList(this.allNodeLinkList);

        this.extraMetaData &&
          this.allNodeLinkList.forEach((ele) => {
            ele.extraMetaData = this.extraMetaData[ele.nodeId || ele.linkId];
          });
        this.setSpinShow(false);
        cb && cb();
      };
    },
    // 根据子图层 过滤要显示的node和list
    filterNodeLinkList() {
      this.nodeSelectedList = [];

      let list = [];
      const nodeLinkSelectedSet = new Set();

      this.sublayerSelectedList.forEach((ele) => {
        let len = this.allNodeLinkList.length;
        let listTemp = [];
        for (let i = 0; i < len; i++) {
          const item = this.allNodeLinkList[i];
          const id = item.nodeId || item.linkId;
          const isInclude = nodeLinkSelectedSet.has(id);

          if (!isInclude) {
            if (item.sublayerList && item.sublayerList.length) {
              // 获取单个元素绑定的子图层列表
              const sublayerList = item.sublayerList.map(
                (eleChild) => eleChild.sublayerId
              );
              if (sublayerList.includes(ele)) {
                nodeLinkSelectedSet.add(id);
                listTemp.push(item);
              }
            } else if (ele === "other") {
              nodeLinkSelectedSet.add(id);
              listTemp.push(item);
            }
          }
        }
        list.unshift(...this.sortListByTier(listTemp));
      });

      this.nodeLinkList = list;
      this.setNodeLinkBysyblayerList(list);
      this.resetNodeList();
      this.$bus.emit("onNodeLinkSublayerListChange", list);
      // 重新设置层级显示
    },
    // 跟据图层设置层级 文字->图片->连线
    sortListByTier(list) {
      let linkList = [];
      let nodeList = [];
      let textList = [];

      list.forEach((ele) => {
        if (ele.nodeId) {
          if (ele.nodeType === "text") {
            textList.push(ele);
          } else {
            nodeList.push(ele);
          }
        } else {
          const linkStyle = JSON.parse(ele.linkStyles);
          if (linkStyle.isBlock) {
            linkList.unshift(ele);
          } else {
            linkList.push(ele);
          }
        }
      });
      //   linkList = linkList.sort((a, b) => {
      //     return a.metaData && a.metaData.index - b.metaData.index;
      //   });
      //   nodeList = nodeList.sort((a, b) => {
      //     return a.metaData && a.metaData.index - b.metaData.index;
      //   });
      //   linkList.forEach((ele) => {
      //     console.log(ele.linkId, ele.linkPath, ele.metaData.index);
      //   });
      return [...linkList, ...nodeList, ...textList];
    },
    // 移动画布
    moveSvg() {
      const svgTx =
        (this.mousePosition.x - this.mouseStartPoint.x) *
        this.scale *
        this.viewBoxScale;
      const svgTy =
        (this.mousePosition.y - this.mouseStartPoint.y) *
        this.scale *
        this.viewBoxScale;
      this.svgSizePosition.tx += svgTx;
      this.svgSizePosition.ty += svgTy;
    },
    // 修改直线的拐点类型
    setPathPoint(type) {
      const points = _.cloneDeep(
        this.nodeLinkList[this.activeIndex].pathPoints
      );
      const { index } = this.pointSelectedInfo;
      switch (type) {
        case "L":
          points[index] = {
            x: points[index].x,
            y: points[index].y,
          };
          break;
        case "Q":
          points[index] = {
            x: points[index].x,
            y: points[index].y,
            q: {
              x: (points[index].x + points[index - 1].x) / 2 - 50,
              y: (points[index].y + points[index - 1].y) / 2 - 50,
            },
          };
          break;
        case "C":
          points[index] = {
            x: points[index].x,
            y: points[index].y,
            c: [
              {
                x: (points[index].x + points[index - 1].x - 50) / 2 - 50,
                y: (points[index].y + points[index - 1].y) / 2 - 50,
              },
              {
                x: (points[index].x + points[index - 1].x + 50) / 2 - 50,
                y: (points[index].y + points[index - 1].y) / 2 - 50,
              },
            ],
          };
          break;
        case "A":
          points[index] = {
            x: points[index].x,
            y: points[index].y,
            a: {
              rx: 50,
              ry: 50,
              rot: 0,
              laf: 1,
              sf: 1,
            },
          };
          break;
      }
      this.$set(this.nodeLinkList[this.activeIndex], "pathPoints", points);
    },
    // 获取SVG中鼠标相对于 (0,0)的坐标
    getMousePoint(e) {
      const svg = this.$refs.topoEdit;

      const offsetX = e.offsetX / this.viewBoxScale + svg.viewBox.animVal.x;
      const offsetY = e.offsetY / this.viewBoxScale + svg.viewBox.animVal.y;
      return {
        x: offsetX,
        y: offsetY,
      };
    },
    // 鼠标右键释放
    handleContextmenuClick(e) {
      const { pageX, pageY, offsetX, offsetY } = e;
      this.rightMenuPosition = {
        x: pageX,
        y: pageY + 5,
      };
      this.pastePosition = { x: offsetX, y: offsetY };
      if (this.isLinkDraw) {
        this.finishDrawLink(e.target);
      } else {
        this.isMouseRightVisible = true;
        this.contextmenuSelectedLinkId = e.target.id;
      }
    },
    // 复制按钮点击
    handleCloneClick() {
      this.$Message.success("已复制选择的元素");
      const nodeSelectedList = this.nodeLinkList
        .filter((ele) => ele.selected)
        .map((ele) => {
          if (ele.linkId) {
            return {
              ...ele,
              fromObj: "",
              endObj: "",
            };
          } else {
            return ele;
          }
        });
      // .sort((a, b) => {
      //     console.log(a.pathPoints, b.pathPoints);
      //     return a.pathPoints[0].y - b.pathPoints[0].y;
      //   } else {
      //     return a.y - b.y;
      //   }
      // });
      if (!nodeSelectedList.length) return;
      // if (nodeSelectedList.length > 20) {
      //   this.$Message.warning("复制元素不能超过20个");
      // } else {
      this.cloneNodeList = nodeSelectedList;
      // }
    },
    // 粘贴
    /**
     * 键盘复制的
     */
    handlePasteClick(isKeyboardPaste) {
      if (!this.cloneNodeList.length) {
        this.$Message.warning("没有复制元素");
        return;
      }
      const list = _.cloneDeep(this.cloneNodeList);
      const leftPosition = getLeftPosition(list);

      list.forEach((cloneNode, index) => {
        if (!cloneNode.type) return;

        cloneNode.selected = false;
        cloneNode.isPaste = true;
        const { mapId } = this.selectedMapInfo;
        const { x, y } = this.pastePosition;

        if (cloneNode.mapId === mapId) {
          if (
            cloneNode.type === "CustomPath" &&
            cloneNode.pathPoints &&
            cloneNode.pathPoints.length
          ) {
            const { pathPoints } = cloneNode;
            const tx = isKeyboardPaste ? 20 : x - leftPosition.x;
            const ty = isKeyboardPaste ? 20 : y - leftPosition.y;
            cloneNode.pathPoints = pathPoints.map((ele) => {
              const { x, y } = ele;
              return {
                ...this.updatePath(ele, tx, ty),
                x: x + tx,
                y: y + ty,
              };
            });

            cloneNode.linkPath = JSON.stringify(cloneNode.pathPoints);
          } else {
            cloneNode.x = isKeyboardPaste
              ? cloneNode.position.x + 20
              : cloneNode.x - leftPosition.x + x;
            cloneNode.y = isKeyboardPaste
              ? cloneNode.position.y + 20
              : cloneNode.y - leftPosition.y + y;

            cloneNode.nodePosition = `${cloneNode.x},${cloneNode.y}`;
          }
        } else {
          cloneNode.mapId = mapId;
        }
        cloneNode.nodeId && (cloneNode.nodeId = "");
        cloneNode.linkId && (cloneNode.linkId = "");
        cloneNode.sublayerList = [];

        if (cloneNode.type === "CustomPath") {
          cloneNode.type = "path";
        }
        this.addNode(cloneNode, cloneNode.type, {
          x: cloneNode.x,
          y: cloneNode.y,
        });
      });
      this.nodeSelectedList = [];
      this.dragPointGroup.hide();
      this.rotatePointGroup.hide();
    },
    // 菜单删除
    handleDeleteClick() {
      this.delNodeOrLink();
    },
    cancelAttach() {
      const list = [];
      this.nodeLinkList.forEach((ele) => {
        if (ele.linkId) {
          ele.fromObj = "";
          ele.endObj = "";
          list.push(ele);
        }
      });

      this.updateNodeAndLink(list);
    },
    /**
     * 组合
     */
    handleCompositionClick() {
      const groupId = Date.now();
      this.nodeLinkList.forEach((ele, index) => {
        if (ele.selected) {
          this.$set(this.nodeLinkList[index], "groupId", groupId);
        }
      });
      this.groupPostionList.push({
        groupId,
        position: getGroupAreaSize(this.nodeSelectedList),
        tx: 0,
        ty: 0,
      });
      this.dragPointGroup.hide();
      this.rotatePointGroup.hide();
    },
    // 打散
    handleBreakClick() {
      const groups = Array.from(
        new Set(this.nodeSelectedList.map((ele) => ele.groupId))
      );
      this.groupPostionList = this.groupPostionList.filter(
        (ele) => !groups.includes(ele.groupId)
      );
      this.nodeLinkList.forEach((ele, index) => {
        if (groups.includes(ele.groupId)) {
          this.$set(this.nodeLinkList[index], "groupId", null);
        }
      });
    },
    // 锁定
    handleLockClick() {
      this.dragPointGroup.hide();
      this.rotatePointGroup.hide();
      this.nodeLinkList.forEach((ele, index) => {
        if (ele.selected) {
          this.$set(this.nodeLinkList[index], "locked", true);
        }
      });
    },
    // 解锁
    handleUnlockClick() {
      this.nodeLinkList.forEach((ele, index) => {
        if (ele.selected) {
          this.$set(this.nodeLinkList[index], "locked", false);
        }
      });
    },
    // 鼠标右键菜单点击
    handleMenuClick({ type, val }) {
      //   if (this.versionSelected.versionId) return;
      switch (type) {
        case "rotate":
          this.rotateLink(val);
          break;
        case "mirror":
          this.mirrorLink(val);
          break;
        case "alignLink":
          this.alignLink(val);
          break;
        case "paralleLink":
          this.paralleLink(val);
          break;
        default:
          break;
      }
    },
    // 如果是组合组件，层级也要一起调整
    // SVG的层级按照数据的顺序由低到高
    handleTierClick(index) {
      const len = this.nodeLinkList.length;
      switch (index) {
        case 1:
          // 置顶
          this.nodeSelectedList.forEach((eleSelected) => {
            const index = this.nodeLinkList.findIndex(
              (eleNode) =>
                eleNode.nodeId && eleNode.nodeId === eleSelected.nodeId
            );
            if (index < 0 || index === len - 1) return;
            const item = this.nodeLinkList.splice(index, 1)[0];
            this.nodeLinkList.push(item);
          });
          break;
        case 2:
          // 置底
          this.nodeSelectedList.forEach((eleSelected) => {
            const index = this.nodeLinkList.findIndex(
              (eleNode) =>
                eleNode.nodeId && eleNode.nodeId === eleSelected.nodeId
            );
            if (index < 0 || index === 0) return;
            const item = this.nodeLinkList.splice(index, 1)[0];
            this.nodeLinkList.unshift(item);
          });
          break;
        case 3:
          // 上移一层
          this.nodeSelectedList.reverse().forEach((eleSelected) => {
            const index = this.nodeLinkList.findIndex(
              (eleNode) =>
                eleNode.nodeId && eleNode.nodeId === eleSelected.nodeId
            );
            // 没找到或者最后一个不用处理
            if (index < 0 || index === len - 1) return;
            const item = this.nodeLinkList.splice(index, 1)[0];
            this.nodeLinkList.splice(index + 1, 0, item);
          });
          break;
        case 4:
          // 下移一层
          this.nodeSelectedList.forEach((eleSelected) => {
            const index = this.nodeLinkList.findIndex(
              (eleNode) =>
                eleNode.nodeId && eleNode.nodeId === eleSelected.nodeId
            );
            // 没找到或者在第一个不用处理
            if (index <= 0) return;
            const item = this.nodeLinkList.splice(index, 1)[0];
            this.nodeLinkList.splice(index - 1, 0, item);
          });
          break;

        default:
          break;
      }
      //   this.$bus.$emit("onAllNodeLinkList", this.nodeLinkList);
    },
    //鼠标左键 组件按下事件，获取组件的nodeId
    handleMousedown(e, index, item) {
      //   if (this.versionSelected.versionId) return;

      // 0 为鼠标左键、1 为中键、2 为右键
      if (e.button !== 0) return;
      if (!item.locked) {
        this.isDrag = true;
      }
      this.selectNode(index, item, true);
    },
    // 选中元素
    selectNode(index, item) {
      if (this.isSpaceDown) return;
      const { type, rotate } = item;
      // 绘制色块 只能选择path元素
      if (this.isDrawBlock && type !== "CustomPath") return;
      // 选择link绑定的元素
      if (this.isSelectLinkObj) {
        this.nodeLocationId = item.nodeId;

        this.$bus.emit("handleSelectLinkObj", item);
        return;
      }
      //   return;
      // 划线时不进行其他操作
      if (this.isLinkDraw) return;
      const id = type === "CustomPath" ? "linkId" : "nodeId";
      // 不在已选择的列表中，则新增
      if (
        this.nodeSelectedList.findIndex(
          (ele) => ele[id] && ele[id] === item[id]
        ) < 0
      ) {
        if (!this.isShiftKeyDown) {
          this.nodeSelectedList = [];
        }
        if (item.groupId) {
          this.nodeLinkList.forEach((ele) => {
            if (ele.groupId === item.groupId) {
              this.nodeSelectedList.push(ele);
            }
          });
        } else {
          this.nodeSelectedList.push(item);
        }
        // this.setHistoryList(this.nodeSelectedList);
      } else {
        if (this.isShiftKeyDown && !this.isMove) {
          this.nodeSelectedList = this.nodeSelectedList.filter((ele) => {
            const idEle = ele.type === "CustomPath" ? "linkId" : "nodeId";
            return ele[idEle] && ele[idEle] !== item[id];
          });
        }
      }
      this.setHistorySelectedList();

      this.activeIndex = index;
      this.$bus.emit("handleSingleNodeSelected", item);
      this.setNodeLinkSelected(item);
      clearConnectedElementsHighlight();
      if (this.isSwitchMove) {
        this.connectedElements = getConnectedElements(item, this.nodeLinkList);
      }

      this.activeType = type;
      this.currentRotate = rotate;
      this.rotatePointGroup.front();
      this.dragPointGroup.front();
      this.dragPointGroup.hide();
      this.rotatePointGroup.hide();
    },
    // 取消选择元素
    cancelSelectNode() {
      if (!this.nodeSelectedList.length) return;
      this.dragPointGroup.hide();
      this.rotatePointGroup.hide();
      this.activeIndex = null;
      this.nodeSelectedList.forEach((ele) => {
        this.$set(ele, "selected", false);
      });
      this.nodeSelectedList = [];
      this.isSvgMousedown = false;
      this.isPointDragMove = false;
      this.nodeLocationId = "";
      this.connectedElements = [];
      this.setSelectLinkObj(false);
      this.$bus.emit("handleSingleNodeSelected", {});
      this.setNodeLinkSelected({});
    },
    // 框选元素
    handleSelectNode({ x, y, width, height }) {
      //   if (this.versionSelected.versionId) return;
      this.nodeSelectedList = getNodesInScope(
        this.nodeLinkList,
        {
          pL: x,
          pR: x + width,
          pT: y,
          pB: y + height,
        },
        this.isDrawBlock
      );
    },
    // 保存当前选择的元素
    setHistorySelectedList() {
      // 找到元素关联的连线
      const linkBindList = [];
      this.nodeSelectedList.forEach((node) => {
        if (node.type !== "CustomPath") {
          this.nodeLinkList.forEach((ele, index) => {
            if (!ele.linkId) return;
            if (ele.fromObj === node.nodeId && !ele.selected) {
              linkBindList.push(ele);
            }
            if (ele.endObj === node.nodeId && !ele.selected) {
              linkBindList.push(ele);
            }
          });
        }
      });
      const set = new Set([...linkBindList, ...this.nodeSelectedList]);
      const list = Array.from(set);

      nodeLinkBeforeMoveList = _.cloneDeep(list);
    },
    // 将元素拿到最上面处理
    frontNodeIndex(index) {
      this.tempIndex = index;
      const item = this.nodeLinkList.splice(index, 1);
      this.nodeLinkList.push(...item);
    },
    // 拖拽结束后，恢复原来的位置
    recoverNodeIndex() {
      if (this.tempIndex === null) return;
      const item = this.nodeLinkList.pop();
      this.nodeLinkList.splice(this.tempIndex, 0, item);
      this.activeIndex = this.tempIndex;
      this.tempIndex = null;
    },
    // 按住Shift 画横线
    drawLink(x, y, target) {
      if (!this.isLinkDraw) return;
      let point = {};
      if (this.isShiftKeyDown && this.linkDrawPoints.length >= 1) {
        const prePoint = this.linkDrawPoints[this.linkDrawPoints.length - 1];
        if (Math.abs(prePoint.x - x) >= Math.abs(prePoint.y - y)) {
          point = { x, y: prePoint.y };
        } else {
          point = { x: prePoint.x, y };
        }
      } else {
        point = { x, y };
      }
      const { id, nodeName } = target;
      if (!this.linkDrawPoints.length && nodeName === "rect" && id) {
        this.drawLinkFromObj = id.split("-")[1];
      }
      // 点击开始的点结束
      if (id === "topo-block-start") {
        this.finishDrawLink({});
        return;
      } else {
        this.linkDrawPoints.push(point);
      }

      if (
        this.linkDrawPoints.length === 2 &&
        this.drawLinkFromObj &&
        this.isLinkAlign
      ) {
        const firstPoint = getIntersectPoint(
          this.linkDrawPoints,
          this.drawLinkFromObj,
          this.nodeLinkList
        );
        firstPoint && this.$set(this.linkDrawPoints, 0, firstPoint);
      }

      this.$set(this.pathGuidePoint, 0, point);
    },
    finishDrawBlock() {
      this.finishDrawLink({}, false);
    },
    /**
     * 生成path
     * 双击的时候会生成最后一个无效的点，需要删除
     */
    finishDrawLink(target, isDblclick) {
      // 双击时会添加两个相同的点，做过滤
      const len = this.linkDrawPoints.length;
      if (!len) return;
      if (
        isDblclick &&
        JSON.stringify(
          this.linkDrawPoints[len - 1] ===
            JSON.stringify(this.linkDrawPoints[len - 2])
        )
      ) {
        this.linkDrawPoints.pop();
      }

      this.pathGuidePoint = [];
      // this.$bus.emit("endDrawLink");
      if (len <= 1) return;
      const { id } = target;
      let endObj = "";
      if (id) {
        endObj = id.split("-")[1];
        if (this.isLinkAlign) {
          const point = getIntersectPoint(
            this.linkDrawPoints.slice(-2),
            endObj,
            this.nodeLinkList,
            this.isLinkAlign
          );
          if (point) {
            this.$set(
              this.linkDrawPoints,
              this.linkDrawPoints.length - 1,
              point
            );
          }
        }
      }
      const linkOption = {
        fill: this.isDrawBlock ? "rgb(82,174,109)" : "none",
        color: "#a1a2a2",
        isBlock: this.isDrawBlock,
      };
      let params = {
        mapId: this.selectedMapInfo.mapId, //图层id
        linkType: "实线", //连线类型
        dashedLink: "5,5", //虚线类型
        compClass: "", //关联组件
        pathPoints: this.linkDrawPoints,
        linkWidth: 5, //线宽
        linkStyles: JSON.stringify(linkOption), //线样式
        linkAnimations: { fadeOut: "12" }, //线动效
        fromObj: this.drawLinkFromObj, //起始对象id
        endObj, //终止对象id
        bindData: {}, //关联数据
        bindMap: {}, //关联图层
      };
      this.addNode(params, "path", {});
    },
    // 辅助线显示
    setGuidePath({ x, y }) {
      if (!this.isLinkDraw) return;
      const firstPoint = this.pathGuidePoint[0];
      if (this.isShiftKeyDown) {
        if (Math.abs(firstPoint.x - x) >= Math.abs(firstPoint.y - y)) {
          this.$set(this.pathGuidePoint, 1, { x, y: firstPoint.y });
        } else {
          this.$set(this.pathGuidePoint, 1, { x: firstPoint.x, y });
        }
      } else {
        this.$set(this.pathGuidePoint, 1, { x, y });
      }
    },
    handleMouseup($event, index, item) {
      // 更新transform 旋转的中心点
      this.isDrag = false;
    },
    // 文字双击
    handleDbclick() {
      const { x, y, h, nodeText } = this.nodeLinkList[this.activeIndex];
      const { left, top } = this.$refs.topoEdit.getBoundingClientRect();
      this.$refs.nodeTextEditor.initData(nodeText);
      this.editInput = {
        isVisible: true,
        position: {
          x: left + x * this.scale,
          y: top + y * this.scale + h * this.scale,
        },
      };
    },
    // 输入更新文字
    handleEditorSave(val) {
      if (!val) return;
      nodeLinkBeforeMoveList = [
        structuredClone(this.nodeLinkList[this.activeIndex]),
      ];

      this.nodeLinkList[this.activeIndex].nodeText = val;
      this.editInput.isVisible = false;
      this.$bus.emit(
        "handleSingleNodeSelected",
        this.nodeLinkList[this.activeIndex]
      );
      this.setNodeLinkSelected(this.nodeLinkList[this.activeIndex]);

      this.updateNodeAndLink([this.nodeLinkList[this.activeIndex]]);
    },
    // path点拖拽结束
    pointChange({ points, pointIndex, isPointDragMove }) {
      this.isPointDragMove = isPointDragMove;
      this.$set(
        this.nodeLinkList[this.activeIndex].pathPoints,
        pointIndex,
        points[pointIndex]
      );
      this.updateNodeAndLink([this.nodeLinkList[this.activeIndex]]);
    },
    // 当拖拽Path的起始和结束点 遇到其他组件，进行吸附
    setLinkBind() {
      if (this.activeIndex === null || this.activeIndex === undefined) return;
      const { index } = this.pointSelectedInfo;
      const { type, pathPoints } = this.nodeLinkList[this.activeIndex];
      if (type !== "CustomPath") return;
      if (this.nodeMouseover && this.nodeMouseover.nodeId) {
        const { nodeId, x, y } = this.nodeMouseover;
        if (index === 0) {
          this.$set(this.nodeLinkList[this.activeIndex], "fromObj", nodeId);
        } else if (index === pathPoints.length - 1) {
          this.$set(this.nodeLinkList[this.activeIndex], "endObj", nodeId);
        }
      } else {
        if (index === 0) {
          this.$set(this.nodeLinkList[this.activeIndex], "fromObj", "");
        } else if (index === pathPoints.length - 1) {
          this.$set(this.nodeLinkList[this.activeIndex], "endObj", "");
        }
      }

      this.nodeMouseover = {};
      this.isPointDragMove = false;
      this.updateNodeAndLink([this.nodeLinkList[this.activeIndex]]);
      this.adsorbPosition = {};
    },
    /**
     * 设置吸附的点
     * 拖拽第一个或者最后一个点，移动到其他节点上面时，获取当前节点的边界，然后进行吸附
     */
    setAdsorbPosition(e) {
      let startPoint = {};
      const { pathPoints } = this.nodeLinkList[this.activeIndex];
      if (pathPoints.length <= 1) return;
      if (
        this.pointActiveIndex !== 0 &&
        this.pointActiveIndex !== pathPoints.length - 1
      )
        return;
      if (this.pointActiveIndex === 0) {
        startPoint = pathPoints[1];
      } else if (this.pointActiveIndex === pathPoints.length - 1) {
        startPoint = pathPoints[this.pointActiveIndex - 1];
      }
      const centerPoint = this.nodeMouseover.middleRotatePoint;
      this.adsorbPosition = getIntersectionPoint(
        startPoint,
        centerPoint,
        this.nodeMouseover.w / 2
      );
    },
    // 拖拽后更新位置 把transform替换成x,y
    updateData() {
      let linkBind = [];
      let groupMoveList = {};
      const list = [];
      this.nodeLinkList.forEach((ele, index) => {
        const isUpdate =
          ele.selected ||
          (this.isSwitchMove && this.connectedElements.includes(ele));
        if (isUpdate) {
          if (ele.type === "CustomPath") {
            const { tx, ty, pathPoints, groupId } = ele;
            const points = pathPoints.map((ele) => {
              const { x, y } = ele;
              return {
                ...this.updatePath(ele, tx, ty),
                x: +x + tx,
                y: +y + ty,
              };
            });
            if (groupId && !groupMoveList[groupId]) {
              groupMoveList[groupId] = { groupId, tx, ty };
            }
            this.$set(this.nodeLinkList[index], "pathPoints", points);
            this.nodeLinkList[index].tx = 0;
            this.nodeLinkList[index].ty = 0;
          } else {
            const { w, h, x, y, tx, ty, groupId } = ele;
            const xMove = +x + tx;
            const yMove = +y + ty;
            this.nodeLinkList[index].x = xMove;
            this.nodeLinkList[index].y = yMove;
            this.nodeLinkList[index].tx = 0;
            this.nodeLinkList[index].ty = 0;
            this.nodeLinkList[index].middleRotatePoint = {
              x: xMove + w / 2,
              y: yMove + h / 2,
            };
            this.nodeLinkList[index].nodePosition = `${xMove},${yMove}`;
            // 获取组合框的位移
            if (groupId && !groupMoveList[groupId]) {
              groupMoveList[groupId] = { groupId, tx, ty };
            }
            if (!this.isSwitchMove && this.isConnectionMove) {
              // 拖拽结束后，更新关联的线段
              linkBind.push(...this.getBindLink(ele));
            }
          }
          list.push(ele);
        }
      });
      this.transformPreStep = { tx: 0, ty: 0 };
      this.moveGroupLayout(Object.values(groupMoveList));

      // const set = new Set([...list, ...linkBind]);
      const set = new Set([...list, ...linkBind]);

      const res = Array.from(set);

      this.updateNodeAndLink(res);
    },
    // 接口更新更新关联的线段
    // 若线段 跟 绑定的元素 已经同时被选择，则跳过
    // 筛选连线、不在已选择列表中的连线、有绑定值的连线
    getBindLink(node) {
      const { nodeId } = node;
      return this.nodeLinkList.filter(
        (ele) => ele.linkId && (ele.fromObj === nodeId || ele.endObj === nodeId)
      );
    },
    // 移动后对应的点也要进行变换
    updatePath(point, tx, ty) {
      let res = {};
      if (point.a) {
        res.a = point.a;
      } else if (point.c) {
        res.c = point.c.map((ele) => {
          return {
            x: ele.x + tx,
            y: ele.y + ty,
          };
        });
      } else if (point.q) {
        const { x, y } = point.q;
        res.q = {
          x: x + tx,
          y: y + ty,
        };
      }
      return res;
    },
    // 纵向拉伸svg
    scaleVerticalSvg({ svgVerticalScale, directionByScale, isSave }) {
      this.nodeSelectedList = [];
      if (isSave) {
        this.updateNodeAndLink(this.allNodeLinkList);
        this.updateSvg();
      } else {
        const svgSizePosition = _.cloneDeep(this.svgSizeBackupPosition.size);

        // 纵向
        if (directionByScale === "vertical") {
          this.svgSizePosition.h = (
            svgSizePosition.h * svgVerticalScale
          ).toFixed();
        } else {
          this.svgSizePosition.w = (
            svgSizePosition.w * svgVerticalScale
          ).toFixed();
        }

        let linkList = [];
        let nodeList = [];

        this.allNodeLinkList.forEach((ele) => {
          if (ele.linkId) {
            linkList.push(ele);
          } else {
            nodeList.push(ele);
          }
        });
        const scaleSvg = (direction) => {
          if (direction === "vertical") {
            return scaleSvgY(linkList, nodeList, svgVerticalScale);
          } else {
            return scaleSvgX(linkList, nodeList, svgVerticalScale);
          }
        };

        this.allNodeLinkList = scaleSvg(directionByScale);
        this.setMapInfo({
          ...this.svgInfo,
          mapSize: `${this.svgSizePosition.w}*${this.svgSizePosition.h}`,
        });
        this.$bus.emit("onAllNodeLinkList", this.allNodeLinkList);
        this.getSvgSizePosition(this.svgSizePosition);
      }
    },
    updateSvg() {
      const { w, h } = this.svgSizePosition;
      const params = {
        ...this.svgInfo,
        mapSize: `${w}*${h}`,
      };
      this.$post(`/topoEdit/updateMap`, params);
    },
    scaleNodeComponents() {
      nodeList.forEach((ele) => {
        if (ele.nodeId) {
          if (ele.nodeType === "text") {
            const { rotate, w } = ele;
            // 求当前文字旋转后的线段
            let startPoint = { x: ele.x, y: ele.y };
            let endPoint = { x: 0, y: 0 };
            const radian = (rotate * Math.PI) / 180;
            endPoint.x = startPoint.x + w * Math.cos(radian);
            endPoint.y = startPoint.y + w * Math.sin(radian);

            startPoint.y = startPoint.y * 2;
            endPoint.y = endPoint.y * 2;

            const radianByScale = Math.atan2(
              endPoint.y - startPoint.y,
              endPoint.x - startPoint.x
            );

            ele.rotate = radianByScale * (180 / Math.PI);
            ele.y = startPoint.y;
          } else {
            const { nodeId, y } = ele;
            ele.y = ele.y * 2;

            linkList.forEach((link) => {
              if (link.fromObj === nodeId) {
                const len = link.pathPoints.length;

                if (!link.endObj) {
                  link.pathPoints.forEach((point) => {
                    if (point.isScale) return;

                    point.y += y;
                    point.isScale = true;
                  });
                } else {
                  link.pathPoints.forEach((point, index) => {
                    if (point.isScale || index >= (len - 1) / 2) return;

                    if (
                      link.pathPoints[index + 1] &&
                      ~~point.y === ~~link.pathPoints[index + 1].y
                    ) {
                      point.y += y;
                      link.pathPoints[index + 1].y += y;
                      point.isScale = true;
                      link.pathPoints[index + 1].isScale = true;
                      return;
                    }

                    point.y += y;
                    point.isScale = true;
                  });
                }
              }

              if (link.endObj === nodeId) {
                const len = link.pathPoints.length;

                if (!link.fromObj) {
                  link.pathPoints.forEach((point) => {
                    if (point.isScale) return;
                    point.y += y;
                    point.isScale = true;
                  });
                } else {
                  link.pathPoints.forEach((point, index) => {
                    if (
                      len === 3 &&
                      ~~link.pathPoints[0].y === ~~link.pathPoints[1].y
                    ) {
                      if (!link.pathPoints[2].isScale) {
                        link.pathPoints[2].y += y;
                        link.pathPoints[2].isScale = true;
                      }
                      return;
                    }

                    if (point.isScale || index < (len - 1) / 2) return;

                    if (
                      link.pathPoints[index + 1] &&
                      ~~point.y === ~~link.pathPoints[index + 1].y
                    ) {
                      point.y += y;
                      link.pathPoints[index + 1].y += y;
                      point.isScale = true;
                      link.pathPoints[index + 1].isScale = true;
                      return;
                    }

                    point.y += y;
                    point.isScale = true;
                  });
                }
              }
            });
          }
          ele.middleRotatePoint.y = ele.y + ele.h / 2;
        } else {
          //   ele.pathPoints = ele.pathPoints.map((elePoint) => {
          //     elePoint.y = elePoint.y * scale;
          //     return elePoint;
          //   });
        }
        // if (
        //   ele.nodeId &&
        //   ["500kv-p", "500kv", "电网组件1"].includes(ele.nodeType)
        // ) {
        //   ele.x -= ele.w / 2;
        //   ele.y -= ele.h / 2;
        //   ele.w = ele.w * 2;
        //   ele.h = ele.h * 2;
        //   ele.middleRotatePoint.x = ele.x + ele.w / 2;
        // } else if (ele.linkId) {
        //   if (ele.linkStyles.includes("500")) {
        //     ele.linkWidth = 6;
        //   }
        // }
        // const { w, h, x, y } = ele;
        // if (ele.linkId) {
        //   if (ele.linkStyles.includes("#F40D0D")) {
        //     const styles = JSON.parse(ele.linkStyles);
        //     styles.color = "#a1a2a2";
        //     ele.linkStyles = JSON.stringify(styles);
        //   }
        // }

        // if (ele.linkId) {
        //   const { nodeStyles } = ele;
        //   let res = JSON.parse(nodeStyles);
        //   if (res.rotate) {
        //     const { pathPoints } = ele;
        //     const [centerPoint, ...points] = pathPoints;
        //     const pointsRotate = points.map((ponintRotate) => {
        //       let r = Math.atan2(
        //         ponintRotate.y - centerPoint.y,
        //         ponintRotate.x - centerPoint.x
        //       );
        //       const angle = -res.rotate - (180 / Math.PI) * r;
        //       return rotatePoint(ponintRotate, centerPoint, angle);
        //     });

        //     ele.pathPoints = [centerPoint, ...pointsRotate];
        //   }
        // }
        // if (ele.nodeId && ele.nodeType === "xt6") {
        //   const { nodeStyles } = ele;
        //   let res = JSON.parse(nodeStyles);
        //   res.image = "/ftp/icon/xt6-01.png";
        //   ele.nodeStyles = JSON.stringify(res);
        //   console.log(
        //     "🚀 ~ file: SvgContent.vue ~ line 1734 ~ this.nodeLinkList=this.nodeLinkList.map ~ res",
        //     res
        //   );
        // }
        // if (ele.nodeType === "dwzj3") {
        //   const newW = 100;
        //   const newH = 66;
        //   ele.x = x - (newW - w) / 2;
        //   ele.y = y - (newH - h) / 2;
        //   ele.w = newW;
        //   ele.h = newH;
        //   ele.middleRotatePoint.x = ele.x + ele.w / 2;
        //   ele.middleRotatePoint.y = ele.y + ele.h / 2;
        //   ele.nodePosition = `${ele.x},${ele.y}`;
        // }
        // if (
        //   ele.fontColor === "#fff" ||
        //   ele.fontColor === "#ffffff" ||
        //   ele.fontColor === "rgb(255,255,255)"
        // ) {
        //   ele.fontSize = "15";
        // }
        // if (ele.nodeId && ["220kv-p", "220kv"].includes(ele.nodeType)) {
        //   const { w, h, x, y } = ele;
        //   const newVal = 31;
        //   ele.x = x - (newVal - w) / 2;
        //   ele.y = y - (newVal - h) / 2;
        //   ele.w = newVal;
        //   ele.h = newVal;
        //   ele.middleRotatePoint.x = ele.x + ele.w / 2;
        //   ele.middleRotatePoint.y = ele.y + ele.h / 2;
        //   ele.nodePosition = `${ele.x},${ele.y}`;
        // }
        // else if (ele.nodeType === "500kv-p") {
        //   const newWith = +(w / 1.32).toFixed(2),
        //     newHeight = +(w / 1.32).toFixed(2);
        //   ele.x = x - (newWith - w) / 2;
        //   ele.y = y - (newHeight - h) / 2;
        //   ele.w = newWith;
        //   ele.h = newHeight;
        //   ele.middleRotatePoint.x = ele.x + ele.w / 2;
        //   ele.middleRotatePoint.y = ele.y + ele.h / 2;
        //   ele.nodePosition = `${ele.x},${ele.y}`;
        // }
        //  else if (ele.linkId) {
        //   if (ele.linkWidth === 6) {
        //     ele.linkWidth = 4;
        //   }
        //   if (ele.linkWidth === 10) {
        //     ele.linkWidth = 6;
        //   }
        // }
        return ele;
      });
      this.nodeLinkList = [...linkList, ...nodeList];

      //   this.updateNodeAndLink(this.nodeLinkList);
    },
    // 缩放界面元素后，进行接口调用
    scaleMapComponents() {
      console.log("---");

      // this.compScale = 1.09;
      this.allNodeLinkList = this.allNodeLinkList.map((ele) => {
        if (ele.linkId) {
          // if (ele.linkStyles.includes("500")) {
          //   ele.linkWidth = 4;
          // } else if (ele.linkStyles.includes("220")) {
          //   ele.linkWidth = 2;
          // }
          ele.pathPoints = ele.pathPoints.map((elePoint) => {
            elePoint.x = elePoint.x * this.compScale;
            elePoint.y = elePoint.y * this.compScale;
            return elePoint;
          });
          ele.linkWidth = parseFloat(
            (ele.linkWidth * this.compScale).toFixed(2)
          );
        } else if (ele.nodeId) {
          const { w, h, x, y, textPosition, fontSize, nodeType } = ele;
          ele.w = w * this.compScale;
          ele.h = h * this.compScale;
          ele.x = x * this.compScale;
          ele.y = y * this.compScale;
          // ele.x -= 220;
          // ele.y += 300;
          // if (nodeType === "220kv" || nodeType === "500kv") {
          //   ele.w = 127;
          //   ele.h = 138;
          // } else if (nodeType === "220kv-p" || nodeType === "500kv-p") {
          //   ele.w = 121;
          //   ele.h = 125;
          // }
          // if (nodeType === "text" && fontSize === "45") {
          //   ele.fontSize = "43";
          // }
          // if (fontSize === "34") {
          //   ele.x = ~~(x * this.compScale);
          // } else {
          //   ele.x = ~~(x * this.compScale) + 126;
          // }
          ele.middleRotatePoint.x = ele.x + ele.w / 2;
          ele.middleRotatePoint.y = ele.y + ele.h / 2;
          ele.nodePosition = `${ele.x},${ele.y}`;
          ele.nodeSize = `${ele.w}*${ele.h}`;
          ele.fontSize = (+fontSize || 16) * this.compScale + "";
          // const arr = textPosition.split(",");
          // const [textX, textY] = arr.map((ele) => ~~(+ele * this.compScale));
          // ele.textPosition = `${textX},${arr[1]}`;
        }
        return ele;
      });
      this.updateNodeAndLink(this.allNodeLinkList);
      this.setCompScale(1);
      return;
      this.nodeLinkList = this.nodeLinkList.map((ele) => {
        const { nodeType, nodeId, fontColor } = ele;
        if (nodeId && nodeType === "text") {
          // if (
          //   fontColor === "rgb(0,255,0)" ||
          //   fontColor === "rgb(255,255,255)"
          // ) {
          //   ele.fontSize = '45';
          // }
          // ele.nodeSize = "220*55";
          // ele.w = 220;
          // ele.h = 55;
        }
        return ele;
      });
      // this.updateNodeAndLink(this.nodeLinkList);
    },
    // 初始化鼠标滚轮事件 放大缩小
    initScaleEvent() {
      this.$refs.topoEdit.addEventListener("mousewheel", this.onMouseWheel, {
        passive: false,
      });
    },
    scaleSvgMap(e) {
      let ratio = 1.1;
      // 缩小
      if (e.deltaY > 0) {
        ratio = 1 / 1.1;
      }
      const { w, h, tx, ty } = this.svgSizePosition;

      const maxScale = 5;
      const minScale = 0.01;
      const _scale = this.scale * ratio;
      if (_scale > maxScale) {
        ratio = maxScale / this.scale;
        this.scale = maxScale;
      } else if (_scale < minScale) {
        ratio = minScale / this.scale;
        this.scale = minScale;
      } else {
        this.scale = _scale;
      }
      //  缩放后相对于之前位置（中心点） 偏移的值
      const origin = {
        x: (ratio - 1) * w * 0.5,
        y: (ratio - 1) * h * 0.5,
      };
      // 计算偏移量
      this.svgSizePosition.tx -= (ratio - 1) * (e.clientX - tx) - origin.x;
      //   -60 是去除导航栏的高度
      this.svgSizePosition.ty -= (ratio - 1) * (e.clientY - 60 - ty) - origin.y;
    },
    onMouseWheel(ev) {
      //   let down = true; // 定义一个标志，当滚轮向下滚时，执行一些操作
      //   down = ev.wheelDelta ? ev.wheelDelta < 0 : ev.detail > 0;
      //   if (this.isCtrlKeyDown) {
      //     let scale = this.compScale;
      //     if (down) {
      //       this.dragPointGroup.hide();
      //       this.rotatePointGroup.hide();
      //       if (scale < 0.01 + this.scaleStep) {
      //         scale = 0.01;
      //         return;
      //       } else {
      //         scale = parseFloat(scale) - this.scaleStep;
      //       }
      //     } else {
      //       scale = parseFloat(scale) + this.scaleStep;
      //     }
      //     this.setCompScale(+scale.toFixed(2));
      //   } else {
      this.scaleSvgMap(ev);
      //   }

      if (ev.preventDefault) {
        ev.preventDefault(); // 阻止默认事件
      }
      return false;
    },
    // 方向键移动
    moveArrowKey(key) {
      let tx = 0,
        ty = 0;
      switch (key) {
        case "ArrowUp":
          ty = -1;
          break;
        case "ArrowDown":
          ty = 1;
          break;
        case "ArrowRight":
          tx = 1;
          break;
        case "ArrowLeft":
          tx = -1;
          break;
        default:
          break;
      }
      if (this.isShiftKeyDown) {
        tx = 5 * tx;
        ty = 5 * ty;
      }
      this.nodeLinkList.forEach((ele) => {
        if (ele.selected) {
          ele.tx += tx;
          ele.ty += ty;
          if (ele.type !== "CustomPath") {
            this.updateBindLinkPosition(ele, tx, ty);
          }
        }
      });
    },
    bindEvent() {
      document.addEventListener("click", (e) => {
        this.isMouseRightVisible = false;
      });
      document.addEventListener("keyup", (e) => {
        const { code } = e;
        if (code === "Space") {
          this.isSpaceDown = false;
        }
      });
    },
    getLinkSelectedList() {
      //   const list = this.nodeSelectedList
      //     .filter((ele) => !!ele.linkId)
      //     .map((ele) => ele.linkId);
      return this.nodeLinkList.filter((ele) => !!ele.linkId && ele.selected);
    },
    /**
     * 线段等距
     * @param durection vertical / horizontal
     */
    alignLink(distance) {
      let linkList = this.nodeSelectedList.filter((ele) => !!ele.linkId);
      linkList.forEach((ele) => {
        const { x, y } = ele.pathPoints[0];
        ele.distance = Math.sqrt(x * x + y * y);
      });

      const itemIndex = linkList.findIndex((ele, index) => {
        return ele.linkId && ele.linkId === this.contextmenuSelectedLinkId;
      });

      if (itemIndex === -1) {
        linkList.sort((a, b) => a.distance - b.distance);
      } else {
        // 把右键选择的item放到第一位
        if (itemIndex > 0) {
          const item = linkList.splice(itemIndex, 1)[0];
          linkList.unshift(item);
        }
      }
      let leftCount = 0;
      let rightCount = 0;
      linkList.forEach((link, index) => {
        if (index === 0) return;
        // 不动的线段
        const { pathPoints } = linkList[0];

        function setAllPoint(n) {
          if (pathPoints[n] && pathPoints[n + 1]) {
            const currPoint = link.pathPoints[n];
            const line = [pathPoints[n], pathPoints[n + 1]];
            const len = distanceOfPointAndLine(currPoint, line);
            const isRight = checkPointLeftOrRight(currPoint, line);
            let offset = 0;
            if (isRight) {
              rightCount++;
              offset = distance * rightCount - len;
            } else {
              leftCount++;
              offset = distance * leftCount - len;
            }
            let { angle, tx, ty } = getTransformPosition(line, offset);
            if (
              (angle === -90 && isRight) ||
              (angle === 90 && !isRight) ||
              (angle === 0 && isRight) ||
              (angle === 180 && isRight) ||
              (angle < 0 && angle > -180 && isRight) ||
              (angle > 0 && angle < 90 && isRight) ||
              (angle > 90 && angle < 180 && !isRight)
            ) {
              tx = -tx;
              ty = -ty;
            }
            link.pathPoints = link.pathPoints.map((point) => {
              return {
                x: Math.round(point.x + tx),
                y: Math.round(point.y + ty),
              };
            });
            setAllPoint(++n);
          }
        }

        setAllPoint(0);
      });
      this.updateNodeAndLink(linkList);
    },
    // 平行元素
    paralleLink() {
      let linkList = this.nodeSelectedList.filter((ele) => !!ele.linkId);
      const item =
        linkList.find((ele, index) => {
          return ele.linkId && ele.linkId === this.contextmenuSelectedLinkId;
        }) || linkList[0];
      const [centerPoint, ponintRotate] = item.pathPoints;

      const getAngel = (centerPoint, ponintRotate) => {
        let r = Math.atan2(
          ponintRotate.y - centerPoint.y,
          ponintRotate.x - centerPoint.x
        );
        return (180 / Math.PI) * r;
      };
      let angelBase = getAngel(centerPoint, ponintRotate) + this.rotationOffset;

      this.nodeSelectedList.map((ele, index) => {
        // 如果是基准线，不进行操作
        if (ele.linkId && item.linkId === ele.linkId) return;
        if (ele.nodeId) {
          // 节点
          if (angelBase > 90) {
            ele.rotate = angelBase - 180;
          } else if (angelBase < -90) {
            ele.rotate = 180 + angelBase;
          } else {
            ele.rotate = angelBase;
          }
        } else {
          // 连线
          const angleEle = getAngel(ele.pathPoints[0], ele.pathPoints[1]);
          // 每条线相对base线段要旋转的角度
          let angleDiff = angelBase - angleEle;
          if (Math.abs(angleDiff) >= 170) {
            angleDiff += 180;
          }
          const point = rotatePoint(
            ele.pathPoints[1],
            ele.pathPoints[0],
            angleDiff
          );
          // 修改第一个点
          this.$set(ele.pathPoints, 1, point);
          ele.pathPoints[1] = point;
        }

        return ele;
      });

      this.updateNodeAndLink(this.nodeSelectedList);
    },
    // 选择所有的元素
    selectAllEl() {
      // 编辑色块
      if (this.isDrawBlock) {
        this.nodeSelectedList = _.cloneDeep(this.nodeLinkList).filter((ele) => {
          return ele.linkId && ele.linkPath.includes("Z");
        });
      } else {
        this.nodeSelectedList = _.cloneDeep(this.nodeLinkList).filter((ele) => {
          if (ele.linkId && ele.linkPath.includes("Z")) {
            return false;
          } else {
            return true;
          }
        });
      }
    },
    handleSvgKeydown(e) {
      //   if (this.versionSelected.versionId) return;
      const { ctrlKey, code } = e;
      if (ctrlKey) {
        // 组合键 Ctrl+ 其他按键
        this.isCtrlKeyDown = true;
        switch (code) {
          case "KeyA":
            e.preventDefault();
            this.selectAllEl();
            break;
          case "KeyC":
            e.preventDefault();
            this.handleCloneClick();
            break;
          case "KeyV":
            e.preventDefault();
            this.handlePasteClick(true);
            break;
          case "KeyZ":
            e.preventDefault();
            this.returnPrevStep();
            break;
          case "KeyS":
            e.preventDefault();
            this.saveAllData();
            break;
          default:
            break;
        }
      } else if (code === "Space") {
        e.preventDefault();
        this.isSpaceDown = true;
      } else {
        // 单个按键
        this.listenerKeydownEvent(e);
      }
    },
    listenerKeydownEvent(e) {
      switch (e.key) {
        case "Shift":
          this.isShiftKeyDown = true;
          break;
        case "Delete":
          this.delNodeOrLink();
          break;
        case "ArrowUp":
        case "ArrowDown":
        case "ArrowRight":
        case "ArrowLeft":
          e.preventDefault();
          this.moveArrowKey(e.key);
          break;
        default:
          break;
      }
    },
    listenerKeyupEvent(e) {
      //   if (this.versionSelected.versionId) return;
      switch (e.key) {
        case "Control":
          this.isCtrlKeyDown = false;
          break;
        case "Shift":
          this.isShiftKeyDown = false;
          break;
        case "ArrowUp":
        case "ArrowDown":
        case "ArrowRight":
        case "ArrowLeft":
          // this.nodeLinkList.forEach((ele, index) => {
          //   if (ele.selected) {
          //     index === 0 && this.setDragPointPosition(ele);
          //     this.updateData();
          //   }
          // });
          this.updateData();

          this.setDragPointPosition(this.nodeLinkList[this.activeIndex]);
          break;
        case " ":
          this.isSpaceDown = false;
          break;
        default:
          break;
      }
    },
    // 本地只把数据进行筛选，不重新获取数据
    delNodeLinkByLocal(linkIdList, nodeIdList) {
      const filterFn = (ele) => {
        if (ele.linkId) {
          return !linkIdList.includes(ele.linkId);
        } else if (ele.nodeId) {
          return !nodeIdList.includes(ele.nodeId);
        }
      };
      this.allNodeLinkList = this.allNodeLinkList.filter(filterFn);
      this.nodeLinkList = this.nodeLinkList.filter(filterFn);
    },
    // 删除节点或者连线
    delNodeOrLink(list) {
      if (!list && !this.nodeSelectedList.length) return;
      this.$Modal.confirm({
        title: "警告",
        content: "是否要删除该元素",
        onOk: () => {
          const { nodeList: nodeIdList, linkList: linkIdList } =
            getNodeLinkList(list ? list : this.nodeSelectedList, true);
          const { mapId } = this.svgInfo;
          const linkPromise =
            linkIdList.length &&
            this.$post(`/topoEdit/deleteLink`, {
              linkIdList,
              mapId,
            });

          const nodePromise =
            nodeIdList.length &&
            this.$post(`/topoEdit/deleteNode`, {
              nodeIdList,
              mapId,
            });

          const promiseList = [linkPromise, nodePromise].filter((ele) => !!ele);

          this.setSpinShow(true);

          Promise.all(promiseList)
            .then(([data]) => {
              if (data.code == "0000") {
                this.$Message.success("删除成功");
                this.delNodeLinkByLocal(linkIdList, nodeIdList);
                this.$bus.emit("handleSingleNodeSelected", {});
                this.setNodeLinkSelected({});

                //   this.$bus.emit("updateAllNodeList", {
                //     isAdd: false,
                //     data: [...nodeList, ...linkList],
                //   });
                this.activeIndex = null;
                this.nodeSelectedList = [];
                this.dragPointGroup.hide();
                this.rotatePointGroup.hide();
              }
            })
            .finally(() => {
              this.setSpinShow(false);
            });
        },
        onCancel: () => {},
      });
    },
    /**
     * HTML5拖拽释放
     * 素材库的素材先以图片进行展示
     */
    drop(e) {
      e.preventDefault();
      const baseNode = [
        "text",
        "rect",
        "circle",
        "path",
        "image",
        "jianxiu",
        "ji",
      ];
      const type = e.dataTransfer.getData("type");
      const svg = this.$refs.topoEdit;

      const position = {
        x: e.offsetX / this.viewBoxScale + svg.viewBox.animVal.x,
        y: e.offsetY / this.viewBoxScale + svg.viewBox.animVal.y,
      };
      if (!type) return;
      const params = generateNodeLink(
        type,
        position,
        this.selectedMapInfo.mapId
      );
      this.addNode(params, type, position);
    },

    moveNode() {
      // 原生拖拽
      if (this.activeIndex !== null && this.isDrag && this.isMove) {
        const { x, y } = this.mousePosition;
        let tx = x - this.mouseStartPoint.x;
        let ty = y - this.mouseStartPoint.y;
        let groupIdMoveList = [];
        // 按下shift 只能垂直或者横向移动
        if (this.isShiftKeyDown) {
          if (Math.abs(tx) > Math.abs(ty)) {
            ty = 0;
          } else {
            tx = 0;
          }
        }
        this.nodeLinkList.forEach((ele, index) => {
          if (ele.selected && !ele.locked) {
            this.nodeLinkList[index].tx = tx;
            this.nodeLinkList[index].ty = ty;
            if (!groupIdMoveList.includes(ele.groupId)) {
              groupIdMoveList.push({
                groupId: ele.groupId,
                tx,
                ty,
              });
            }
            if (ele.type !== "CustomPath") {
              this.updateBindLinkPosition(ele, tx, ty);
            }
          }
        });
        this.transformPreStep = {
          tx,
          ty,
        };
        this.moveGroupLayout(groupIdMoveList);
      }
    },
    moveGroupLayout(groupIdMoveList) {
      this.groupPostionList.forEach((ele) => {
        const item = groupIdMoveList.find(
          (eleMove) => eleMove.groupId === ele.groupId
        );
        if (item) {
          ele.tx = item.tx;
          ele.ty = item.ty;
        }
      });
    },
    updateGroupLayout() {
      this.groupPostionList.forEach((ele) => {
        if (ele.tx || ele.ty) {
          ele.position.x += ele.tx;
          ele.position.y += ele.ty;
          ele.tx = 0;
          ele.ty = 0;
        }
      });
    },
    // 移动Node的时，将绑定在node上面的Path同时更新
    // 如果连线也被选择，则不更新  可能会循环
    // node 移动的node
    updateBindLinkPosition(node, tx, ty) {
      if (!this.isConnectionMove) return;
      const transformDiff = {
        tx: tx - this.transformPreStep.tx,
        ty: ty - this.transformPreStep.ty,
      };

      if (this.isSwitchMove) {
        this.connectedElements.forEach((ele) => {
          ele.tx = tx;
          ele.ty = ty;
        });
      } else {
        //  移动关联的线----如果关联的线已经被选中，则不进行线段的单独更新
        this.nodeLinkList.forEach((ele, index) => {
          if (!ele.linkId) return;
          const { pathPoints } = ele;
          if (ele.fromObj && ele.fromObj === node.nodeId && !ele.selected) {
            const points = updatePathPoint(pathPoints[0], transformDiff);
            this.$set(ele.pathPoints, 0, points);
          }
          if (ele.endObj && ele.endObj === node.nodeId && !ele.selected) {
            let len = pathPoints.length - 1;
            const points = updatePathPoint(pathPoints[len], transformDiff);
            this.$set(ele.pathPoints, len, points);
          }
        });
      }

      // }
      // });
      // const centerPoint = {
      //   x: +node.middleRotatePoint.x + tx,
      //   y: +node.middleRotatePoint.y + ty,
      // };
      // this.nodeLinkList.forEach((ele, index) => {
      //   if (!ele.linkId) return;
      //   // if (ele.linkId && ele.selected) return;
      //   if (ele.fromObj === node.nodeId || ele.endObj === node.nodeId) {
      //     const { pathPoints } = this.nodeLinkList[index];
      //     let indexUpdate = 0;
      //     if (ele.fromObj === node.nodeId) {
      //       indexUpdate = 0;
      //       const points = getIntersectionPoint(
      //         pathPoints[1],
      //         centerPoint,
      //         node.w / 2
      //       );
      //       this.$set(this.nodeLinkList[index].pathPoints, indexUpdate, points);
      //     }
      //     if (ele.endObj === node.nodeId) {
      //       let len = pathPoints.length - 1;
      //       const points = getIntersectionPoint(
      //         pathPoints[len - 1],
      //         centerPoint,
      //         node.w / 2
      //       );
      //       this.$set(this.nodeLinkList[index].pathPoints, len, points);
      //     }
      //   }
      // });
    },
    // 旋转
    rotateNode(e) {
      if (this.isRotate) {
        // this.setHistoryList(this.nodeSelectedList);
        const { x, y, w, h } = this.nodeLinkList[this.activeIndex];
        let rotateMidPoint = { x: x + w / 2, y: y + h / 2 };
        let angle = 0,
          sp = this.rotateStartPoint,
          mp = rotateMidPoint;
        let p = { x: e.offsetX, y: e.offsetY };

        let sAngle = Math.atan2(sp.y - mp.y, sp.x - mp.x);
        let pAngle = Math.atan2(p.y - mp.y, p.x - mp.x);

        angle = this.currentRotate + ((pAngle - sAngle) * 180) / Math.PI;

        this.nodeLinkList[this.activeIndex].rotate = Math.round(angle);
        this.updateNodeAndLink([this.nodeLinkList[this.activeIndex]]);
      }
    },
    // 判断是否是单个元素或者单个组合元素
    checkIsSingleNode(list) {
      let obj = {};
      list.forEach((ele) => {
        if (ele.groupId) {
          obj.groupId = ele;
        } else {
          obj[ele.linkId ? ele.linkId : ele.nodeId] = ele;
        }
      });
      return Object.values(obj).length;
    },
    updateRotateNode(ponintRotate, centerPoint, angle) {
      const point = rotatePoint(ponintRotate, centerPoint, angle);
      this.$set(this.nodeLinkList[this.activeIndex].pathPoints, 1, point);
      this.updateNodeAndLink([this.nodeSelectedList[0]]);
    },
    // 右键菜单点击，旋转连线
    rotateLink(angle) {
      const { pathPoints } = this.nodeSelectedList[0];
      const [centerPoint, ponintRotate] = pathPoints;
      let r = Math.atan2(
        ponintRotate.y - centerPoint.y,
        ponintRotate.x - centerPoint.x
      );
      angle = -angle - (180 / Math.PI) * r;
      this.updateRotateNode(ponintRotate, centerPoint, angle);
    },
    /**
     * 右键菜单点击，镜像连线
     * 其实就是旋转到一定角度的连线
     */
    mirrorLink(direction) {
      const { pathPoints } = this.nodeSelectedList[0];
      const [centerPoint, ponintRotate] = pathPoints;
      let r = Math.atan2(
        ponintRotate.y - centerPoint.y,
        ponintRotate.x - centerPoint.x
      );
      let angle = (180 / Math.PI) * r;
      if (direction === "horizontal") {
        if (angle <= 0) {
          angle = -180 - 2 * angle;
        } else {
          angle = 180 - 2 * angle;
        }
      } else {
        if (angle <= 0) {
          angle = -360 - 2 * angle;
        } else {
          angle = 360 - 2 * angle;
        }
      }
      this.updateRotateNode(ponintRotate, centerPoint, angle);
    },
    // 对齐, 排除线段
    alignNodeLink(type) {
      if (!this.nodeSelectedList.length) return;
      this.dragPointGroup.hide();
      this.rotatePointGroup.hide();
      const positionList = this.nodeSelectedList.map((ele) => {
        if (ele.nodeId) {
          const { x, y, w, h, nodeId, groupId } = ele;
          return {
            x,
            y,
            w,
            h,
            nodeId,
            groupId,
          };
        } else {
          const { x, y, w, h } = getEdgePosition(ele, this.groupPostionList);
          return {
            x,
            y,
            w,
            h,
            linkId: ele.linkId,
            groupId: ele.groupId,
          };
        }
      });
      let len = this.checkIsSingleNode(positionList);

      let x,
        y,
        avgDistance,
        totalWidth,
        totalHeight,
        centerPoint = 0;
      let sortList = [];
      switch (type) {
        case 1:
          // 左对齐
          x = len > 1 ? positionList.sort((a, b) => a.x - b.x)[0].x : 0;
          this.nodeLinkList.forEach((ele) => {
            if (ele.selected && !ele.locked) {
              // 如果只有一个，相对布局
              const tx = x - getEdgePosition(ele, this.groupPostionList).x;
              ele.tx = tx;
              this.updateBindLinkPosition(ele, tx, 0);
            }
          });
          break;
        case 2:
          // 右对齐
          if (len > 1) {
            const [a] = positionList.sort((a, b) => b.x - a.x);
            x = a.x + a.w;
          } else {
            x = this.svgSizePosition.w;
          }
          this.nodeLinkList.forEach((ele) => {
            if (ele.selected && !ele.locked) {
              const { x: elX, w: elW } = getEdgePosition(
                ele,
                this.groupPostionList
              );

              const tx = x - elX - elW;
              ele.tx = tx;
              this.updateBindLinkPosition(ele, tx, 0);
            }
          });
          break;
        case 3:
          // 顶对齐
          y = len > 1 ? positionList.sort((a, b) => a.y - b.y)[0].y : 0;
          this.nodeLinkList.forEach((ele) => {
            if (ele.selected && !ele.locked) {
              const ty = y - getEdgePosition(ele, this.groupPostionList).y;
              ele.ty = ty;
              this.updateBindLinkPosition(ele, 0, ty);
            }
          });
          break;
        case 4:
          // 底对齐
          if (len > 1) {
            const [a] = positionList.sort((a, b) => b.y + b.h - (a.y + a.h));
            y = a.y + a.h;
          } else {
            y = this.svgSizePosition.h;
          }
          this.nodeLinkList.forEach((ele) => {
            if (ele.selected && !ele.locked) {
              const { y: elY, h: elH } = getEdgePosition(
                ele,
                this.groupPostionList
              );

              const ty = y - elY - elH;
              ele.ty = ty;
              this.updateBindLinkPosition(ele, 0, ty);
            }
          });
          break;
        case 5:
          // 水平居中
          if (len > 1) {
            sortList = getNodeAndGroupPosition(
              positionList,
              this.groupPostionList
            ).sort((a, b) => a.x - b.x);
            // 拿到最右边的x位置
            const endX = sortList
              .map((ele) => {
                return ele.w + ele.x;
              })
              .sort((a, b) => b - a)[0];
            totalWidth = endX - sortList[0].x;
            centerPoint = sortList[0].x + totalWidth / 2;
          } else {
            centerPoint = this.svgSizePosition.w / 2;
          }
          this.nodeLinkList.forEach((ele) => {
            if (ele.selected && !ele.locked) {
              const { x, w } = getEdgePosition(ele, this.groupPostionList);
              const tx = centerPoint - (x + w / 2);
              ele.tx = tx;
              this.updateBindLinkPosition(ele, tx, 0);
            }
          });
          break;
        case 6:
          // 垂直居中
          if (len > 1) {
            sortList = positionList.sort((a, b) => a.y - b.y);
            // 拿到最下边的y位置
            const endY = sortList
              .map((ele) => {
                return ele.h + ele.y;
              })
              .sort((a, b) => b - a)[0];
            totalHeight = endY - sortList[0].y;
            centerPoint = sortList[0].y + totalHeight / 2;
          } else {
            centerPoint = this.svgSizePosition.h / 2;
          }
          this.nodeLinkList.forEach((ele) => {
            if (ele.selected && !ele.locked) {
              const { y: elY, h: elH } = getEdgePosition(
                ele,
                this.groupPostionList
              );

              const ty = centerPoint - (elY + elH / 2);
              ele.ty = ty;
              this.updateBindLinkPosition(ele, 0, ty);
            }
          });
          break;
        case 7:
          // 水平等间距
          // 组合的节点需要处理，要作为一个节点处理
          if (len <= 2) return;
          sortList = getNodeAndGroupPosition(
            positionList,
            this.groupPostionList
          ).sort((a, b) => a.x - b.x);
          // 所有元素的宽度
          totalWidth = sortList
            .map((ele) => ele.w)
            .reduce((acc, cur) => acc + cur);
          // 计算相同宽度（理想）下，每个的间距，然后要根据每个宽度进行处理
          avgDistance = Math.round(
            (sortList[sortList.length - 1].x +
              sortList[sortList.length - 1].w -
              sortList[0].x -
              totalWidth) /
              (sortList.length - 1)
          );
          for (let i = 0; i < sortList.length; i++) {
            if (i !== 0 && i !== sortList.length - 1) {
              const index = this.nodeLinkList.findIndex(
                (ele) =>
                  (ele.nodeId && ele.nodeId === sortList[i].nodeId) ||
                  (ele.linkId && ele.linkId === sortList[i].linkId)
              );
              let currWidth = sortList[0].x;
              for (let j = 0; j < i; j++) {
                currWidth += sortList[j].w;
              }
              const tx = i * avgDistance + currWidth - sortList[i].x;
              // 如果是group里面的节点，要分别更新
              if (sortList[i].groupId) {
                this.nodeLinkList.forEach((ele, index) => {
                  if (ele.groupId === sortList[i].groupId && !ele.locked) {
                    this.nodeLinkList[index].tx = tx;
                    this.updateBindLinkPosition(
                      this.nodeLinkList[index],
                      tx,
                      0
                    );
                  }
                });
              } else {
                if (!this.nodeLinkList[index].locked) {
                  this.nodeLinkList[index].tx = tx;
                  this.updateBindLinkPosition(this.nodeLinkList[index], tx, 0);
                }
              }
            }
          }
          break;
        case 8:
          // 垂直等间距
          if (len <= 2) return;
          sortList = getNodeAndGroupPosition(
            positionList,
            this.groupPostionList
          ).sort((a, b) => a.y - b.y);
          // 所有元素的宽度
          totalHeight = sortList
            .map((ele) => ele.h)
            .reduce((acc, cur) => acc + cur);
          // 计算相同宽度（理想）下，每个的间距，然后要根据每个宽度进行处理
          avgDistance = Math.round(
            (sortList[sortList.length - 1].y +
              sortList[sortList.length - 1].h -
              sortList[0].y -
              totalHeight) /
              (sortList.length - 1)
          );
          for (let i = 0; i < sortList.length; i++) {
            if (i !== 0 && i !== sortList.length - 1) {
              const index = this.nodeLinkList.findIndex(
                (ele) =>
                  (ele.nodeId && ele.nodeId === sortList[i].nodeId) ||
                  (ele.linkId && ele.linkId === sortList[i].linkId)
              );
              let currWidth = sortList[0].y;
              for (let j = 0; j < i; j++) {
                currWidth += sortList[j].h;
              }
              const ty = i * avgDistance + currWidth - sortList[i].y;
              if (sortList[i].groupId) {
                this.nodeLinkList.forEach((ele, index) => {
                  if (ele.groupId === sortList[i].groupId && !ele.locked) {
                    this.nodeLinkList[index].ty = ty;
                    this.updateBindLinkPosition(
                      this.nodeLinkList[index],
                      0,
                      ty
                    );
                  }
                });
              } else {
                if (!this.nodeLinkList[index].locked) {
                  this.nodeLinkList[index].ty = ty;
                  this.updateBindLinkPosition(this.nodeLinkList[index], 0, ty);
                }
              }
            }
          }
          break;

        default:
          break;
      }
      this.updateData();
      this.updateGroupLayout();
    },
    generatePath({ x, y }) {
      let path = [{ x, y }];
      for (let i = 1; i <= 2; i++) {
        path.push({ x: x + i * 100, y });
      }
      return path;
    },
    // 没有批量插入的接口,循环实现
    async multiAddNode(list) {
      this.setSpinShow(true);
      for (let i = 0; i < list.length; i++) {
        const ele = list[i];
        try {
          this.setSpinContent(`新增第${i + 1}条，共${list.length}条`);
          await this.addNode(ele, ele.type);
        } catch (error) {
          this.setSpinContent(`新增第${i + 1}条出错，正在重试`);
          await this.addNode(ele, ele.type);
        }
      }
      this.setSpinShow(false);
      this.setSpinContent("");
      this.fetchNodeLinkList();
    },
    // 接口调用
    addNode(params, type, position = {}) {
      return new Promise((resolve, reject) => {
        const url = type === "path" ? "insertLink" : "insertNode";
        if (type === "path") {
          if (params.pathPoints) {
            params.linkPath = generatePathD(params, this.isDrawBlock);
          }
        }
        this.$post(`/topoEdit/${url}`, params)
          .then((data) => {
            if (data.code == "0000") {
              const options = {
                ...params,
                type,
                position,
              };
              if (type === "path") {
                options.linkId = data.data;
              } else {
                options.nodeId = data.data;
              }
              resolve();
              this.generateNode(options);
              //   this.$bus.emit("updateAllNodeList", {
              //     isAdd: true,
              //     data: {
              //       metaData: {},
              //       ...options,
              //     },
              //   });
            }
          })
          .catch(() => {
            reject();
          });
      });
    },
    // 接口调用后，加上nodeId或者linkId后绑定位置信息
    generateNode(options) {
      const { nodeType, linkPath, nodePosition, nodeSize, nodeText, linkId } =
        options;
      let newNode = {};
      if (linkId) {
        newNode = {
          type: "CustomPath",
          //   pathPoints: generatePathPoint(linkPath),
          tx: 0,
          ty: 0,
        };
        this.linkDrawPoints = [];
      } else {
        const [x, y] = nodePosition.split(",").map((ele) => +ele);
        const [w, h] = nodeSize.split("*").map((ele) => +ele);
        switch (nodeType) {
          case "rect":
            newNode = {
              type: "CustomRect",
            };
            break;
          case "circle":
            newNode = {
              type: "CustomCircle",
            };
            break;
          case "image":
            newNode = {
              type: "CustomImage",
            };
            break;
          case "text":
            newNode = {
              type: "CustomText",
              nodeText: nodeText || " ",
            };
            break;
          default:
            // 素材库的组件，后续修改为自定义组件
            newNode = {
              type: "CustomImage",
            };
            break;
        }
        newNode = {
          ...newNode,
          h,
          w,
          x,
          y,
          tx: 0,
          ty: 0,
          middleRotatePoint: {
            x: x + w / 2,
            y: y + h / 2,
          },
        };
      }

      newNode = Object.assign({}, options, newNode);
      // 将新插入的Path放在最底层，方便拖拽吸附
      // if (linkId) {
      //   const linkStyles = JSON.parse(newNode.linkStyles);
      //   if (linkStyles.isBlock) {
      //     this.nodeLinkList.unshift(newNode);
      //   } else {

      //   }
      // } else {
      // this.nodeLinkList.push(newNode);
      this.allNodeLinkList.push(newNode);
      this.filterNodeLinkList();
      // }
      //   this.selectPasteNode(newNode);
    },
    selectPasteNode(node) {
      if (this.isSpaceDown) return;
      // 选中粘贴的元素
      if (node.isPaste) {
        this.nodeSelectedList.push(node);
        this.activeIndex = this.nodeLinkList.length - 1;
        if (this.cloneNodeList.length === 1) {
          this.$bus.emit("handleSingleNodeSelected", node);
          this.setNodeLinkSelected(node);
        } else {
          this.$bus.emit("handleSingleNodeSelected", {});
          this.setNodeLinkSelected({});
        }
      }
    },
    // 重新设置node和link层级
    resetNodeList() {
      let linkList = [];
      let nodeList = [];

      this.nodeLinkList.forEach((ele) => {
        if (ele.nodeId) {
          nodeList.push(ele);
        } else {
          const linkStyle = JSON.parse(ele.linkStyles);
          if (linkStyle.isBlock) {
            linkList.unshift(ele);
          } else {
            linkList.push(ele);
          }
        }
      });
      this.nodeLinkList = [...linkList, ...nodeList];
    },
    // 旋转的点
    initRotatePotint() {
      this.rotatePoint = this.rotatePointGroup
        .findOne("circle")
        .mousedown(({ offsetX, offsetY }) => {
          this.isRotate = true;
          this.rotateStartPoint = { x: offsetX, y: offsetY };
        });
    },
    // 拖拽的点
    // TODO 改成Vue的写法
    initDragPotint() {
      let offsetWidth = 0;
      let offsetHeight = 0;
      let baseIndex = 0;
      let _transformRes;
      let _oppositePoint = {};
      let _currentNode = {};

      this.dragPointGroup.find("circle").each((node) => {
        node
          .draggable()
          .on("beforedrag", (e) => {
            const { offsetX, offsetY } = e.detail.event;

            _currentNode = _.cloneDeep(this.nodeLinkList[this.activeIndex]);
            _transformRes = transformRotate(_currentNode);
            const { point } = _transformRes;
            const { oppositePoint, oppositeIndex } = getPointAndOpposite(
              point,
              offsetX,
              offsetY
            );
            _oppositePoint = oppositePoint;
            offsetWidth = Math.abs(offsetX - oppositePoint.x);
            offsetHeight = Math.abs(offsetY - oppositePoint.y);
            baseIndex = oppositeIndex;
          })
          .on("dragmove", (e) => {
            e.preventDefault();
            const pointX = e.detail.event.offsetX;
            const pointY = e.detail.event.offsetY;

            let scale = {
              x: 1,
              y: 1,
            };
            let realScale = 1;

            // 判断是根据x方向的偏移量来计算缩放比还是y方向的来计算
            if (offsetWidth > offsetHeight) {
              realScale = Math.abs(pointX - _oppositePoint.x) / offsetWidth;
            } else {
              realScale = Math.abs(pointY - _oppositePoint.y) / offsetHeight;
            }

            if ([0, 2, 4, 6].indexOf(baseIndex) >= 0) {
              scale.x = scale.y = realScale;
            } else if ([1, 5].indexOf(baseIndex) >= 0) {
              scale.y = realScale;
            } else if ([3, 7].indexOf(baseIndex) >= 0) {
              scale.x = realScale;
            }

            const { width, height, x, y } = getNewDraw(
              _currentNode,
              scale,
              _transformRes,
              baseIndex
            );

            this.nodeLinkList[this.activeIndex].w = width;
            this.nodeLinkList[this.activeIndex].h = height;
            this.nodeLinkList[this.activeIndex].x = x;
            this.nodeLinkList[this.activeIndex].y = y;
            this.nodeLinkList[this.activeIndex].nodeSize = `${width}*${height}`;

            this.nodeLinkList[this.activeIndex].middleRotatePoint.x =
              x + width / 2;
            this.nodeLinkList[this.activeIndex].middleRotatePoint.y =
              y + height / 2;
            this.updateNodeAndLink([this.nodeLinkList[this.activeIndex]]);
            this.setDragPointPosition(this.nodeLinkList[this.activeIndex]);
          });
      });
    },
    // 设置拖拽点的位置
    setDragPointPosition(box) {
      if (!box) return;
      const { x, y, w, h } = box;
      this.rotatePointGroup.each(function (index) {
        let positionX = x + w / 2;
        let positionY = y - 20;
        if (index === 1) {
          positionX -= 4;
        }

        this.move(positionX, positionY);
      });
      this.dragPointGroup.each(function (index) {
        let positionX = x - 4;
        let positionY = y - 4;
        switch (index) {
          case 0:
            break;
          case 1:
            positionX = positionX + w / 2;
            break;
          case 2:
            positionX = positionX + w;
            break;
          case 3:
            positionX = positionX + w;
            positionY = positionY + h / 2;
            break;
          case 4:
            positionX = positionX + w;
            positionY = positionY + h;
            break;
          case 5:
            positionX = positionX + w / 2;
            positionY = positionY + h;
            break;
          case 6:
            positionY = positionY + h;
            break;
          case 7:
            positionY = positionY + h / 2;
            break;
          default:
            break;
        }
        this.move(positionX, positionY);
      });
    },
  },
};
</script>
<style lang="scss">
@import "../../assets/scss/svg-editor.scss";
</style>
