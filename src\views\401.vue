<template>
  <div class="not-found">
    <div class="warning">未授权访问</div>
    <div>
      <router-link class="go-home" to="/" replace>返回首页</router-link>
      <a class="go-home" @click="logout">注销</a>
    </div>
  </div>
</template>

<script>
import Cookies from "js-cookie";

export default {
  name: "NotFound",
  methods: {
    logout() {
      this.$post("/logout").then((res) => {
        if (res.code === "0000") {
          this.$Message.success("注销成功");
          Cookies.remove("token");
          Cookies.remove("displayName");
          window.localStorage.removeItem("routes");
          this.$router.replace("/login");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.not-found {
  text-align: center;
  padding-top: 40vh;
}
.go-home {
  color: #fff;
  font-size: 20px;
  font-family: sans-serif;
  font-weight: bold;
  position: relative;
  padding: 0.6em 0.4em;
  text-decoration: none;
  &:hover {
    color: #70c0e8;
  }
}
.warning {
  color: whitesmoke;
  font-size: 80px;
  font-family: sans-serif;
  font-weight: bold;
  position: relative;
}
</style>
