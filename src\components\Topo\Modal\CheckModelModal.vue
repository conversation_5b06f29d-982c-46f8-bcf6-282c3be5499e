<template>
  <Modal v-model="isVisible" title="模型检查" border draggable :mask="false">
    <ul v-if="nodeLinkList.length" class="model-check-list">
      <li
        v-for="(item, index) in nodeLinkList"
        :key="item.nodeId || item.linkId"
        :class="[
          {
            'model-check-item-selected':
              modelCheckSelected === (item.nodeId || item.linkId),
          },
          'model-check-item',
        ]"
        @click="handleModelCheckClick(item)"
      >
        <div>
          <span style="display: inline-block; width: 38px"
            >{{ index + 1 }} &nbsp;</span
          >
          <span> {{ item.metaData.name || item.nodeId || item.linkId }}</span>
        </div>
        <Icon type="md-alert" size="20" @click.stop="showModelDetail(item)" />
      </li>
    </ul>
    <span v-show="!nodeLinkList.length" class="ml-5">暂无数据</span>
    <Modal v-model="isMetaDataVisible" title="MetaData详情">
      <div class="model-check-detail-list">
        <div
          v-for="(value, key) in modelDetail"
          :key="key"
          class="model-check-detail-item"
        >
          <span class="mr-5">{{ key }}</span>
          <span>{{ value }}</span>
        </div>
      </div>
      <div slot="footer">
        <Button type="text" @click="isMetaDataVisible = false">取消</Button>
      </div>
    </Modal>
    <div slot="footer">
      <Button type="text" @click="closeModal">取消</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState } from "vuex";
import { post } from "@/utils/http";
import { getHttpType } from "@/store/assistant/";

export default {
  name: "MetaDataEditModal",
  props: {},
  data() {
    return {
      isVisible: false,
      nodeLinkList: [],
      modelCheckSelected: "",
      isMetaDataVisible: false,
      modelDetail: {},
    };
  },
  computed: {
    ...mapState("topoStore", {
      allNodeLinkList: "allNodeLinkList",
    }),
  },
  methods: {
    show(mapId, type) {
      this.isVisible = true;
      this.fetchData(mapId, type);
    },
    fetchData(mapId, type) {
      const url =
        type === "model"
          ? "/data/checkModelByMapId"
          : "/data/checkNodeLeakByMapId";

      post(url, {
        mapId,
        type: getHttpType(),
      }).then((data) => {
        if (data.code === "0000") {
          const list = Array.isArray(data.data)
            ? data.data
            : [...data.data.nodes, ...data.data.links];
          this.nodeLinkList = this.getModelCheckMetaData(list);
          this.$bus.emit("onModelCheck", this.nodeLinkList);
        }
      });
    },
    getModelCheckMetaData(list) {
      const res = [];
      this.allNodeLinkList.forEach((item) => {
        const data = list.some((ele) => (item.nodeId || item.linkId) === ele);
        if (data) {
          res.push(item);
        }
      });
      return res;
    },
    handleModelCheckClick(val) {
      const id = val.nodeId || val.linkId;
      this.modelCheckSelected = this.modelCheckSelected === id ? "" : id;
      this.$bus.emit("handleSingleNodeSelected", val);
    },
    showModelDetail(val) {
      this.isMetaDataVisible = true;
      this.modelDetail = val.metaData || {};
    },
    closeModal() {
      this.isVisible = false;
      this.modelCheckSelected = "";
      this.modelDetail = {};
      this.nodeLinkList = [];
      this.$bus.emit("handleSingleNodeSelected", {});
    },
  },
};
</script>

<style lang="scss" scoped>
.ivu-form-item {
  margin-bottom: 5px;
}
.model-check-list {
  height: 350px;
  overflow: auto;
}
.model-check-title {
  display: block;
  width: fit-content;
  padding: 3px 5px;
  margin-bottom: 5px;
  background-color: #70c0e8;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  font-size: 12px;
}
.model-check-item {
  padding: 5px 10px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:hover {
    background-color: #95a1ad73;
  }
}
.model-check-item-selected {
  background-color: #95a1ad;
  &:hover {
    background-color: #95a1ad;
  }
}
.model-check-detail-list {
  max-height: 300px;
  overflow-y: auto;
}
.model-check-detail-item {
  width: 95%;
  display: flex;
  justify-content: space-between;
  padding: 5px;
  span {
    flex: 1;
    word-break: break-all;
  }
}
</style>
