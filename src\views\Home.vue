<template>
  <div class="home-container">
    <!-- <Header>
        <HeaderBar></HeaderBar>
      </Header> -->
    <SiderView :menuList="menuList" />
    <router-view />
  </div>
</template>

<script>
import { mapState } from "vuex";
import HeaderBar from "@/components/HeaderBar";
import SiderView from "@/components/SiderView";
import { menuOptions } from "@/utils/menuOptions";

export default {
  name: "Home",
  components: {
    HeaderBar,
    SiderView,
  },
  data() {
    return {
      routePaths: [],
    };
  },
  computed: {
    ...mapState("topoStore", {
      menuList: "menuList",
    }),
  },
  mounted() {
    document.onclick = null;
    document.onkeydown = null;
    if (window.timer) {
      clearInterval(window.timer);
    }
    this.initEvent();
  },
  created() {
    this.$bus.on("refresh", () => {
      window.location.reload(true);
    });
  },
  beforeUnmount() {
    this.$bus.off("refresh");
  },
  methods: {
    initEvent() {
      const windows = document.getElementsByClassName("floating-window");

      // 给每个悬浮窗添加鼠标悬停事件监听器
      for (let i = 0; i < windows.length; i++) {
        windows[i].addEventListener("mouseover", function () {
          // 将当前悬浮窗置顶
          this.style.zIndex = 999;
        });

        windows[i].addEventListener("mouseout", function () {
          // 恢复悬浮窗的初始层级
          this.style.zIndex = 998;
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.home-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: $baseBgColor;
  overflow: hidden;
}
</style>
