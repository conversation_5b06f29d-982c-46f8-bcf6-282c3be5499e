.nari_current_container {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    /* will-change: transform; */
}

#naritopo-btnCon {
    position: absolute;
    display: flex;
    width: 425px;
    height: 3150px;
    left: 9800px;
    top: 0px;
    flex-direction: column;
    justify-content: space-around;
}

#naritopo-loading {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.2);
}

.pulse-container {
    width: 200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pulse-bubble {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #3ff9dc;
}

.pulse-bubble-1 {
    animation: pulse 0.4s ease 0s infinite alternate;
}

.pulse-bubble-2 {
    animation: pulse 0.4s ease 0.2s infinite alternate;
}

.pulse-bubble-3 {
    animation: pulse 0.4s ease 0.4s infinite alternate;
}

#naritopo-btnCon .active {
    background-color: #0e42d0;
    color: #fff;
}

.naritopo-btn {
    height: 425px;
    border: 1px solid #fff;
    color: rgb(255, 255, 0);
    font-size: 100px;
    user-select: none;
    border-radius: 5%;
    border: 2px solid #3ff9dc;
}

.bubble {
    stroke-width: 2px;
    stroke: #fff;
    fill: none;
    filter: url(#bubble-out);
    animation: bubbleMove 5s ease-in infinite;
}

@keyframes bubbleMove {
    from {
        transform: translate(0, 0);
    }

    to {
        transform: translate(0, -100px);
    }
}

@keyframes halfArrowMove {
    100% {
        offset-distance: 50%;
    }
}

@keyframes arrowMove {
    100% {
        offset-distance: 100%;
    }
}

@keyframes pulse {
    from {
        opacity: 1;
        transform: scale(1);
    }

    to {
        opacity: 0.25;
        transform: scale(0.75);
    }
}

.topo-centerText {
    fill: #fff;
    font-size: 87px;
    font-family: digital-7_mono;
    transform: translate(199px, 148.8px);
    user-select: none;
    text-anchor: end;
}

.area-board-name {
    fill: url(#area_board_name);
    filter: url(#area_board_name_shadow);
    font-size: 69px;
    font-family: 方正正大黑简体;
    dominant-baseline: central;
    text-anchor: middle;
    transform: translate(86px, 55px);
    letter-spacing: 5px;
}

.area-board-value {
    fill: url(#area_board_value);
    font-size: 90px;
    font-weight: bold;
    font-family: 微软雅黑;
    dominant-baseline: central;
    text-anchor: end;
    letter-spacing: 1px;
}

.topo-flash {
    animation: flash 0.8s ease-in-out infinite;
}

.topo-flash-node {
    filter: sepia(1);
    animation: flash 0.8s ease-in-out infinite;
}

@keyframes flash {
    from {
        opacity: 1;
    }

    to {
        opacity: 0.25;
    }
}

foreignObject div {
    position: relative;
    overflow: auto;
}

foreignObject div::-webkit-scrollbar {
    position: fixed;
    width: 0.5rem;
}

foreignObject div::-webkit-scrollbar-thumb {
    background: #ccc;
}