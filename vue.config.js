const { defineConfig } = require("@vue/cli-service");
module.exports = defineConfig({
  transpileDependencies: true,
  //基本配置
  publicPath: "./", //基本路径
  outputDir: "ds", //输出文件目录
  //   outputDir: "v2", //输出文件目录
  productionSourceMap: false,
  css: {
    loaderOptions: {
      scss: {
        additionalData: `@import "~@/assets/scss/variables.scss";`,
      },
      less: {
        lessOptions: {
          javascriptEnabled: true,
        },
      },
    },
  },
  devServer: {
    host: "0.0.0.0",
    // 代理转发配置，用于调试环境
    proxy: {
      "/ds-action": {
        //请求接口
        // target: "http://**************:8899",
        // target: "http://**************:6818",
        // target: "http://**************:6818/action",
        target: "http://**************:8899",
        //  target: 'http://**************:8080',
        //刘俊
        // target: 'http://*************:8899',

        //本地创建虚拟服务端
        changeOrigin: true,
        //允许websocket跨域
        ws: true,
        //路径重写，以target中的地址替换/action，项目中直接使用/action即可
        pathRewrite: {
          "^/ds-action": "",
        },
      },
      "/img": {
        //请求接口
        // target: 'http://**************:8899',
        target: "http://**************:6818/",

        //本地创建虚拟服务端
        changeOrigin: true,
        //允许websocket跨域
        ws: true,
        //路径重写，以target中的地址替换/action，项目中直接使用/action即可
        pathRewrite: {
          "^/img": "",
        },
      },
      "/ftp": {
        //请求接口
        target: "http://**************:6818",
        //本地创建虚拟服务端
        changeOrigin: true,
        //允许websocket跨域
        ws: true,
        //路径重写，以target中的地址替换/action，项目中直接使用/action即可
        pathRewrite: {
          "^/ftp": "/ftp",
        },
      },
    },
  },
});
