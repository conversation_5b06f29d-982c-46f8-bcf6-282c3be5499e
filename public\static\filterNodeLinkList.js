function sortListByTier(list) {
  let linkList = [];
  let nodeList = [];
  let textList = [];

  list.forEach((ele) => {
    if (ele.nodeId) {
      if (ele.nodeType === "text") {
        textList.push(ele);
      } else {
        nodeList.push(ele);
      }
    } else {
      const linkStyle = JSON.parse(ele.linkStyles);
      if (linkStyle.isBlock) {
        linkList.unshift(ele);
      } else {
        linkList.push(ele);
      }
    }
  });
  return [...linkList, ...nodeList, ...textList];
}

function filterNodeLinkList(data) {
  const { sublayerSelectedList, allNodeLinkList } = data;

  const nodeLinkSelectedSet = new Set();
  let list = [];

  sublayerSelectedList.forEach((ele) => {
    let len = allNodeLinkList.length;
    let listTemp = [];
    for (let i = 0; i < len; i++) {
      const item = allNodeLinkList[i];
      const id = item.nodeId || item.linkId;
      const isInclude = nodeLinkSelectedSet.has(id);

      if (!isInclude) {
        if (item.sublayerList && item.sublayerList.length) {
          // 获取单个元素绑定的子图层列表
          const sublayerList = item.sublayerList.map(
            (eleChild) => eleChild.sublayerId
          );
          if (sublayerList.includes(ele)) {
            nodeLinkSelectedSet.add(id);
            listTemp.push(item);
          }
        } else if (ele === "other") {
          nodeLinkSelectedSet.add(id);
          listTemp.push(item);
        }
      }
    }
    list.unshift(...sortListByTier(listTemp));
  });

  return list;
  //   const { id, type, nodeLinkList, allNodeLinkList, sublayerSelectedList } =
  //     data;
  //   if (type === "add") {
  //     const list = [];
  //     allNodeLinkList.forEach((ele) => {
  //       // 如果已经显示，不进行后续操作
  //       if (ele.isShow) return;
  //       if (ele.sublayerList && ele.sublayerList.length) {
  //         const isSelected = ele.sublayerList.some(
  //           (eleChild) => eleChild.sublayerId === id
  //         );
  //         if (isSelected) {
  //           ele.isShow = true;
  //           list.push(ele);
  //         }
  //       } else if (id === "other") {
  //         ele.isShow = true;
  //         list.push(item);
  //       }
  //     });
  //     nodeLinkList.push(...sortListByTier(list));
  //   } else if (type === "subtract") {
  //     // 取消勾选某个图层
  //     this.nodeLinkList = this.nodeLinkList.filter((ele) => {
  //       if (ele.sublayerList && ele.sublayerList.length) {
  //         const isSelected = ele.sublayerList.some((eleChild) => {
  //           eleChild.sublayerId === id;
  //         });
  //         // 把已经选择的删除
  //         if (isSelected) {
  //           ele.isShow = false;
  //           //   nodeLinkList.splice(index, 1);
  //           return true;
  //         }
  //       } else if (id === "other") {
  //         ele.isShow = false;
  //         // 其他
  //         // nodeLinkList.splice(index, 1);
  //         return true;
  //       } else {
  //         return false;
  //       }
  //     });
  //   }
}

self.onmessage = (e) => {
  const list = filterNodeLinkList(e.data);
  self.postMessage(list);
};
