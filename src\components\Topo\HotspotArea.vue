<template>
  <div v-if="isHotspot" class="heat-layout-mask">
    <div
      class="heat-layout"
      ref="heatLayout"
      :style="{
        width: `${size.w}px`,
        height: `${size.h}px`,
        transform: `translate3d(${hotspotAreaPosition.left}px, ${
          hotspotAreaPosition.top + 60
        }px, 0px)`,
      }"
    >
      <div
        id="heatDragArea"
        class="heat-drag-area"
        :style="{
          transform: `translate(${heatDragAreaRects.x}px, ${heatDragAreaRects.y}px)`,
          width: `${heatDragAreaRects.w}px`,
          height: `${heatDragAreaRects.h}px`,
        }"
        @mousedown="handleAreaMousedown"
        @mousemove="handleAreaMousemove"
      >
        <span
          v-for="(item, index) in dragOptions"
          :key="item"
          :class="[item, 'heat-drag-point']"
          @mousedown.stop="handleDragPointMousedown($event, index)"
        ></span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
export default {
  props: {
    svgSizePosition: {
      type: Object,
      default: () => {},
    },
    scale: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      isDrag: false,
      isScale: false,
      size: { w: 0, h: 0 },
      offset: {
        x: 0,
        y: 0,
      },
      heatDragAreaRects: {
        w: 0,
        h: 0,
        x: 0,
        y: 0,
      },
      currheatDragAreaRects: {
        w: 0,
        h: 0,
      },
      startPosition: {
        x: 0,
        y: 0,
      },
      startPointPosition: {
        x: 0,
        y: 0,
      },
      hotspotAreaPosition: {
        left: 0,
        top: 0,
      },
      activePointIndex: 0,
      dragOptions: ["left-top", "right-top", "right-bottom", "left-bottom"],
    };
  },
  mounted() {
    this.initEventBus();
    document.addEventListener("mouseup", this.handleAreaMouseup);
    document.addEventListener("mousemove", this.handleDragPointMousemove);
  },
  beforeDestroy() {
    this.$bus.off("addHotspot");
    this.$bus.off("hotspotItemClick");
    document.removeEventListener("mouseup", this.handleAreaMouseup);
    document.removeEventListener("mousemove", this.handleDragPointMousemove);
  },
  computed: {
    ...mapState("topoStore", {
      isHotspot: "isHotspot",
      hotspotSize: "hotspotSize",
    }),
  },
  watch: {},
  methods: {
    ...mapMutations("topoStore", ["setHotspotDragInfo"]),
    getSize() {
      const size = { w: 0, h: 0 };

      size.w = this.svgSizePosition.w * this.scale;
      size.h = this.svgSizePosition.h * this.scale;

      if (this.hotspotSize.width && this.hotspotSize.height) {
        const scale = this.hotspotSize.width / this.hotspotSize.height;
        size.h = size.w / scale;
      }
      return size;
    },
    initEventBus() {
      this.$bus.on("addHotspot", () => {
        const size = this.getSize();
        this.heatDragAreaRects.w = size.w * 0.3;
        this.heatDragAreaRects.h = size.h * 0.3;
      });
      this.$bus.on("hotspotItemClick", (val) => {
        const { scale, position } = val;
        const [x, y] = position.split(",").map((ele) => +ele);
        this.setSize();

        const size = this.getSize();

        this.heatDragAreaRects = {
          w: size.w / scale,
          h: size.h / scale,
          x: (-x / scale) * this.scale,
          y: (-y / scale) * this.scale,
        };
      });
    },
    setSize() {
      this.size.w = this.svgSizePosition.w * this.scale;
      this.size.h = this.svgSizePosition.h * this.scale;

      this.$nextTick(() => {
        const { left, top } = document
          .querySelector("#topoEdit")
          .getBoundingClientRect();
        this.hotspotAreaPosition = { left, top: top - 60 };
        this.offset = {
          x: left,
          y: top - 60,
        };
      });
    },
    limitPosition(val, min, max, size) {
      if (val < min) {
        return min;
      }
      if (val > max - size) {
        return max - size;
      }
      return val;
    },
    handleAreaMousedown(e) {
      let scrollX =
        document.querySelector("#topoSvgEditorLayout").scrollLeft || 0;

      let scrollY =
        document.querySelector("#topoSvgEditorLayout").scrollTop || 0;

      console.log("scrollY", scrollY);
      this.startPosition = {
        x: e.offsetX - scrollX,
        y: e.offsetY + 60 - scrollY,
      };
      this.isDrag = true;
    },
    handleAreaMousemove(e) {
      if (this.isDrag) {
        const { clientX, clientY } = e;

        let x = this.limitPosition(
          clientX - this.startPosition.x - this.offset.x,
          0,
          this.size.w,
          this.heatDragAreaRects.w
        );
        let y = this.limitPosition(
          clientY - this.startPosition.y - this.offset.y,
          0,
          this.size.h,
          this.heatDragAreaRects.h
        );
        this.heatDragAreaRects.x = x;
        this.heatDragAreaRects.y = y;
      }
    },
    handleAreaMouseup(e) {
      if (this.isDrag || this.isScale) {
        const { x, y, w } = this.heatDragAreaRects;
        const scale = this.size.w / w;
        const realX = -((x / this.scale) * scale).toFixed(2);
        const realY = -((y / this.scale) * scale).toFixed(2);
        const position = `${realX},${realY}`;
        this.setHotspotDragInfo({
          position,
          scale,
        });
      }
      this.isDrag = false;
      this.isScale = false;
    },
    handleDragPointMousedown(e, index) {
      this.isScale = true;
      this.activePointIndex = index;
      this.currheatDragAreaRects = { ...this.heatDragAreaRects };
      this.startPointPosition = {
        x: e.clientX - this.offset.x,
        y: e.clientY - this.offset.y,
      };
    },
    handleDragPointMousemove(e) {
      if (this.isScale) {
        let { clientX, clientY } = e;
        clientX = this.limitPosition(
          clientX - this.offset.x,
          0,
          this.size.w,
          0
        );
        clientY = this.limitPosition(
          clientY - this.offset.y,
          0,
          this.size.h,
          0
        );
        const size = this.getSize();

        const scale = size.w / size.h;
        const diffX = clientX - this.startPointPosition.x;
        const diffY = diffX / scale;

        const { x, y, w, h } = this.currheatDragAreaRects;
        const limitX = this.size.w;
        const limitY = this.size.h;
        if (clientX > limitX || clientY > limitY) return;
        switch (this.activePointIndex) {
          case 0:
            this.heatDragAreaRects.w = -diffX + w;
            this.heatDragAreaRects.h = -diffY + h;
            this.heatDragAreaRects.x = diffX + x;
            this.heatDragAreaRects.y = diffY + y;
            break;
          case 1:
            this.heatDragAreaRects.w = diffX + w;
            this.heatDragAreaRects.h = diffY + h;
            this.heatDragAreaRects.y = -diffY + y;
            break;
          case 2:
            this.heatDragAreaRects.w = diffX + w;
            this.heatDragAreaRects.h = diffY + h;
            break;
          case 3:
            this.heatDragAreaRects.w = -diffX + w;
            this.heatDragAreaRects.h = -diffY + h;
            this.heatDragAreaRects.x = diffX + x;
            break;
          default:
            break;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.heat-layout-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}
.heat-layout {
  position: absolute;
  top: 0;
  left: 0;
  .heat-drag-area {
    position: absolute;
    width: 200px;
    height: 200px;
    left: 0;
    top: 0;
    border: 2px solid violet;
    background-color: rgba(127, 255, 212, 0.37);
    cursor: move;
  }
  .heat-drag-point {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: red;
  }
  .left-top {
    left: -5px;
    top: -5px;
    cursor: nw-resize;
  }
  .right-top {
    right: -5px;
    top: -5px;
    cursor: ne-resize;
  }
  .left-bottom {
    left: -5px;
    bottom: -5px;
    cursor: sw-resize;
  }
  .right-bottom {
    right: -5px;
    bottom: -5px;
    cursor: se-resize;
  }
}
</style>
