import { get, post } from "../utils/http";
import { message } from "@/utils/resetMessage";
import { getHttpType } from "./assistant/";
import _ from "lodash";

const sceneStore = {
  state: {
    menuData: [], // 目录列表
    mapList: [],
    pointSelectedInfo: {
      type: "",
      index: 0,
      cIndex: 0,
    },
    objectList: [],
    isLinkDraw: false,
    isLinkAlign: false,
    svgBgData: {
      path: "",
      isOpen: false,
    },
    isSelectLinkObj: false,
    isShowImageLine: true,
    isGridBgShow: false,
    is220kvShow: false,
    textColorList: [
      "rgb(255,0,0)",
      "rgb(0,255,0)",
      "rgb(255,255,255)",
      "rgb(51,96,251)",
    ],
    isDrawBlock: false,
    contextmenuSelectedLinkId: "",
    compScale: 1,
    sublayerList: [],
    colorSelected: "#00ff2a",
    isHotspot: false,
    hotspotDragInfo: {},
    hotspotSize: { width: 0, height: 0 },
    isMapFileVisible: false,
    ismapMetaVisible: false,
    sublayerSelectedList: [],
    versionSelected: {
      versionName: "草稿",
    },
    versionList: [],
    modelData: {},
    isModelVisible: false,
    spinShow: false,
    spinContent: "加载中...",
    mapInfo: {
      description: { north: "", south: "" },
    },
    extraMetaData: {},
    isPlanDividerShow: true,
    isSwitchMove: false,
    isConnectionMove: true,
    textHideBySublayerList: [],
    isHideTextBySublayer: true,
    allNodeLinkList: [],
    nodeLinkBysyblayerList: [],
    nodeLinkSelected: {},
    menuList: [],
    viewBoxScale: 1,
    mapType: "province",
    menuMapList: [],
    rotationOffset: 0,
  },
  mutations: {
    setMenuData(state, data) {
      state.menuData = data;
    },
    setMapList(state, data) {
      state.mapList = data;
    },
    setPointSelectedInfo(state, data) {
      state.pointSelectedInfo = data;
    },
    setPointObjectList(state, data) {
      state.objectList = data;
    },
    setPointIsLinkDraw(state, data) {
      state.isLinkDraw = data;
    },
    setSvgBgData(state, data) {
      state.svgBgData = data;
    },
    setShowImageLine(state, data) {
      state.isShowImageLine = data;
    },
    setGridBgShow(state, data) {
      state.isGridBgShow = data;
    },
    set220kvShow(state, data) {
      state.is220kvShow = data;
    },
    setTextColorList(state, data) {
      state.textColorList = data;
    },
    setLinkAlign(state, data) {
      state.isLinkAlign = data;
    },
    setSelectLinkObj(state, data) {
      state.isSelectLinkObj = data;
    },
    setDrawBlock(state, data) {
      state.isDrawBlock = data;
    },
    setContextmenuSelectedLinkId(state, data) {
      state.contextmenuSelectedLinkId = data;
    },
    setCompScale(state, data) {
      state.compScale = data;
    },
    setSublayerList(state, data) {
      state.sublayerList = data;
    },
    setColorSelected(state, data) {
      state.colorSelected = data;
    },
    setHotspot(state, data) {
      state.isHotspot = data;
    },
    setHotspotDragInfo(state, data) {
      state.hotspotDragInfo = data;
    },
    setHotspotSize(state, data) {
      state.hotspotSize = data;
    },
    setMapFileVisible(state, data) {
      state.isMapFileVisible = data;
    },
    setmapMetaVisible(state, data) {
      state.ismapMetaVisible = data;
    },
    setSublayerSelectedList(state, data) {
      state.sublayerSelectedList = data;
    },
    setVersionSelected(state, data) {
      state.versionSelected = data || {
        versionName: "草稿",
      };
    },
    setVersionList(state, data) {
      state.versionList = data;
    },
    setModelData(state, data) {
      state.modelData = data;
    },
    setModelVisible(state, data) {
      state.isModelVisible = data;
    },
    setSpinShow(state, data) {
      state.spinShow = data;
    },
    setSpinContent(state, data) {
      state.spinContent = data || "加载中";
    },
    setMapInfo(state, data) {
      state.mapInfo = data;
    },
    setExtraMetaData(state, data) {
      state.extraMetaData = data;
    },
    setPlanDividerShow(state, data) {
      state.isPlanDividerShow = data;
    },
    setSwitchMove(state, data) {
      state.isSwitchMove = data;
    },
    setConnectionMove(state, data) {
      state.isConnectionMove = data;
    },
    setMenuList(state, data) {
      state.menuList = data;
    },
    setMenuMapList(state, data) {
      state.menuMapList = _.cloneDeep(data);
    },
    setViewBoxScale(state, data) {
      state.viewBoxScale = data;
    },
    setMapType(state, data) {
      state.mapType = data;
    },
    setHideTextBySublayer(state, data) {
      state.isHideTextBySublayer = data;
    },
    setTextHideBySublayerList(state, data) {
      state.textHideBySublayerList = data;
    },
    setNodeLinkBysyblayerList(state, data) {
      state.nodeLinkBysyblayerList = data;
    },
    setNodeLinkSelected(state, data) {
      state.nodeLinkSelected = data;
    },
    setAllNodeLinkList(state, data) {
      state.allNodeLinkList = data;
    },
    setRotationOffset(state, data) {
      state.rotationOffset = data;
    },
  },
  getters: {
    listBySelected: (state) => {
      let list = state.allList.filter((item) => item.selected);
      return list;
    },
  },
  actions: {
    checkTopoRelationshipByMapId({ commit }, data) {
      return new Promise((resolve, reject) => {
        post(`/data/checkTopoRelationshipByMapId`, {
          mapId: data,
          type: getHttpType(),
        }).then((data) => {
          if (data.code === "0000") {
            resolve(data.data);
          }
        });
      });
    },
    getModelList({ commit }, data) {
      return new Promise((resolve, reject) => {
        get("/data/getModelList", {
          name: data,
          type: getHttpType(),
        }).then(({ data }) => {
          if (data.code === "0000") {
            commit("setModelData", data.data);
            resolve();
          }
        });
      });
    },
    getVersionList({ commit }, data) {
      return new Promise((resolve, reject) => {
        get(`/topoEdit/getVersionListByMapId`, {
          mapId: data,
        }).then(({ data }) => {
          if (data.code == "0000") {
            commit("setVersionList", data.data);
            resolve();
          }
        });
      });
    },
    getMapInfo({ state, commit }, data) {
      return new Promise((resolve, reject) => {
        get(`/topoEdit/getMapById`, {
          mapId: data,
          versionId: state.versionSelected.versionId,
        }).then(({ data }) => {
          if (data.code == "0000") {
            commit("setMapInfo", data.data);
            resolve(data.data);
          }
        });
      });
    },
    getExtraMetaData({ state, commit }, data) {
      return new Promise((resolve, reject) => {
        get("/data/getDataByMapId", {
          mapId: data,
        }).then(({ data }) => {
          commit("setExtraMetaData", data.data);
        });
      });
    },
    updateMetaDataByMapIdAndObjId({ state }, data) {
      const { metaData, nodeId, linkId } = data.nodeLink;
      const mapIds = data.mapIds;
      return new Promise((resolve, reject) => {
        post("/topoEdit/updateMetaDataByMapIdAndObjId", {
          nodeId,
          linkId,
          mapId: mapIds,
          metaData, //元数据
        }).then((data) => {
          if (data.code === "0000") {
            message.success("批量更新成功");
            resolve();
          }
        });
      });
    },
    updateMetaDataByMapIdsAndObjIds({ state }, data) {
      return new Promise((resolve, reject) => {
        post("/topoEdit/updateMetaDataByMapIdsAndObjIds", data).then((data) => {
          if (data.code === "0000") {
            message.success("批量更新成功");
            resolve();
          }
        });
      });
    },
  },
  namespaced: true,
};

export default sceneStore;
