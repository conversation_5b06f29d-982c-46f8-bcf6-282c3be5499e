<template>
  <Modal
    v-model="isVisible"
    title="打印设置"
    :width="1000"
    :mask-closable="false"
  >
    <div style="margin-bottom: 10px">
      <Select
        style="width: 100px; margin: 0 10px"
        size="small"
        v-model="svgPostion"
        @on-change="svgPostionChange"
      >
        <Option
          v-for="item in svgPostionOptions"
          :value="item.value"
          :key="item.value"
          >{{ item.label }}</Option
        >
      </Select>

      <Select
        style="width: 100px; margin: 0 10px"
        size="small"
        v-model="direction"
        @on-change="changeDirection"
      >
        <Option
          v-for="item in directionOptions"
          :value="item.value"
          :key="item.value"
          >{{ item.label }}</Option
        >
      </Select>
      <Checkbox v-model="isIncludeBlock" @on-change="handleBlockChange"
        >是否包含色块</Checkbox
      >
      <Checkbox v-model="isIncludeBg" @on-change="handleBgChange"
        >是否包含背景</Checkbox
      >
    </div>
    <!-- <div>
      <span>子图层选择:</span>
      <CheckboxGroup
        :value="sublayerSelectedList"
        @on-change="handleSublayerSelect"
      >
        <Checkbox label="other">其他</Checkbox>
        <Checkbox
          v-for="item in sublayerList"
          :key="item.sublayerId"
          :label="item.sublayerId"
          >{{ item.sublayerName }}</Checkbox
        >
      </CheckboxGroup>
    </div> -->
    <div id="printContent">
      <!-- <img id="printImg" alt="" /> -->
    </div>

    <div slot="footer">
      <Button type="primary" v-print="'#printImg'">打印</Button>
      <!-- <Button type="primary" @click="generateImage">生成预览</Button> -->
      <Button type="primary" @click="previewImage">全屏预览</Button>
      <Button type="text" @click="isVisible = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
import print from "vue-print-nb";
import $ from "jquery";
import { drawSvgPrintData, clipSvg } from "@/utils/drawSvg";

import { mapState, mapMutations } from "vuex";

export default {
  name: "SvgPrintConfigModal",
  props: {},
  directives: {
    print,
  },
  data() {
    return {
      isVisible: false,
      svgInfo: {},
      svgHtml: null,
      directionOptions: [
        {
          value: "horizontal",
          label: "横向",
        },
        {
          value: "vertical",
          label: "纵向",
        },
      ],
      direction: "horizontal",
      isIncludeBg: false,
      svgSize: {
        width: 0,
        height: 0,
      },
      distance: 40,
      svgPostion: "up",
      svgPostionOptions: [
        {
          value: "all",
          label: "全部",
        },
        {
          value: "up",
          label: "江北",
        },
        {
          value: "down",
          label: "江南",
        },
      ],
      svgVerticalScale: 1,
      isIncludeBlock: false,
    };
  },
  mounted() {
    this.initEvent();
  },
  computed: {
    ...mapState("topoStore", {
      sublayerList: "sublayerList",
      sublayerSelectedList: "sublayerSelectedList",
    }),
  },
  watch: {
    //  Modal 的on-cancel不触发，所以需要监听isVisible的变化
    isVisible(val) {
      !val && this.hide();
    },
  },
  methods: {
    ...mapMutations("topoStore", ["setSublayerSelectedList"]),
    initEvent() {
      this.$bus.on("onMapSlected", (val) => {
        this.svgInfo = val;
      });
    },
    show() {
      this.isVisible = true;

      let [w, h] = this.svgInfo.mapSize.split("*").map((ele) => +ele);

      //   this.svgVerticalScale = 1 - this.distance / h;
      //   this.$bus.emit("onSvgVerticalScaleChange", {
      //     svgVerticalScale: this.svgVerticalScale,
      //     isKeepSvgSize: true,
      //   });
      this.$nextTick(() => {
        this.generateSvg();
        this.handleBgChange(this.isIncludeBg);
      });
    },
    ok() {
      // let newstr = document.getElementById("printContent").innerHTML;
      // let oldstr = document.body.innerHTML;
      // document.body.innerHTML = newstr;
      // window.print();
      // document.body.innerHTML = oldstr;
    },
    hide() {
      this.isVisible = false;
      // 数据量太大，直接赋值，不使用变量
      $("#printImg").attr("src", "");
      $("#printImg").css("display", "none");
      this.direction = "horizontal";
      this.isIncludeBg = false;
      this.isIncludeBlock = false;
      $("#printContent").empty();
      //   this.$bus.emit("onSvgVerticalScaleChange", {
      //     svgVerticalScale: 1,
      //   });
    },
    handleSublayerSelect(val) {
      let list = [];
      const sublayerList = [...this.sublayerList];
      // 按照排序显示
      // listOrder越小的 放在最上面
      sublayerList
        .sort((a, b) => a.listOrder - b.listOrder)
        .forEach((ele) => {
          if (val.includes(ele.sublayerId)) {
            list.push(ele.sublayerId);
          }
        });
      if (val.includes("other")) {
        !list.includes("other") && list.push("other");
      }
      this.setSublayerSelectedList(list);
      this.$bus.emit("sublayerSelect", list);
    },
    convertImgToBase64(url) {
      return new Promise((resolve) => {
        let canvas = document.createElement("canvas"),
          ctx = canvas.getContext("2d"),
          img = new Image();
        img.src = url;
        img.onload = function () {
          canvas.height = img.height;
          canvas.width = img.width;
          ctx.drawImage(img, 0, 0);
          let dataURL = canvas.toDataURL("image/png");
          canvas = null;
          resolve(dataURL);
        };
      });
    },
    generateSvg() {
      const { mapId, mapSize } = this.svgInfo;
      if (!mapId) return this.$Message.warning("未选择图层");
      let svg = $("#topoEdit").clone(true);
      const comp = $(svg).find(".topo-component-list");
      //   let [w, h] = mapSize.split("*").map((ele) => +ele);
      let w = $(svg).width();
      let h = $(svg).height();
      // 浏览器限制canvas大小，只能按比例缩小
      // https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/canvas
      let scale = 1;
      this.svgSize = {
        width: w,
        height: h,
      };
      $(svg).css({ transform: "" });
      $(svg).attr({
        id: "printSvg",
      });
      $(svg)
        .find(".topo-component-list")
        .css({
          transform: `scale(${scale})`,
          "transform-origin": "top left",
          //   "font-family": '"楷体","楷体_GB2312"',
        });
      $(svg).empty().append(comp);
      $(svg).find(".topo-link-arrow").remove();
      $(svg).find(".custom-move-rect").remove();
      $(svg).find(".custom-path-move").remove();

      const texts = $(svg).find("text");
      const _this = this;
      texts.each(function () {
        const color = $(this).css("fill");
        if (color === "rgb(255, 255, 255)" && !_this.isIncludeBg) {
          $(this).css("fill", "rgb(0, 0, 0)");
        } else if (color === "rgb(0, 255, 0)") {
          $(this).remove();
        }
      });

      const blocks = $(svg).find(".topo-line-block");
      blocks.each(function () {
        const fill = $(this).css("fill");
        $(this).css({
          "stroke-dasharray": "10 10",
          fill: "transparent",
        });
        $(this).attr({
          "data-fill": fill,
        });
      });

      $("#printContent").append(svg[0]);
      $("#printContent svg").attr("id", "printImg");

      this.svgPostionChange(this.svgPostion);
    },
    svgPostionChange(val) {
      const { width, height } = this.svgSize;
      let hByScale = height * this.svgVerticalScale;
      const svg = $("#printImg");

      let moveY = 0;

      if (val === "up") {
        hByScale = hByScale * 0.524;
      } else if (val === "down") {
        moveY = hByScale * 0.53;

        hByScale = hByScale * 0.47;
        // 江南的Y起始点计算
      }

      svg.find(".topo-component-list").css({
        transform: `translate(0, -${moveY}px)`,
      });

      drawSvgPrintData(svg[0], width, hByScale, this.distance, this.svgPostion);
      clipSvg("printImg", width, hByScale, val, moveY);

      this.changeDirection(this.direction);
    },
    changeDirection(val) {
      const height = $("#printImg").height();

      $("#printImg").css({
        transform:
          val === "horizontal"
            ? ""
            : `rotate(90deg) translate(0px, ${-height}px)`,
        "transform-origin": "0 0",
      });
    },
    handleBlockChange(val) {
      const blocks = $("#printImg").find(".topo-line-block");

      const fillColorMap = {
        "rgb(76, 104, 131)": "rgb(215,255,255)",
        "rgb(90, 74, 120)": "rgb(235,233,255)",
        "rgb(118, 89, 100)": "rgb(255,233,240)",
        "rgb(116, 111, 103)": "rgb(252,255,235)",
        "rgb(73, 119, 115)": "rgb(235,255,239)",
      };

      blocks.each(function () {
        let fillBackup = $(this).attr("data-fill");

        $(this).css({
          "stroke-dasharray": "10 10",
          fill: val ? fillColorMap[fillBackup] : "transparent",
        });
      });
    },
    handleBgChange(val) {
      $("#printImg").css({
        background: val ? "rgb(39, 39, 39)" : "transparent",
      });
      $("#printContent").css({
        background: val ? "rgb(39, 39, 39)" : "#ffffff",
      });
      const texts = $("#printImg").find("text");
      texts.each(function () {
        const color = $(this).css("fill");
        if (val) {
          color === "rgb(0, 0, 0)" && $(this).css("fill", "rgb(255, 255, 255)");
        } else {
          color === "rgb(255, 255, 255)" && $(this).css("fill", "rgb(0, 0, 0)");
        }
      });
    },
    previewImage() {
      const el = document.querySelector("#printContent");
      el.requestFullscreen();
    },
  },
};
</script>

<style lang="scss">
#printContent {
  width: 100%;
  height: 450px;
  // display: flex;
  // justify-content: center;
  overflow: auto;
}
@media print {
  #printImg {
    -webkit-print-color-adjust: exact !important;
  }
}
</style>
