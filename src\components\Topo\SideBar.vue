<template>
  <div class="topo-side-bar">
    <Icon
      type="ios-albums"
      @click="isVisible = !isVisible"
      style="cursor: pointer; font-size: 24px"
    />

    <mapMeta v-show="isVisible"></mapMeta>
  </div>
</template>

<script>
import mapMeta from "./MapMeta.vue";

export default {
  name: "SideBar",
  components: { mapMeta },
  data() {
    return {
      isVisible: false,
    };
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="scss" scoped>
.topo-side-bar {
  position: fixed;
  right: 310px;
  top: 73px;
  width: 20px;
  height: 20px;
  // background-color: #fff;
  z-index: 99;
}
</style>
