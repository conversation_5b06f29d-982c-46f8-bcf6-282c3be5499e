.ad-SVG {
  display: block;
  background: #fff;
  border-radius: 4px;
}
.ad-Path {
  fill: none;
  stroke: #555;
  stroke-width: 4px;
  stroke-linecap: round;
}
.topo-svg-editor-layout {
  background-color: $svgEditColor;
  position: relative;
  height: calc(100vh - 90px);
  width: 100%;
  overflow: auto;
}
.selectRectangleTop {
  position: absolute;
  background-color: #87ceeb;
  opacity: 0.7;
  border: 1px solid #00bfff;
  z-index: 99;
}
#topoEdit {
  position: absolute;
  border-width: 1px;
  overflow: hidden;
  left: 0;
  width: 800px;
  height: 400px;
  border-color: #d0d0d0;
  border-style: solid;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  user-select: none;
}
.topo-svg-bg {
  position: absolute;
  overflow: hidden;
  left: 0;
  top: 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.scale-view {
  width: 200px;
  height: 200px;
  background-color: rgb(96, 140, 233);
  position: absolute;
  left: 0;
  top: 0;
}
.topo-custom-component :hover {
  // filter: drop-shadow(0px 3px 3px rgba(0, 0, 0, 0.4));
}

.topo-select-view {
  position: fixed;
  border: 1px solid #00a8ff;
  background-color: rgba($color: #00a8ff, $alpha: 0.4);
}
#textWidth {
  position: absolute;
  display: inline-block;
  line-height: 1;
  visibility: hidden;
  white-space: nowrap;
  z-index: -900;
  white-space: pre;
}
