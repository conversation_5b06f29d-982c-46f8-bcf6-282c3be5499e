<template>
  <Modal v-model="isVisible" title="编辑子图层">
    <Form
      v-if="isVisible"
      ref="formValidate"
      :model="sublayerForm"
      :rules="ruleValidate"
      label-position="right"
      :label-width="100"
    >
      <FormItem label="ID" prop="sublayerId">
        <p>{{ sublayerInfo.sublayerId }}</p>
      </FormItem>
      <FormItem label="名称" prop="sublayerName">
        <Input v-model="sublayerForm.sublayerName"> </Input>
      </FormItem>
      <FormItem label="大屏" prop="isVisible">
        <Tooltip content="是否在大屏上显示">
          <i-switch
            v-model="sublayerForm.isVisible"
            :true-value="1"
            :false-value="0"
            size="large"
          >
            <span slot="open">显示</span>
            <span slot="close">隐藏</span>
          </i-switch>
        </Tooltip>
      </FormItem>
      <FormItem label="排序" prop="listOrder">
        <InputNumber
          :min="1"
          :precision="0"
          v-model="sublayerForm.listOrder"
        ></InputNumber>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="closeModal">取消</Button>
      <Button type="primary" @click="submitForm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "SublayerEditModal",
  props: {
    mapId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      isVisible: false,
      sublayerSelectedList: [],
      sublayerForm: {
        sublayerName: "",
        isVisible: 1,
        listOrder: 1,
      },
      sublayerInfo: {},
      ruleValidate: {
        sublayerName: [
          {
            required: true,
            message: "名称不能为空",
            trigger: "blur",
          },
        ],
        listOrder: [
          {
            required: true,
            message: "排序不能为空",
          },
        ],
      },
    };
  },
  computed: {
    ...mapState("topoStore", {
      sublayerList: "sublayerList",
    }),
    nodeLinkList() {
      const nodes = [];
      const links = [];
      this.nodeSelectedList.forEach((ele) => {
        ele.nodeId && nodes.push(ele);
        ele.linkId && links.push(ele);
      });

      return {
        nodes,
        links,
      };
    },
  },
  methods: {
    show(val) {
      this.sublayerInfo = val;
      this.sublayerForm.sublayerName = val.sublayerName;
      // 防止undefined
      this.sublayerForm.isVisible = val.isVisible || 0;
      this.sublayerForm.listOrder = val.listOrder;
      this.isVisible = true;
    },
    submitForm() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          const params = {
            ...this.sublayerForm,
            mapId: this.mapId,
            sublayerId: this.sublayerInfo.sublayerId,
          };
          this.$post(`/topoEdit/updateSublayer`, params).then(() => {
            this.$Message.success("修改成功");
            this.$bus.emit("onGetSublayerList", true);
            this.closeModal();
          });
        }
      });
    },
    closeModal() {
      this.isVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.ivu-form-item {
  margin-bottom: 5px;
}
</style>
