<template>
  <Modal v-model="isVisible" title="数据批量绑定">
    <div class="multi-data-bind-layout">
      <Tree
        :data="menuMapList"
        show-checkbox
        @on-check-change="handleCheckChange"
      ></Tree>
    </div>
    <div slot="footer">
      <Button type="text" @click="hide">取消</Button>
      <Button type="primary" @click="ok">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  name: "MapSelectModal",
  props: {},
  data() {
    return {
      isVisible: false,
      mapIds: [],
      currentNodeLink: {},
      currentNodeLinkList: [],
      isMulti: false,
    };
  },
  computed: {
    ...mapState("topoStore", {
      menuMapList: "menuMapList",
    }),
  },
  methods: {
    ...mapActions("topoStore", [
      "updateMetaDataByMapIdAndObjId",
      "updateMetaDataByMapIdsAndObjIds",
    ]),
    show(val, type) {
      this.isVisible = true;
      this.isMulti = type === "multi";
      if (type === "multi") {
        this.currentNodeLinkList = val;
      } else {
        this.currentNodeLink = val;
      }
    },
    hide() {
      this.isVisible = false;
      this.currentNodeLink = {};
    },
    handleCheckChange(val) {
      this.mapIds = val.filter((ele) => ele.isMap).map((ele) => ele.key);
    },
    getParams() {
      const nodeIdList = [];
      const linkIdList = [];
      let originMapId = "";
      this.currentNodeLinkList.forEach((ele) => {
        originMapId = ele.mapId;
        if (ele.linkId) {
          linkIdList.push(ele.linkId);
        } else {
          nodeIdList.push(ele.nodeId);
        }
      });

      return {
        nodeIdList,
        linkIdList,
        originMapId,
      };
    },
    ok() {
      if (!this.mapIds.length) {
        return this.$Message.warning("请选择图层");
      }
      if (this.isMulti) {
        const parmas = {
          ...this.getParams(),
          mapIdList: this.mapIds,
        };
        this.updateMetaDataByMapIdsAndObjIds(parmas).then((result) => {
          this.hide();
        });
      } else {
        this.updateMetaDataByMapIdAndObjId({
          nodeLink: this.currentNodeLink,
          mapIds: this.mapIds,
        }).then((result) => {
          this.hide();
        });
      }
    },
  },
};
</script>

<style lang="scss">
.multi-data-bind-layout {
  height: 300px;
  overflow: auto;
}
</style>
