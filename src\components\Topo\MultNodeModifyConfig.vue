<template>
  <div style="padding: 0 16px">
    <Row class="mb-5" v-for="item in options" :key="item.label">
      <Col span="4" class="text-left">{{ item.title }}</Col>
      <Col span="20" class="text-left">
        <CheckboxGroup
          v-model="formSearch[item.label + 'List']"
          @on-change="handleChange"
        >
          <Checkbox
            v-for="itemChild in item.list"
            :label="itemChild"
            :key="itemChild"
          >
            <span
              v-if="item.label === 'linkColor' || item.label === 'fontColor'"
              :style="{
                width: '12px',
                height: '12px',
                display: 'inline-block',
                backgroundColor: itemChild,
              }"
            ></span>
            <span v-else>{{ itemChild }}</span>
          </Checkbox>
        </CheckboxGroup>
      </Col>
    </Row>
    <p>已筛选: {{ count }}</p>
    <div class="setting-btn-group">
      <Button type="primary" size="small" @click="clearOptions">重置</Button>
      <Button type="primary" size="small" @click="refreshOptions">刷新</Button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "MultNodeModifyConfig",
  props: {
    mapId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      formSearch: {
        categoryList: [],
        nodeTypeList: [],
        typeList: [],
        voltList: [],
        fontSizeList: [],
        fontColorList: [],
        linkWidthList: [],
        linkColorList: [],
      },
      options: [
        {
          title: "分类",
          label: "category",
          list: ["节点", "连线"],
        },
        {
          title: "类型",
          label: "nodeType",
          list: [],
        },
        {
          title: "type",
          label: "type",
          list: [],
        },
        {
          title: "volt",
          label: "volt",
          list: [],
        },
        {
          title: "字号",
          label: "fontSize",
          list: [],
        },
        {
          title: "字色",
          label: "fontColor",
          list: [],
        },
        {
          title: "线宽",
          label: "linkWidth",
          list: [],
        },

        {
          title: "线色",
          label: "linkColor",
          list: [],
        },
      ],
      nodeLinkSublayerList: [],
      linkList: [],
      nodeList: [],
      count: 0,
    };
  },
  mounted() {
    this.initEventBus();
  },
  beforeDestroy() {
    this.$bus.off("onNodeLinkSublayerListChange");
  },
  computed: {
    ...mapState("topoStore", {}),
  },
  methods: {
    hexToRgb(hex) {
      if (hex === "#fff") {
        hex = "#ffffff";
      }
      return `rgb(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
        "0x" + hex.slice(3, 5)
      )},${parseInt("0x" + hex.slice(5, 7))})`;
    },
    clearOptions() {
      this.$bus.emit("onMultSetting", this.nodeLinkSublayerList);
      this.count = this.nodeLinkSublayerList.length;
      this.formSearch = {
        categoryList: [],
        nodeTypeList: [],
        fontSizeList: [],
        fontColorList: [],
        linkWidthList: [],
        linkColorList: [],
      };
      this.generateOptions();
    },
    refreshOptions() {
      this.generateOptions();
    },
    generateOptions() {
      this.nodeList = [];
      this.linkList = [];
      const nodeType = new Set();
      const types = new Set();
      const volts = new Set();
      const fontSize = new Set();
      const fontColor = new Set();
      const linkWidth = new Set();
      const linkColor = new Set();
      for (let i = 0; i < this.nodeLinkSublayerList.length; i++) {
        const element = this.nodeLinkSublayerList[i];
        const { type, volt } = element.metaData || {};

        if (element.nodeId) {
          this.nodeList.push(element);
          element.nodeType && nodeType.add(element.nodeType);
          element.fontSize && fontSize.add(element.fontSize);
          if (element.fontColor) {
            const color = element.fontColor.includes("#")
              ? this.hexToRgb(element.fontColor)
              : element.fontColor;
            fontColor.add(color);
          }
        } else {
          this.linkList.push(element);

          element.linkWidth && linkWidth.add(element.linkWidth);
          const styles = JSON.parse(element.linkStyles);
          if (styles.color) {
            const color = styles.color.includes("#")
              ? this.hexToRgb(styles.color)
              : styles.color;
            linkColor.add(color);
          }
        }
        type && types.add(type);
        volt && volts.add(volt);
      }

      this.options[1].list = Array.from(nodeType);
      this.options[2].list = Array.from(types);
      this.options[3].list = Array.from(volts);
      this.options[4].list = Array.from(fontSize).sort((a, b) => a - b);
      this.options[5].list = Array.from(fontColor);
      this.options[6].list = Array.from(linkWidth).sort((a, b) => a - b);
      this.options[7].list = Array.from(linkColor);
    },
    initEventBus() {
      this.$bus.on("onNodeLinkSublayerListChange", (list) => {
        this.nodeLinkSublayerList = list;
        this.generateOptions();
      });
    },
    handleChange() {
      const {
        categoryList,
        nodeTypeList,
        typeList,
        voltList,
        fontSizeList,
        fontColorList,
        linkWidthList,
        linkColorList,
      } = this.formSearch;
      let linkList = [];
      let nodeList = [];

      if (categoryList) {
        if (categoryList.includes("节点")) {
          nodeList = this.nodeList;
        }
        if (categoryList.includes("连线")) {
          linkList = this.linkList;
        }
        if (nodeTypeList.length) {
          nodeList = nodeList.filter((ele) =>
            nodeTypeList.includes(ele.nodeType)
          );
        }
        if (typeList.length) {
          nodeList = nodeList.filter((ele) =>
            typeList.includes(ele.metaData.type)
          );
        }
        if (voltList.length) {
          nodeList = nodeList.filter((ele) =>
            voltList.includes(ele.metaData.volt)
          );
        }
        if (fontSizeList.length) {
          nodeList = nodeList.filter((ele) =>
            fontSizeList.includes(ele.fontSize)
          );
        }
        if (fontColorList.length) {
          nodeList = nodeList.filter((ele) => {
            if (ele.fontColor) {
              let color = ele.fontColor.includes("#")
                ? this.hexToRgb(ele.fontColor)
                : ele.fontColor;
              return fontColorList.includes(color);
            }
          });
        }

        if (linkWidthList.length) {
          linkList = linkList.filter((ele) =>
            linkWidthList.includes(ele.linkWidth)
          );
        }
        if (linkColorList.length) {
          linkList = linkList.filter((ele) => {
            const styles = JSON.parse(ele.linkStyles);

            if (styles.color) {
              let color = styles.color.includes("#")
                ? this.hexToRgb(styles.color)
                : styles.color;
              return linkColorList.includes(color);
            }
          });
        }
      }

      this.count = linkList.length + nodeList.length;
      this.$bus.emit("onMultSetting", [...linkList, ...nodeList]);
    },
  },
};
</script>

<style lang="scss" scoped>
.setting-btn-group {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}
</style>
