<template>
  <div>
    <Modal
      v-model="isJScodeVisible"
      title="自定义样式"
      @on-ok="handleCodeSubmit"
      @on-cancel="handleCodeCancel"
    >
      <CodeMirror
        v-if="isJScodeVisible"
        :value="jsCode"
        ref="codeMirror"
      ></CodeMirror>
      <div slot="footer">
        <Button type="error" @click="testCode"> 执行 </Button>
        <Button type="info" @click="handleCodeSubmit"> 确定 </Button>
        <Button @click="handleCodeCancel"> 取消 </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import CodeMirror from "@/components/CodeMirror/CodeMirror";

export default {
  props: {
    styles: {
      type: Object,
      dafault: () => {},
    },
  },
  components: { CodeMirror },
  data() {
    return {
      jsCode: "",
      bindData: {},
      isJScodeVisible: false,
    };
  },
  mounted() {},
  methods: {
    show() {
      this.isJScodeVisible = true;
      const code = this.styles || "";
      this.jsCode = typeof code === "object" ? JSON.stringify(code) : code;
    },
    handleCodeCancel() {
      this.isJScodeVisible = false;
    },
    testCode() {
      const string = this.$refs.codeMirror.code;
      if (string.length == 0) {
        this.$Message.error(`请编辑需要执行的代码`);
        return;
      }
      try {
        if (
          ["[object Object]"].includes(
            Object.prototype.toString.call(JSON.parse(string))
          )
        ) {
          this.$Message.success(`代码格式无错误`);
        }
      } catch (e) {
        this.$Message.error(`代码异常，出现以下错误${e}`);
      }
    },
    handleCodeSubmit() {
      const { code } = this.$refs.codeMirror;
      try {
        const result = code ? JSON.parse(code) : {};
        this.$emit("handleChange", result);
        this.handleCodeCancel();
      } catch (e) {
        this.$Message.warning("JSON格式不正确");
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
