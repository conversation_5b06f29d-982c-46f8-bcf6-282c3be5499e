const dragPointDirection = [
  {
    duration: "nw-resize",
    id: "leftTop",
  },
  {
    duration: "n-resize",
    id: "top",
  },
  {
    duration: "ne-resize",
    id: "topRight",
  },
  {
    duration: "e-resize",
    id: "right",
  },
  {
    duration: "se-resize",
    id: "rightBottom",
  },
  {
    duration: "s-resize",
    id: "bottom",
  },
  {
    duration: "sw-resize",
    id: "leftBottom",
  },
  {
    duration: "w-resize",
    id: "left",
  },
];

// 线路：
// rtKeyId：线路数据点号
// rtKeyIdDesc：线路名称
// rtKeyId1：线路一端数据点号
// rtKeyId1Desc：线路一端描述
// rtKeyId2Desc：线路另一端描述
// volt：电压等级
// 文字Text：
// volt：电压等级
// 文字DText：
// volt：电压等级
// rtKeyId：数据点号
// 变电站：
// volt：电压等级
// name：名称
// num：动态数据数量
// rtKeyId+序号：对应数据点号
// rtKeyIdDesc+序号：厂站名称（序号0），各设备序号从1依次增加
// lowBusDesc+序号：低压母线描述
// highBusDesc+序号：高压母线描述
// xmfrDesc+序号：主变描述
// switchDesc+序号：开关描述
// lowBusNo：低压母线序号逗号分割，表示哪些是低压母线
// highBusNo：高压母线序号逗号分隔，表示哪些是高压母线
// xfmrNo：主变序号逗号分隔，表示哪些是主变
// switchNo：开关序号逗号分割，表示哪些是开关，开关格式2_1_2,第一个2表示序号2是开关，后面的1，2表示序号2开关在index是1，2的母线之间，就是lowBusNo逗号分割之后的第1，2个母线
// xfmr_num：主变数量
// switch_num：开关数量
// highbus_num：高压母线数量
// lowbus_num：低压母线数量
// 电厂：
// name：电厂名称
// volt：电压等级
// powerType：电厂类型 风电：WIND火电：THERMAL核电：NUCLEAR光伏：PV
// rtKeyId+序号：数据点号
// keyDesc+序号：机组说明
// mvarate+序号：机组额定容量
// num：设备数量
// no：机组序号逗号分隔，位置代表几号机组，第一位就是1号机组
const nodeLinkDescriptionMap = {
  rtKeyId: "线路数据点号",
  rtKeyIdDesc: "线路名称",
  rtKeyId1: "线路一端数据点号",
  rtKeyId1Desc: "线路一端描述",
  rtKeyId2Desc: "线路另一端描述",
  volt: "电压等级",
  name: "名称",
  num: "动态数据数量",
  lowBusDesc: "低压母线描述",
  highBusDesc: "高压母线描述",
  xmfrDesc: "主变描述",
  switchDesc: "开关描述",
  lowBusNo: "低压母线序号",
  highBusNo: "高压母线序号",
  xfmrNo: "主变序号逗号分隔，表示哪些是主变",
  switchNo:
    "开关序号逗号分割，表示哪些是开关，开关格式2_1_2,第一个2表示序号2是开关，后面的1，2表示序号2开关在index是1，2的母线之间，就是lowBusNo逗号分割之后的第1，2个母线",
  xfmr_num: "主变数量",
  switch_num: "开关数量",
  highbus_num: "高压母线数量",
  lowbus_num: "低压母线数量",
  powerType: {
    WIND: "风电",
    THERMAL: "火电",
    NUCLEAR: "核电",
    PV: "光伏",
  },
  type: {
    TransStation: "电站",
    PowerStation: "电厂",
  },
  keyDesc: "机组说明",
  mvarate: "机组额定容量",
  no: "机组序号逗号分隔，位置代表几号机组，第一位就是1号机组",
  layerId: "图层id",
};

const dasharrayOptions = [
  {
    value: "实线",
    label: "实线",
  },
  {
    value: "10,10",
    label: "虚线1",
  },
  {
    value: "10,10",
    label: "虚线2",
  },
  {
    value: "15,15",
    label: "虚线3",
  },
  {
    value: "20,20",
    label: "虚线4",
  },
];

const fontSizeList = [
  "12px",
  "13px",
  "14px",
  "15px",
  "16px",
  "17px",
  "18px",
  "19px",
  "20px",
  "21px",
  "22px",
  "23px",
  "24px",
  "25px",
  "26px",
  "27px",
  "28px",
  "29px",
  "30px",
  "31px",
  "32px",
  "33px",
  "34px",
  "35px",
  "36px",
  "37px",
  "38px",
  "39px",
  "40px",
  "41px",
  "42px",
  "43px",
  "44px",
  "45px",
  "46px",
  "47px",
  "48px",
  "49px",
  "50px",
  "51px",
  "52px",
  "53px",
  "54px",
  "55px",
  "56px",
  "57px",
  "58px",
  "59px",
  "60px",
  "61px",
  "62px",
  "63px",
  "64px",
  "65px",
  "66px",
  "67px",
  "68px",
  "69px",
  "70px",
  "71px",
  "72px",
  "73px",
  "74px",
  "75px",
  "76px",
  "77px",
  "78px",
  "79px",
  "80px",
  "81px",
  "82px",
  "83px",
  "84px",
  "85px",
  "86px",
  "87px",
  "88px",
  "89px",
  "90px",
  "91px",
  "92px",
  "93px",
  "94px",
  "95px",
  "96px",
  "97px",
  "98px",
  "99px",
  "100px",
  "101px",
  "102px",
  "103px",
  "104px",
  "105px",
  "106px",
  "107px",
  "108px",
  "109px",
  "110px",
  "111px",
  "112px",
  "113px",
  "114px",
  "115px",
  "116px",
  "117px",
  "118px",
  "119px",
  "120px",
  "121px",
  "122px",
  "123px",
  "124px",
  "125px",
  "126px",
  "127px",
  "128px",
  "129px",
  "130px",
  "131px",
  "132px",
  "133px",
  "134px",
  "135px",
  "136px",
  "137px",
  "138px",
  "139px",
  "140px",
  "141px",
  "142px",
  "143px",
  "144px",
  "145px",
  "146px",
  "147px",
  "148px",
  "149px",
  "150px",
];

export {
  dragPointDirection,
  nodeLinkDescriptionMap,
  dasharrayOptions,
  fontSizeList,
};
