const idList = [
  { "I4YkyanqYz#653899_113997366104687051": "green" },
  { "lvsm9Dnkty#412412_113997366104687051": "green" },
  { "oGXGROEU0T#60125_113997366104687051": "green" },
  { "FNj1UM7kqA#60126_113997366104687051": "green" },
  { "9ZaRKhI3KV#61295_113997366104687051": "green" },
  { "lvFc9SuL4f#61297_113997366104687051": "green" },
  { kGfLO6vQXa: "green" },
  { oSOWfFUs6m: "green" },
  { w9cPXLG2EH: "green" },
  { zm6ZuoJEWy: "green" },
  { "0PZwDELlY8": "green" },
  { HGKaNJdF4C: "green" },
  { XxeETma0OL: "green" },
  { iCLCk2GScl: "green" },
  { BN61iOBoBx: "green" },
  { udyDGH1wBh: "green" },
  { AkONIdf9rw: "green" },
  { OLnkLaRukY: "green" },
  { xzhfvr7f0T: "green" },
  { ICw4ke6Es1: "green" },
  { u8it0ifhW8: "green" },
  { "mWCtLQ9uyM#706695_113997366104686638": "green" },
  { kNLaCXhZ84: "green" },
  { bMB6SlScaz: "green" },
  { o5KyFmaW2a: "green" },
  { Yi8uqtM9C2: "green" },
  { K2WTLBdER5: "green" },
  { aaXf1ImoA4: "green" },
  { "7iozplzEIs": "green" },
  { zqTXtXzzYc: "green" },
  { "3zWoB1eCfc#60739_113997366104686638": "green" },
  { "JAj8GuAfiS#59052_113997366104686638": "green" },
  { "dTvn8NMYBD#663853_113997366104686638": "green" },
  { "fOyO8Nq9m9#663855_113997366104686638": "green" },
  { nP9TLjbP8d: "green" },
  { SaBPqsWMG1: "green" },
  { "87cmhkE1LG": "green" },
  { C7KpswfR6b: "green" },
  { Xu2aCqGGJu: "green" },
  { rw9cXEhHQb: "green" },
  { "kMQcijNGpV#509854_113997366104686638": "green" },
  { "dKNABEyzsy#509852_113997366104686638": "green" },
  { "va0lRJnfuV#509855_113997366104686638": "green" },
  { "Z8yraNmOjn#509857_113997366104686638": "green" },
  { IFWhuhhDac: "green" },
  { rYr7hNbSBz: "green" },
];

export const setNodeLinkColor = (ele) => {
  const isInclude = idList.includes(ele.nodeId || ele.linkId);
  if (!isInclude) return;
  if (ele.nodeId && isInclude) {
    const styles = JSON.parse(ele.nodeStyles);
    styles.dynamicColor = idList[ele.nodeId];
    ele.nodeStyles = JSON.stringify(styles);
  } else {
    const styles = JSON.parse(ele.linkStyles);
    styles.dynamicColor = idList[ele.linkId];
    ele.linkStyles = JSON.stringify(styles);
  }
};
