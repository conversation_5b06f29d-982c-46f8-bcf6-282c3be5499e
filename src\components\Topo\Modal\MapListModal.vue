<template>
  <Modal v-model="isVisible" title="图层列表">
    <div style="margin-bottom: 50px">
      请选择图层：
      <Select v-model="menuSelected" style="width: 80%">
        <OptionGroup
          v-for="item in mapList"
          :key="item.menuId"
          :label="item.menuName"
        >
          <Option
            v-for="map in item.maps"
            :key="map.mapId"
            :value="map.mapId"
            >{{ map.mapName }}</Option
          >
        </OptionGroup>
      </Select>
    </div>
    <div slot="footer">
      <Button type="text" @click="hide">取消</Button>
      <Button type="primary" @click="handleSubmit">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";

export default {
  data() {
    return {
      isVisible: false,
      tableData: [],
      versionRowSelected: {},
      columns: [
        {
          title: "版本名称",
          key: "versionName",
        },
        {
          title: "临时版本",
          slot: "dummy",
        },
        {
          title: "操作",
          slot: "action",
          width: 150,
        },
      ],
      mapList: [],
      menuSelected: "",
    };
  },
  computed: {
    ...mapState("topoStore", {
      versionSelected: "versionSelected",
      versionList: "versionList",
    }),
  },
  methods: {
    ...mapMutations("topoStore", ["setVersionSelected"]),
    ...mapActions("topoStore", ["getVersionList"]),
    // 表格选择回显
    initData() {
      this.tableData = this.versionList.map((ele) => {
        ele._highlight = ele.versionId === this.versionSelected.versionId;
        return ele;
      });
    },
    show() {
      this.initData();
      this.isVisible = true;
      this.fetchmapList();
    },
    hide() {
      this.isVisible = false;
    },
    handleSubmit() {
      if (!this.menuSelected) {
        this.$Message.error("请选择图层");
        return;
      }
      window.open(`static/nariTopoView/test.html?mapId=${this.menuSelected}`);
      this.hide();
    },
    // 获取图层列表
    fetchmapList() {
      this.$get(`/topoEdit/getMenuList`).then(({ data }) => {
        if (data.code == "0000") {
          this.generatemapList(data.data);
        }
      });
    },
    // 递归获取所有的目录以及子目录 ，生成tree
    generatemapList(data) {
      let mapList = [];
      const getList = (list) => {
        list.forEach((ele) => {
          if (ele.maps && ele.maps.length) {
            mapList.push(ele);
          }
          if (ele.children && ele.children.length) {
            getList(ele.children);
          }
        });
      };
      getList(data, "");
      this.mapList = mapList;
      console.log(
        "🚀 ~ file: MapListModal.vue:101 ~ generatemapList ~ mapList:",
        mapList
      );
    },
  },
};
</script>

<style lang="scss">
.topo-form-item {
  margin-bottom: 12px;
}
.topo-form-item .ivu-form-item-content {
  margin-left: 20px !important;
  padding: 0 30px;
}
</style>
