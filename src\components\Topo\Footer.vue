<template>
  <div class="topo-footer" v-show="mapInfo.mapId">
    <div style="margin-left: 30px">
      <span>{{ mapInfo.mapName }}</span>
      <span class="ml-5 click" @click="showVersionModal"
        >版本:{{ versionSelected.versionName }}</span
      >
    </div>
    <!-- <Slider style="width: 200px" v-model="scale"></Slider> -->
    <VersionListModal ref="versionListModal"></VersionListModal>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";

import VersionListModal from "./Modal/VersionListModal.vue";
export default {
  components: { VersionListModal },
  data() {
    return {
      scale: 20,
    };
  },
  computed: {
    ...mapState("topoStore", {
      versionSelected: "versionSelected",
      mapInfo: "mapInfo",
    }),
  },
  methods: {
    showVersionModal() {
      this.$refs.versionListModal.show();
    },
  },
};
</script>

<style lang="scss" scoped>
.topo-footer {
  //   position: fixed;
  //   bottom: 0;
  width: 100%;
  height: 30px;
  background-color: $baseBgColor;
  line-height: 30px;
  font-size: 12px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  .ivu-slider-wrap {
    margin: 13px 0;
  }
}
</style>
