const subLayerList = [
  {
    name: "南京",
    smallList: [
      "FS5tPfwEKT",
      "FS5wMfwEKT",
      "KS3tPfwEKM",
      "W4WacTIW5M",
      "FS3tPfwEKF",
      "M4WacTIW5E",
      "Fw5TXwwEDo",
      "GeCPon7cD",
      "Sz7bYXhs7D5",
      "SV14VwvEwFF",
      "SG3ULIThlwJ",
      "Sxj8antqCnD",
      "SxYk5QUYyPl",
    ],
    fullViewList: [""],
  },
  {
    name: "苏州",
    smallList: ["SRuGzwKHFHp", "SdRtv5MvuJ5"],
    fullViewList: [""],
  },
  { name: "无锡", smallList: ["Stm4WOGbdHH"], fullViewList: [""] },
  { name: "镇江", smallList: ["S29lkkuqslR"], fullViewList: [""] },
  {
    name: "扬州",
    smallList: ["S36jnwd8EXH", "SRxLGa32nHK"],
    fullViewList: [""],
  },
  {
    name: "盐城",
    smallList: ["S5FsC1CybRC", "SsC8fH6EQHe"],
    fullViewList: [""],
  },
  {
    name: "徐州",
    smallList: [
      "FS5tPfwEKT",
      "FS5wMfwEKT",
      "KS3tPfwEKM",
      "W4WacTIW5M",
      "FS3tPfwEKF",
      "M4WacTIW5E",
      "Fw5TXwwEDo",
      "GeCPon7cD",
      "SqKy58L0FMi",
      "S3uJiRTHT7z",
      "SIOlmTNtCrT",
      "S8SQB4dsFUS",
      "S3ot3yNbgiD",
    ],
    fullViewList: [""],
  },
  {
    name: "泰州",
    smallList: ["SslirciPQil", "Sa5UltbdynS", "SobfTgIeyvf"],
    fullViewList: ["SCtBKhR78xs", "SobfTgIeyvf"],
  },
  { name: "宿迁", smallList: ["SoiaL1J5tGB"], fullViewList: [""] },
  {
    name: "南通",
    smallList: [
      "FS5tPfwEKT",
      "FS5wMfwEKT",
      "KS3tPfwEKM",
      "W4WacTIW5M",
      "Sxj1QeDYF2G",
      "FS3tPfwEKF",
      "M4WacTIW5E",
      "SKzNoEbwja4",
      "SfYItaUPDB1",
      "Fw5TXwwEDo",
      "GeCPon7cD",
      "Sn12jytHUcV",
      "SA0LKB5VZld",
      "SLmH691i5FC",
      "SsAulb8VKBv",
      "SR2ngYAJxeo",
    ],
    fullViewList: [""],
  },
  { name: "连云港", smallList: ["SBYFIcEftUo"], fullViewList: [""] },
  { name: "淮安", smallList: ["S7lmk9ReHS0"], fullViewList: [""] },
  {
    name: "常州",
    smallList: ["S72dUT4JrpL", "SeO9tbwVaU2"],
    fullViewList: ["SIABW5uIcuz"],
  },
];

export const getSubLayerList = (name, isFullView) => {
  const subLayer = subLayerList.find((item) => name.includes(item.name));
  return subLayer
    ? isFullView
      ? subLayer.fullViewList
      : subLayer.smallList
    : [];
};
