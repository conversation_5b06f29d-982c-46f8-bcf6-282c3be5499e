<template>
  <div class="topo-edit-layout">
    <TopoHeader></TopoHeader>
    <div class="topo-edit-container">
      <div class="topo-edit-container-main"><SvgContent></SvgContent></div>
    </div>
    <Footer></Footer>
    <Spin size="large" fix v-if="spinShow" style="z-index: 3000">
      <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
      <div>{{ spinContent }}</div>
    </Spin>
  </div>
</template>
<script>
import { mapState } from "vuex";

import TopoHeader from "@/components/Topo/Header";
import SvgContent from "@/components/Topo/SvgContent";
import Footer from "@/components/Topo/Footer.vue";

export default {
  components: {
    TopoHeader,
    SvgContent,
    Footer,
  },
  data() {
    return {
      property: "value",
    };
  },
  computed: {
    ...mapState("topoStore", {
      spinShow: "spinShow",
      spinContent: "spinContent",
    }),
  },
};
</script>
<style lang="scss">
.topo-edit-layout {
  width: 100%;
  height: 100vh;
  // background-color: #fff;
  .topo-edit-container {
    position: relative;
    display: flex;
    flex: 1;
  }
  .topo-edit-container-main {
    display: flex;
    flex: 1;
    height: 100%;
  }
  .spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
</style>
