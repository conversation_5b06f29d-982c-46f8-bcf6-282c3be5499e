import setLang from "../lang";

const lang = {
  i: {
    locale: "pl-PL",
    select: {
      placeholder: "<PERSON><PERSON><PERSON><PERSON>",
      noMatch: "Brak pasujących wyników",
      loading: "Ładowanie",
    },
    table: {
      noDataText: "Brak danych",
      noFilteredDataText: "Brak danych",
      confirmFilter: "<PERSON>twierdź",
      resetFilter: "<PERSON>setuj",
      clearFilter: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      sumText: "<PERSON><PERSON><PERSON>",
    },
    datepicker: {
      selectDate: "Wybierz datę",
      selectTime: "<PERSON><PERSON><PERSON><PERSON> godzinę",
      startTime: "<PERSON><PERSON>a początkowa",
      endTime: "<PERSON><PERSON><PERSON> końcowa",
      clear: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      ok: "OK",
      datePanelLabel: "[mmmm] [yyyy]",
      month: "Miesiąc",
      month1: "<PERSON>ycz<PERSON><PERSON>",
      month2: "<PERSON><PERSON>",
      month3: "<PERSON><PERSON><PERSON>",
      month4: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      month5: "<PERSON>",
      month6: "<PERSON><PERSON><PERSON><PERSON>",
      month7: "Lipiec",
      month8: "<PERSON><PERSON><PERSON><PERSON>",
      month9: "W<PERSON><PERSON><PERSON><PERSON>",
      month10: "<PERSON><PERSON><PERSON>zier<PERSON>",
      month11: "Listopad",
      month12: "Grudzień",
      year: "Rok",
      weekStartDay: "1",
      weeks: {
        sun: "Ndz",
        mon: "Pon",
        tue: "Wto",
        wed: "Śro",
        thu: "Czw",
        fri: "Pią",
        sat: "Sob",
      },
      months: {
        m1: "Sty",
        m2: "Lut",
        m3: "Mar",
        m4: "Kwi",
        m5: "Maj",
        m6: "Cze",
        m7: "Lip",
        m8: "Sie",
        m9: "Wrz",
        m10: "Paź",
        m11: "Lis",
        m12: "Gru",
      },
    },
    transfer: {
      titles: {
        source: "Źródłowy",
        target: "Docelowy",
      },
      filterPlaceholder: "Szukaj tutaj",
      notFoundText: "Nie znaleziono",
    },
    modal: {
      okText: "OK",
      cancelText: "Anuluj",
    },
    poptip: {
      okText: "OK",
      cancelText: "Anuluj",
    },
    page: {
      prev: "Poprzednia Strona",
      next: "Następna Strona",
      total: "Łącznie",
      item: "element",
      items: "elementów",
      prev5: "Poprzednie 5 Stron",
      next5: "Następne 5 Stron",
      page: "/stronę",
      goto: "Idź do",
      p: "",
    },
    rate: {
      star: "Gwiazdka",
      stars: "Gwiazdek",
    },
    time: {
      before: " temu",
      after: " po",
      just: "dopiero co",
      seconds: " sekund",
      minutes: " minut",
      hours: " godzin",
      days: " dni",
    },
    tree: {
      emptyText: "Brak danych",
    },
  },
};

setLang(lang);

export default lang;
