<template>
  <g
    :id="`group-${formatId(data.nodeId)}`"
    :transform="`translate(${data.tx}, ${data.ty}) 
            rotate(${data.rotate} ${data.middleRotatePoint.x} ${data.middleRotatePoint.y})`"
  >
    <rect
      :width="data.w"
      :height="data.h"
      :x="data.x"
      :y="data.y"
      :fill="nodeStyles.fill"
    >
    </rect>
    <NodeText :data="data"></NodeText>
    <rect
      class="custom-move-rect"
      :width="data.w"
      :height="data.h"
      :x="data.x"
      :y="data.y"
      :id="`rect-${formatId(data.nodeId)}`"
      fill="none"
      :stroke-width="isBindNode || data.selected ? 2 : 0"
      :stroke="data.locked ? '#7d8694' : colorSelected"
      stroke-dasharray="3 3"
      @mousedown="handleMousedown"
      @mouseup="handleMouseup"
      style="cursor: move; pointer-events: all"
    ></rect>
  </g>
</template>

<script>
import { mapState } from "vuex";
import NodeText from "./NodeText.vue";
import { formatId } from "@/utils/tools/format";

export default {
  name: "CosRect",
  components: { NodeText },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
    isBindNode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {
    ...mapState("topoStore", {
      colorSelected: "colorSelected",
    }),
    nodeStyles() {
      const { nodeStyles } = this.data;
      if (!nodeStyles) return {};
      return JSON.parse(nodeStyles);
    },
  },
  methods: {
    formatId,
    handleMousedown(e) {
      this.$emit("cusmousedown", e);
    },
    handleMouseup(e) {
      this.$emit("cusmouseup", e);
    },
  },
};
</script>

<style lang="scss" scoped>
.ract-comp {
  position: absolute;
}
</style>
