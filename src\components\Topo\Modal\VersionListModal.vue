<template>
  <Modal v-model="isVisible" title="版本列表" width="700">
    <div>当前版本： {{ versionSelected.versionName }}</div>
    <Table
      border
      :columns="columns"
      :data="tableData"
      max-height="420"
      highlight-row
      @on-row-click="handleRowClick"
      ref="currentRowTable"
    >
      <template #dummy="{ row }">
        <span>{{ row.dummy ? "是" : "否" }}</span>
      </template>
      <template #updatedTime="{ row }">
        <span>{{ getUpdatedTime(row) }}</span>
      </template>
      <template #action="{ row }">
        <Button size="small" @click="coverMap(row)" style="margin-left: 10px"
          >覆盖</Button
        >
        <Button
          v-if="row.dummy"
          type="error"
          size="small"
          @click="deleteRow(row)"
          style="margin-left: 10px"
          >删除</Button
        >
      </template>
    </Table>
    <div slot="footer">
      <Button type="text" @click="hide">取消</Button>
      <Button type="primary" @click="handleSubmit">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";

export default {
  data() {
    return {
      isVisible: false,
      tableData: [],
      versionRowSelected: {},
      columns: [
        {
          title: "版本名称",
          key: "versionName",
        },
        {
          title: "临时版本",
          slot: "dummy",
        },
        {
          title: "更新时间",
          slot: "updatedTime",
        },
        {
          title: "操作",
          slot: "action",
          width: 150,
        },
      ],
    };
  },
  computed: {
    ...mapState("topoStore", {
      versionSelected: "versionSelected",
      versionList: "versionList",
    }),
  },
  methods: {
    ...mapMutations("topoStore", ["setVersionSelected"]),
    ...mapActions("topoStore", ["getVersionList"]),
    // 表格选择回显
    initData() {
      this.tableData = this.versionList.map((ele) => {
        ele._highlight = ele.versionId === this.versionSelected.versionId;
        return ele;
      });
    },
    show() {
      this.initData();
      this.versionRowSelected = this.versionSelected;
      this.isVisible = true;
    },
    hide() {
      this.isVisible = false;
    },
    handleSubmit() {
      this.setVersionSelected(this.versionRowSelected);
      this.$bus.emit("onVersionSlected");
      this.hide();
    },
    handleRowClick(val) {
      if (val.versionId === this.versionRowSelected.versionId) {
        this.versionRowSelected = "";
        this.$refs.currentRowTable.clearCurrentRow();
      } else {
        this.versionRowSelected = _.cloneDeep(val);
      }
    },
    coverMap(row) {
      this.$Modal.confirm({
        title: "警告",
        content: "是否要将数据覆盖草稿",
        onOk: () => {
          this.$post(`/topoEdit/coverDraft`, {
            mapId: row.mapId,
            versionId: row.versionId,
          }).then(() => {
            this.$Message.success("覆盖成功");
            this.setVersionSelected();
            this.$bus.emit("onVersionSlected");
            this.hide();
          });
        },
        onCancel: () => {},
      });
    },
    /**
     * 时间加上8小时
     * @param {*} row
     */
    getUpdatedTime(row) {
      const date = new Date(row.updatedTime);
      date.setHours(date.getHours() + 8);
      return date.toLocaleString().replace(/\//g, "-");
    },
    deleteRow(row) {
      if (row.versionId === this.versionSelected.versionId) {
        return this.$Message.warning("已选择当前版本，无法删除！");
      }
      this.$Modal.confirm({
        title: "警告",
        content: "是否要删除临时版本",
        onOk: () => {
          this.$post(`/topoEdit/deleteDummyVersion`, {
            mapId: row.mapId,
            versionId: row.versionId,
          }).then(() => {
            this.$Message.success("删除成功");
            this.getVersionList(row.mapId).then(() => {
              this.initData();
            });
          });
        },
        onCancel: () => {},
      });
    },
  },
};
</script>

<style lang="scss">
.topo-form-item {
  margin-bottom: 12px;
}
.topo-form-item .ivu-form-item-content {
  margin-left: 20px !important;
  padding: 0 30px;
}
</style>
