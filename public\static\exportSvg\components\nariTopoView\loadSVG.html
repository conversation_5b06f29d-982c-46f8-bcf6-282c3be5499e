<!--
 * @Author: 陆晓夫
 * @Date: 2022-04-29 14:04:50
 * @LastEditors: 陆晓夫
 * @LastEditTime: 2022-04-29 16:08:30
-->
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <input id="fileName" value="20220429" /><button onclick="init()">转化为json</button><br />
    <textarea id="res" style="width: 2000px; height: 1000px"></textarea>
    <script src="../../WisVisual/libs/d3.v5.min.js"></script>
    <script src="../../WisVisual/libs/jquery.min.js"></script>
    <script>
      function init() {
        let fileName = $('#fileName').val();
        d3.html(`./images/${fileName}.svg`).then((data) => {
          let svg = d3.select(data).select('svg');
          let res = {
            xfmr: {},
            lowbus: {},
            switch: {},
            highbus: {},
          };

          parseXfmr(svg, res);
          parseLowBus(svg, res);
          parseHighBus(svg, res);
          parseSwitch(svg, res);
          d3.select('#res').text(JSON.stringify(res));
          console.log(res);
        });
      }

      function parseXfmr(svg, res) {
        let xfmr1 = svg.select('#一主变');
        let cx = xfmr1.attr('cx');
        let cy = xfmr1.attr('cy');
        let r = xfmr1.attr('r');
        let xfmr1Path = `M ${cx - r} ${cy} a ${r} ${r} 0 1 0 ${2 * r} 0 a ${r} ${r} 0 1 0 -${2 * r} 0 z`;
        res.xfmr['1'] = [xfmr1Path];
        res.xfmr['2'] = parsePath(svg.select('#二主变'));
        res.xfmr['3'] = parsePath(svg.select('#三主变'));
        res.xfmr['4'] = parsePath(svg.select('#四主变'));
        res.xfmr['5'] = parsePath(svg.select('#五主变'));
        res.xfmr['6'] = parsePath(svg.select('#六主变'));
        res.xfmr['7'] = parsePath(svg.select('#七主变'));
        res.xfmr['8'] = parsePath(svg.select('#八主变'));
        res.xfmr['9'] = parsePath(svg.select('#九主变'));
        res.xfmr['10'] = parsePath(svg.select('#十主变'));
      }

      function parseLowBus(svg, res) {
        res.lowbus['0-0'] = [svg.select('#中0').attr('d')];
        res.lowbus['1-0'] = [svg.select('#中1').attr('d')];
        res.lowbus['2-0'] = parsePath(svg.select('#中2'));
        res.lowbus['3-0'] = parsePath(svg.select('#中3'));
        res.lowbus['4-0'] = parsePath(svg.select('#中4'));
        res.lowbus['6-0'] = parsePath(svg.select('#中6'));
        res.lowbus['8-0'] = parsePath(svg.select('#中8'));
        res.lowbus['1-1'] = parsePath(svg.select('#中1-1'));
        res.lowbus['2-1'] = parsePath(svg.select('#中2-1'));
        res.lowbus['2-2'] = parsePath(svg.select('#中2-2'));
        res.lowbus['3-1'] = parsePath(svg.select('#中3-1'));
        res.lowbus['3-2'] = parsePath(svg.select('#中3-2'));
        res.lowbus['3-3'] = parsePath(svg.select('#中3-3'));
        res.lowbus['4-1'] = parsePath(svg.select('#中4-1'));
        res.lowbus['4-2'] = parsePath(svg.select('#中4-2'));
        res.lowbus['4-3'] = parsePath(svg.select('#中4-3'));
        res.lowbus['4-4'] = parsePath(svg.select('#中4-4'));
        res.lowbus['6-4'] = parsePath(svg.select('#中6-4'));
        res.lowbus['6-6'] = parsePath(svg.select('#中6-6'));
        res.lowbus['8-4'] = parsePath(svg.select('#中8-4'));
        res.lowbus['8-7'] = parsePath(svg.select('#中8-7'));
        res.lowbus['8-8'] = parsePath(svg.select('#中8-8'));
      }

      function parseSwitch(svg, res) {
        res.switch['1-1'] = parseRect(svg.select('#中1-1'));
        res.switch['2-1'] = parseRect(svg.select('#中2-1'));
        res.switch['2-2'] = parseRect(svg.select('#中2-2'));
        res.switch['3-1'] = parseRect(svg.select('#中3-1'));
        res.switch['3-2'] = parseRect(svg.select('#中3-2'));
        res.switch['3-3'] = parseRect(svg.select('#中3-3'));
        res.switch['4-1'] = parseRect(svg.select('#中4-1'));
        res.switch['4-2'] = parseRect(svg.select('#中4-2'));
        res.switch['4-3'] = parseRect(svg.select('#中4-3'));
        res.switch['4-4'] = parseRect(svg.select('#中4-4'));
        res.switch['6-4'] = parseRect(svg.select('#中6-4'));
        res.switch['6-6'] = parseRect(svg.select('#中6-6'));
        res.switch['8-4'] = parseRect(svg.select('#中8-4'));
        res.switch['8-7'] = parseRect(svg.select('#中8-7'));
        res.switch['8-8'] = parseRect(svg.select('#中8-8'));
      }

      function parseHighBus(svg, res) {
        res.highbus['0'] = [svg.select('#外0').attr('d')];
        res.highbus['1'] = [svg.select('#外1').attr('d')];
        res.highbus['2'] = parsePath(svg.select('#外2'));
        res.highbus['3'] = parsePath(svg.select('#外3'));
        res.highbus['4'] = parsePath(svg.select('#外4'));
        res.highbus['5'] = parsePath(svg.select('#外5'));
        res.highbus['6'] = parsePath(svg.select('#外6'));
      }

      function parsePath(gName) {
        let res = [];
        gName
          .selectAll('path')
          .nodes()
          .forEach((d) => {
            res.push(d3.select(d).attr('d'));
          });
        return res;
      }

      function parseRect(gName) {
        let res = [];
        gName
          .selectAll('rect')
          .nodes()
          .forEach((d) => {
            let t = `${d3.select(d).attr('x')},${d3.select(d).attr('y')},${d3.select(d).attr('width')},${d3.select(d).attr('height')},${d3.select(d).attr('rx')},${d3.select(d).attr('transform')}`;
            res.push(t);
          });
        return res;
      }
    </script>
  </body>
</html>
