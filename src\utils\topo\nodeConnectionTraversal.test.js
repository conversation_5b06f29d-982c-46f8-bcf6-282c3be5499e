/**
 * Test file for nodeConnectionTraversal.js
 *
 * This file contains test cases to verify the functionality of the node connection traversal utility.
 */

import {
  findConnectedElements,
  moveConnectedElements,
} from "./nodeConnectionTraversal";

// Mock data for testing
const mockNodeLinkList = [
  // Nodes
  {
    nodeId: "node1",
    nodeType: "rect",
    type: "CustomRect",
    x: 100,
    y: 100,
    w: 50,
    h: 50,
    tx: 0,
    ty: 0,
  },
  {
    nodeId: "node2",
    nodeType: "switch",
    type: "CustomImage",
    x: 200,
    y: 100,
    w: 30,
    h: 30,
    tx: 0,
    ty: 0,
  },
  {
    nodeId: "node3",
    nodeType: "rect",
    type: "CustomRect",
    x: 300,
    y: 100,
    w: 50,
    h: 50,
    tx: 0,
    ty: 0,
  },
  {
    nodeId: "node4",
    nodeType: "rect",
    type: "CustomRect",
    x: 200,
    y: 200,
    w: 50,
    h: 50,
    tx: 0,
    ty: 0,
  },
  // Links
  {
    linkId: "link1",
    type: "CustomPath",
    fromObj: "node1",
    endObj: "node2",
    pathPoints: [
      { x: 150, y: 125 },
      { x: 200, y: 115 },
    ],
    tx: 0,
    ty: 0,
  },
  {
    linkId: "link2",
    type: "CustomPath",
    fromObj: "node2",
    endObj: "node3",
    pathPoints: [
      { x: 230, y: 115 },
      { x: 300, y: 125 },
    ],
    tx: 0,
    ty: 0,
  },
  {
    linkId: "link3",
    type: "CustomPath",
    fromObj: "node2",
    endObj: "node4",
    bindLink: "node2",
    pathPoints: [
      { x: 215, y: 130 },
      { x: 225, y: 200 },
    ],
    tx: 0,
    ty: 0,
  },
];

// Mock updatePathPoint function
const mockUpdatePathPoint = (point, { tx, ty }) => {
  return {
    ...point,
    x: point.x + tx,
    y: point.y + ty,
  };
};

// Test findConnectedElements function
console.log("Testing findConnectedElements:");
const startNode = mockNodeLinkList[0]; // node1
const connectedElements = findConnectedElements(startNode, mockNodeLinkList);
console.log(
  `Found ${connectedElements.length} connected elements starting from node1`
);
console.log(
  "Connected elements IDs:",
  connectedElements.map((el) => el.nodeId || el.linkId)
);

// Test moveConnectedElements function
console.log("\nTesting moveConnectedElements:");
const switchNode = mockNodeLinkList[1]; // node2 (switch)
const movedElements = moveConnectedElements(
  switchNode,
  mockNodeLinkList,
  10,
  20,
  mockUpdatePathPoint
);
console.log(
  `Moved ${movedElements.length} elements connected to the switch node`
);
console.log(
  "Moved elements IDs:",
  movedElements.map((el) => el.nodeId || el.linkId)
);

// Verify the positions were updated correctly
console.log("\nVerifying position updates:");
movedElements.forEach((el) => {
  if (el.nodeId) {
    console.log(`Node ${el.nodeId}: tx=${el.tx}, ty=${el.ty}`);
  } else if (el.linkId) {
    console.log(
      `Link ${el.linkId}: First point (${el.pathPoints[0].x}, ${el.pathPoints[0].y})`
    );
  }
});

// Export the test data for potential reuse
export { mockNodeLinkList, mockUpdatePathPoint };
