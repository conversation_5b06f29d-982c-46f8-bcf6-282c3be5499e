<template>
  <div class="userConfig">
    <div class="header">
      <div class="button">
        <Button
          @click="setUser()"
          class="addNewUser"
          type="primary"
          icon="ios-add"
          >新增</Button
        >
      </div>
    </div>
    <div class="body">
      <div class="userLeft">
        <Table
          :columns="columns7"
          :content="self"
          :data="tableList"
          :height="tableHeight"
          border
        >
          <template slot="status" slot-scope="{ row }">
            <!-- <Icon size="20" type="md-checkmark" v-show="row.isAvailable" style="color: green"></Icon>
            <Icon size="20" type="md-close" v-show="!row.isAvailable" style="color: red"></Icon> -->
            <i-switch
              v-model="row.isAvailable"
              size="small"
              @on-change="ifSureChangeStatus(row)"
            ></i-switch>
          </template>
          <template slot="action" slot-scope="{ row, index }">
            <Tooltip title="编辑" :disabled="true">
              <i
                class="iconfont permission-icon icon-bianjisekuai"
                @click="edit(row, index)"
              ></i>
            </Tooltip>
            <Tooltip title="权限" :disabled="true">
              <i
                class="iconfont permission-icon icon-permissions"
                @click="managePermission(row, 'permission')"
              ></i>
            </Tooltip>
            <Tooltip title="目录" :disabled="true">
              <i
                class="iconfont permission-icon icon-xueyuan-mulu"
                @click="managePermission(row, 'menu')"
              ></i>
            </Tooltip>
            <Tooltip title="成员" :disabled="true">
              <i
                class="iconfont permission-icon icon-chengyuan"
                @click="manageMember(row)"
              ></i>
            </Tooltip>
            <Tooltip title="删除" :disabled="true">
              <i
                class="iconfont permission-icon icon-shanchu"
                @click="remove(index)"
              ></i>
            </Tooltip>
          </template>
        </Table>
        <Page
          :total="tableListCopy.length"
          :current="topCurPage"
          :page-size="topCurSize"
          show-sizer
          show-total
          @on-change="changeTopPage"
          @on-page-size-change="changeTopSize"
          style="margin-top: 1%"
        ></Page>
      </div>
    </div>
    <Modal
      v-model="modal2"
      width="600"
      @on-cancel="cancel"
      title="权限"
      :mask-closable="false"
    >
      <div
        style="
          display: flex;
          flex-direction: row;
          border-top: 1px solid black;
          border-bottom: 1px solid black;
        "
      >
        <div
          style="
            display: flex;
            flex-direction: row;
            width: 200px;
            align-items: center;
            border-right: 1px solid black;
            justify-content: center;
            font-size: 20px;
          "
        >
          <b>模块</b>
        </div>
        <div
          style="
            display: flex;
            flex: 1;
            justify-content: center;
            align-items: center;
            font-size: 20px;
          "
        >
          <b>方法</b>
        </div>
      </div>
      <div
        style="
          display: flex;
          flex-direction: column;
          overflow-y: auto;
          overflow-x: hidden;
        "
      >
        <div v-for="item in permissionList" :key="item.id">
          <div
            style="
              display: flex;
              flex-direction: row;
              border-top: 1px solid black;
              border-bottom: 1px solid black;
            "
          >
            <div
              style="
                display: flex;
                flex-direction: row;
                width: 200px;
                align-items: center;
                border-right: 1px solid black;
                justify-content: flex-end;
              "
            >
              <div
                :title="item.name"
                style="
                  margin-right: 5px;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                  text-align: center;
                "
              >
                {{ item.name }}
              </div>
              <Checkbox
                v-model="item.ifCheckAllMession"
                @on-change="changeMessionAll(item)"
              ></Checkbox>
            </div>
            <div
              style="
                display: flex;
                flex-wrap: wrap;
                flex: 1;
                align-items: center;
                padding: 5px;
              "
            >
              <div v-for="item1 in item.children" :key="item1.id">
                <div
                  style="
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    width: 240px;
                    margin-right: 10px;
                  "
                >
                  <Checkbox
                    v-model="item1.ifCheckMession"
                    @on-change="changeMession(item)"
                  ></Checkbox>
                  <div
                    :title="item1.name"
                    style="
                      text-overflow: ellipsis;
                      overflow: hidden;
                      white-space: nowrap;
                      text-align: center;
                    "
                  >
                    {{ item1.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <p slot="footer">
        <Button @click="cancel" style="margin-right: 5px">取消</Button>
        <Button @click="setPermission('formValidate')" type="primary"
          >保存</Button
        >
      </p>
    </Modal>
    <Modal
      v-model="modal3"
      title="成员维护"
      :styles="{ width: '1000px' }"
      :mask-closable="false"
    >
      <div style="display: flex; flex-direction: column; height: 200px">
        <div
          style="
            display: flex;
            flex-direction: row;
            border-radius: 10px 10px 0 0;
            padding: 10px;
          "
        >
          <div
            style="
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              width: 150px;
            "
          >
            <div style="margin-right: 5px">组内成员</div>
            <Checkbox
              v-model="choseRole.ifCheckAll"
              @on-change="changeInsideMember"
            ></Checkbox>
          </div>
          <div
            style="
              display: flex;
              flex-wrap: wrap;
              flex: 1;
              overflow-y: auto;
              align-items: center;
            "
          >
            <div v-for="item in choseRole.members" :key="item.id">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  width: 140px;
                  margin-right: 10px;
                "
              >
                <Checkbox
                  v-model="item.ifCheck"
                  @on-change="changeInsideOne"
                ></Checkbox>
                <div>{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            flex-direction: row;
            border-radius: 0 0 10px 10px;
            padding: 10px;
          "
        >
          <div
            style="
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              width: 150px;
            "
          >
            <div style="margin-right: 5px">组外成员</div>
            <Checkbox
              v-model="noChoseRole.ifCheckAll1"
              @on-change="changeOutsideMember"
            ></Checkbox>
          </div>
          <div
            style="
              display: flex;
              flex-wrap: wrap;
              flex: 1;
              overflow-y: auto;
              align-items: center;
            "
          >
            <div v-for="item1 in noChoseRole.members" :key="item1.id">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  width: 140px;
                  margin-right: 10px;
                "
              >
                <Checkbox
                  v-model="item1.ifCheck1"
                  @on-change="changeOutsideOne"
                ></Checkbox>
                <div>{{ item1.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <p slot="footer">
        <Button @click="cancel" style="margin-right: 5px">取消</Button>
        <Button @click="saveMembers" type="primary">保存</Button>
      </p>
    </Modal>
    <Modal
      v-model="modal"
      width="600"
      :footer-hide="true"
      @on-cancel="cancel"
      title="角色编辑"
      :mask-closable="false"
    >
      <div v-if="modal" class="userRight">
        <Form :label-width="100" :model="user" label-position="left">
          <FormItem label="编码">
            <Input v-model="user.code" />
          </FormItem>
          <FormItem label="名称">
            <Input v-model="user.name" />
          </FormItem>
          <!-- <FormItem label="项目">
            <Select v-model="user.projectCode" multiple>
              <Option
                v-for="item in $store.state.homeStore.projectList"
                :key="item.code"
                :value="item.code"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem> -->
          <FormItem label="描述">
            <Input v-model="user.description" />
          </FormItem>
          <!--<FormItem label="是否启用" v-if="updateFlag">-->
          <!--<checkbox v-model="user.isAvailable">启用</checkbox>-->
          <!--</FormItem>-->
          <!-- <FormItem label="组内用户">
            <Transfer
                :data="userList"
                :target-keys="user.idsList"
                :render-format="render1"
                @on-change="handleChange1"></Transfer>
          </FormItem> -->
          <FormItem>
            <Button
              @click="handleSubmit('formValidate')"
              style="float: right"
              type="primary"
              >保存</Button
            >
            <Button @click="cancel" style="float: right; margin-right: 5px"
              >取消</Button
            >
          </FormItem>
        </Form>
      </div>
    </Modal>
    <Modal
      v-model="isMenuConfigVisible"
      width="600"
      @on-cancel="cancel"
      title="权限"
      :mask-closable="false"
    >
      <div v-for="(item, index) in menuList" :key="index">
        <Checkbox
          :value="menuSelectedList.length === item.children.length"
          @on-change="handleAllMenuChange"
          >{{ item.menuName }}</Checkbox
        >
        <br />
        <CheckboxGroup :value="menuSelectedList" @on-change="handleMenuChange">
          <Checkbox
            v-for="(itemChild, itemIndex) in item.children || []"
            :key="itemChild.menuId + '_' + itemIndex"
            :label="itemChild.menuId"
            >{{ itemChild.menuName }}</Checkbox
          >
        </CheckboxGroup>
      </div>
      <div slot="footer">
        <Button @click="cancel" style="margin-right: 5px">取消</Button>
        <Button @click="setPermission('formValidate')" type="primary"
          >保存</Button
        >
      </div>
    </Modal>
  </div>
</template>

<script>
import _ from "lodash";

export default {
  name: "User",
  data() {
    return {
      modal3: false,
      modal2: false,
      modal: false,
      self: this,
      lenArr: [5, 10, 20], // 每页显示长度设置
      pageLen: 5, // 可显示的分页数
      user: {
        id: 0,
        name: "", //角色名称
        description: "", //角色描述
        code: "", //角色code
        permissionIds: [], //角色拥有权限
        isAvailable: true,
        userIds: [],
        idsList: [],
        projectCode: [], //角色关联项目
      },
      permissionList: [],
      userList: [],
      deleteUser: "",
      updateFlag: false,
      addNewUserFlag: false,
      param: { ROLE: "3" }, // 传递参数
      lists: [], // 表格原始数据
      tableList: [], // 分页组件传回的分页后数据
      tableListCopy: [],
      selectedData: "",
      tableHeight: 0,
      columns7: [
        {
          type: "index",
          title: "序号",
          align: "center",
          width: 100,
        },
        {
          title: "名称",
          key: "name",
          align: "center",
          width: 150,
        },
        {
          title: "描述",
          key: "description",
          align: "center",
          width: 250,
        },
        {
          title: "成员列表",
          key: "members",
          align: "center",
        },
        {
          title: "启用",
          key: "isAvailableTrans",
          align: "center",
          slot: "status",
          width: 100,
        },
        {
          title: "操作",
          slot: "action",
          width: 280,
          align: "center",
        },
      ],
      // changeStatusModal: false,
      permissionInfo: {},
      topCurPage: 1,
      topCurSize: 20,
      choseRole: {},
      noChoseRole: {},
      menuList: [],
      isMenuConfigVisible: false,
      menuSelectedList: [],
    };
  },
  async created() {
    let windowHeight = window.screen.height;
    this.tableHeight = 0.7 * windowHeight;
    await this.getUserList();
    await this.getPermissionList();
    await this.fetchMenuList();
  },
  methods: {
    // 获取图层列表
    fetchMenuList() {
      this.$get(`/topoEdit/getMenuList`).then(({ data }) => {
        if (data.code == "0000") {
          this.menuList = data.data;
        }
      });
    },
    handleAllMenuChange(val) {
      if (val && this.menuList.length) {
        this.menuSelectedList = this.menuList[0].children.map(
          (item) => item.menuId
        );
      } else {
        this.menuSelectedList = [];
      }
    },
    handleMenuChange(val) {
      this.menuSelectedList = val;
    },
    render1(item) {
      return item.label;
    },
    handleChange1(newTargetKeys, direction, moveKeys) {
      this.user.idsList = newTargetKeys;
    },

    // 添加用户
    setUser() {
      this.modal = true;
      this.modal2 = false;
      this.addNewUserFlag = true;
      this.updateFlag = false;
      this.user = {
        id: "",
        name: "admin",
        description: "admin",
        code: "123456",
        userIds: [],
        idsList: [],
        projectCode: [],
      };
    },
    //删除
    remove(index) {
      this.deleteUser = this.tableList[index].id;
      this.modal2 = false;
      this.modal = false;
      this.$Modal.confirm({
        title: "确定要删除该角色?",
        onOk: () => {
          this.deleteThis();
        },
        onCancel: () => {
          this.$Message.info("取消删除");
        },
      });
    },
    changeTopPage(data) {
      this.topCurPage = data;
      this.getdata();
    },
    changeTopSize(data) {
      this.topCurSize = data;
      this.getdata();
    },
    //取消
    cancel() {
      this.modal = false;
      this.modal2 = false;
      this.modal3 = false;
      this.isMenuConfigVisible = false;
      this.menuSelectedList = [];
    },
    changeOutsideMember() {
      if (this.noChoseRole.ifCheckAll1) {
        if (this.noChoseRole.members.length != 0) {
          let arr = _.cloneDeep(this.noChoseRole.members);
          // this.noChoseRole.members = []
          for (let i = 0; i < arr.length; i++) {
            this.noChoseRole.members.splice(i, 1, {
              ifCheck1: true,
              name: arr[i].name,
              id: arr[i].id,
            });
          }
        } else {
          this.noChoseRole.members = [];
        }
        this.noChoseRole.ifCheckAll1 = true;
      } else {
        if (this.noChoseRole.members.length != 0) {
          let arr = _.cloneDeep(this.noChoseRole.members);
          // this.noChoseRole.members = []
          for (let j = 0; j < arr.length; j++) {
            this.noChoseRole.members.splice(j, 1, {
              ifCheck1: false,
              name: arr[j].name,
              id: arr[j].id,
            });
          }
        } else {
          this.noChoseRole.members = [];
        }
        this.noChoseRole.ifCheckAll1 = false;
      }
      this.$forceUpdate();
    },
    changeMessionAll(data) {
      if (data.children && data.children.length !== 0) {
        let index = 0;
        for (let h = 0; h < this.permissionList.length; h++) {
          if (this.permissionList[h].id == data.id) {
            index = h;
          }
        }
        for (let j = 0; j < data.children.length; j++) {
          let temp = _.cloneDeep(data.children[j]);
          temp.ifCheckMession = data.ifCheckAllMession;
          this.permissionList[index].children.splice(j, 1, temp);
        }
      }
    },
    changeMession(mession) {
      let num = 0;
      let index = 0;
      for (let h = 0; h < this.permissionList.length; h++) {
        if (this.permissionList[h].id == mession.id) {
          index = h;
        }
      }
      for (let i = 0; i < mession.children.length; i++) {
        if (mession.children[i].ifCheckMession) {
          num++;
        }
        if (i == mession.children.length - 1) {
          if (num == mession.children.length) {
            this.permissionList[index].ifCheckAllMession = true;
          } else {
            this.permissionList[index].ifCheckAllMession = false;
          }
        }
        this.$forceUpdate();
      }
    },
    changeInsideOne() {
      let num = 0;
      for (let i = 0; i < this.choseRole.members.length; i++) {
        if (this.choseRole.members[i].ifCheck) {
          num++;
        }
        if (i == this.choseRole.members.length - 1) {
          if (num != this.choseRole.members.length) {
            this.choseRole.ifCheckAll = false;
          } else {
            this.choseRole.ifCheckAll = true;
          }
        }
      }
    },
    changeOutsideOne() {
      let num = 0;
      for (let i = 0; i < this.noChoseRole.members.length; i++) {
        if (this.noChoseRole.members[i].ifCheck1) {
          num++;
        }
        if (i == this.noChoseRole.members.length - 1) {
          if (num != this.noChoseRole.members.length) {
            this.noChoseRole.ifCheckAll1 = false;
          } else {
            this.noChoseRole.ifCheckAll1 = true;
          }
          this.$forceUpdate();
        }
      }
    },
    changeInsideMember() {
      if (this.choseRole.ifCheckAll) {
        if (this.choseRole.members.length != 0) {
          let arr = _.cloneDeep(this.choseRole.members);
          this.choseRole.members = [];
          for (let i = 0; i < arr.length; i++) {
            this.choseRole.members.push({
              ifCheck: true,
              name: arr[i].name,
              id: arr[i].id,
            });
          }
        } else {
          this.choseRole.members = [];
        }
        this.choseRole.ifCheckAll = true;
      } else {
        if (this.choseRole.members.length != 0) {
          let arr = _.cloneDeep(this.choseRole.members);
          this.choseRole.members = [];
          for (let j = 0; j < arr.length; j++) {
            this.choseRole.members.push({
              ifCheck: false,
              name: arr[j].name,
              id: arr[j].id,
            });
          }
        } else {
          this.choseRole.members = [];
        }
        this.choseRole.ifCheckAll = false;
      }
    },
    //管理权限
    managePermission(data, type) {
      this.modal = false;
      let that = this;
      this.user.id = data.id;
      this.$get(`/systemManage/getPermissionIdsByRoleId`, {
        roleId: data.id,
      }).then(
        (data) => {
          if (data.data.code === "0000") {
            let { permissionIds, menuIds } = data.data.data;
            this.menuSelectedList = menuIds || [];
            for (let i = 0; i < that.permissionList.length; i++) {
              // that.permissionList[i].indeterminate = false;
              if (permissionIds.indexOf(that.permissionList[i].id) !== -1) {
                that.permissionList[i].ifCheckAllMession = true;
              } else {
                that.permissionList[i].ifCheckAllMession = false;
              }
              if (that.permissionList[i].children) {
                let num1 = 0;
                let num2 = 0;
                for (
                  let j = 0;
                  j < that.permissionList[i].children.length;
                  j++
                ) {
                  if (
                    permissionIds.indexOf(
                      that.permissionList[i].children[j].id
                    ) !== -1
                  ) {
                    that.permissionList[i].children[j].ifCheckMession = true;
                    num1++;
                  } else {
                    that.permissionList[i].children[j].ifCheckMession = false;
                    num2++;
                  }
                }
                if (num1 === that.permissionList[i].children.length) {
                  that.permissionList[i].ifCheckAllMession = true;
                } else {
                  that.permissionList[i].ifCheckAllMession = false;
                }
                // if(num2 === that.permissionList[i].children.length) {
                //   that.permissionList[i].ifCheckAllMession = false;
                // }
                // if(num1 < that.permissionList[i].children.length &&
                //    num2 < that.permissionList[i].children.length) {
                //   // that.permissionList[i].indeterminate = true;
                // }
              }
            }
          } else {
            that.$Message.error("获取该角色失败！");
          }
          if (type === "permission") {
            that.modal2 = true;
          } else {
            this.isMenuConfigVisible = true;
          }
        },
        () => {
          that.$Message.error("获取该角色失败！");
        }
      );
    },
    manageMember(data) {
      this.modal3 = true;
      this.choseRole = _.cloneDeep(data);
      this.noChoseRole.members = [];
      this.noChoseRole.ifCheckAll1 = false;
      this.choseRole.members = [];
      this.choseRole.ifCheckAll = false;
      if (this.choseRole.userIds.length > 0) {
        for (let a = 0; a < this.userList.length; a++) {
          for (let a1 = 0; a1 < this.choseRole.userIds.length; a1++) {
            if (this.choseRole.userIds[a1] == this.userList[a].id) {
              this.choseRole.members.push({
                ifCheck: true,
                name: this.userList[a].displayName,
                id: this.userList[a].id,
              });
              this.choseRole.ifCheckAll = true;
              break;
            }
            if (a1 == this.choseRole.userIds.length - 1) {
              this.noChoseRole.members.push({
                ifCheck1: false,
                name: this.userList[a].displayName,
                id: this.userList[a].id,
              });
            }
          }
        }
      } else {
        for (let b = 0; b < this.userList.length; b++) {
          this.noChoseRole.members.push({
            ifCheck1: false,
            name: this.userList[b].displayName,
            id: this.userList[b].id,
          });
        }
      }
    },
    setPermission() {
      let permissionIds = [];
      for (let i = 0; i < this.permissionList.length; i++) {
        if (this.permissionList[i].ifCheckAllMession) {
          permissionIds.push(this.permissionList[i].id);
        }
        let num = 0;
        if (this.permissionList[i].children) {
          for (let j = 0; j < this.permissionList[i].children.length; j++) {
            if (this.permissionList[i].children[j].ifCheckMession) {
              permissionIds.push(this.permissionList[i].children[j].id);
              num++;
            }
          }
          if (num > 0) {
            permissionIds.push(this.permissionList[i].id);
          }
        }
      }
      let totalData = {
        roleId: this.user.id,
        permissionIds,
        menuIds: this.menuSelectedList,
      };
      this.$post(`/systemManage/setPermissions`, totalData).then(
        (data) => {
          if (data.code === "0000") {
            this.$Message.success("更新权限成功！");
          } else {
            this.$Message.error("更新权限失败！");
          }
        },
        () => {
          this.$Message.error("更新权限失败！");
        }
      );
      this.modal2 = false;
      this.isMenuConfigVisible = false;
    },
    /**
     *删除用户信息
     */
    deleteThis() {
      let deleteData = {
        id: this.deleteUser,
      };
      this.$post(`/systemManage/deleteRole`, deleteData).then(
        (data) => {
          if (data.code === "0000") {
            this.$Message.success("删除成功！");
            this.deleteUser = "";
            this.getdata();
          } else {
            this.$Message.error("删除失败！");
          }
        },
        () => {
          this.$Message.error("删除失败！");
        }
      );
    },
    saveMembers() {
      let idsTemp = [];
      let project = "";

      if (this.choseRole && this.choseRole.members.length) {
        for (let i = 0; i < this.choseRole.members.length; i++) {
          if (this.choseRole.members[i].ifCheck) {
            idsTemp.push(this.choseRole.members[i].id);
          }
        }
        if (this.choseRole.projectCode && this.choseRole.projectCode.length) {
          for (let j = 0; j < this.choseRole.projectCode.length; j++) {
            project += this.user.projectCode[j] + ",";
          }
        }
      }

      if (this.noChoseRole && this.noChoseRole.members.length) {
        const res = this.noChoseRole.members
          .filter((ele) => ele.ifCheck1)
          .map((ele) => ele.id);
        idsTemp.push(...res);
      }

      if (project[project.length - 1] === ",") {
        project = project.substring(0, project.length - 1);
      }
      let total = {
        id: this.choseRole.id,
        name: this.choseRole.name,
        code: this.choseRole.code,
        description: this.choseRole.description,
        isAvailable: this.user.isAvailable,
        userIds: idsTemp,
        projects: project,
      };
      this.$post(`/systemManage/updateRole`, total).then((data) => {
        if (data.code === "0000") {
          this.$Message.success("修改成功！");
          this.modal3 = false;
          this.getdata();
        } else {
          this.$Message.error(`${data.errorInfo}`);
        }
      });
    },
    /**
     *提交用户信息
     */
    handleSubmit() {
      //匹配用户名正则
      let reg1 = /^[a-z0-9A-Z_-]{3,20}$/;
      let that = this;
      if (
        this.addNewUserFlag &&
        (this.user.name === "" ||
          this.user.code === "" ||
          this.user.projectCode === [])
      ) {
        this.$Message.warning("请确认表单已填写完成");
        return;
      }
      if (this.updateFlag && this.user.name === "") {
        if (!reg1.test(this.user.name)) {
          this.$Message.warning("用户名输入格式有误");
          return false;
        }
      }
      if (this.updateFlag && this.user.projectCode === []) {
        this.$Message.warning("该用户尚未关联任何项目！");
        return;
      }

      let idsTemp = [];
      for (let i = 0; i < this.user.idsList.length; i++) {
        idsTemp.push(this.user.idsList[i]);
      }
      let project = "";
      for (let j = 0; j < this.user.projectCode.length; j++) {
        project += this.user.projectCode[j] + ",";
      }
      if (project[project.length - 1] === ",") {
        project = project.substring(0, project.length - 1);
      }
      let total = {
        id: this.user.id,
        name: this.user.name,
        code: this.user.code,
        description: this.user.description,
        userIds: idsTemp,
        projects: project,
      };
      if (this.updateFlag) {
        //        total.isAvailable = this.user.isAvailable;
        this.$post(`/systemManage/updateRole`, total).then((data) => {
          if (data.code === "0000") {
            this.$Message.success("修改成功！");
            this.getdata();
          } else {
            this.$Message.error(`${data.errorInfo}`);
          }
        });
      } else if (this.addNewUserFlag) {
        this.$post(`/systemManage/insertRole`, total).then((data) => {
          if (data.code === "0000") {
            this.$Message.success("新增成功！");
            this.getdata();
          } else {
            this.$Message.error("新增失败！");
          }
        });
      }
      this.modal = false;
    },
    /**
     *获取用户列表信息
     */
    getdata() {
      let that = this;
      this.$get(`/systemManage/getRoleList`).then((data) => {
        if (data.data.code === "0000") {
          this.tableList = [];
          this.tableListCopy = [];
          data.data.data.forEach((item) => {
            // if (item.STATUS == -1) {
            // 	return
            // }
            let members = "";
            if (item.userIds.length !== 0) {
              for (let i = 0; i < item.userIds.length; i++) {
                for (let j = 0; j < that.userList.length; j++) {
                  if (item.userIds[i] == that.userList[j].id) {
                    members += that.userList[j].displayName + ",";
                  }
                }
              }
              members = members.substring(0, members.length - 1);
            }
            that.tableListCopy.push({
              id: item.id,
              description: item.description,
              code: item.code,
              name: item.name,
              userIds: item.userIds,
              members: members,
              isAvailableTrans: item.isAvailable,
              isAvailable: item.isAvailable,
              createdBy: item.createdBy,
              createdTime: item.createdTime,
              updatedBy: item.updatedBy,
              updatedTime: item.updatedTime,
            });
          });
          if (that.tableListCopy.length > that.topCurSize) {
            for (
              let i = (that.topCurPage - 1) * that.topCurSize;
              i < that.topCurPage * that.topCurSize;
              i++
            ) {
              if (i < that.tableListCopy.length) {
                that.tableList.push(that.tableListCopy[i]);
              }
            }
          } else {
            that.tableList = _.cloneDeep(that.tableListCopy);
          }
        } else {
          that.$Message.error(data.errorInfo);
        }
      });
    },
    //获取用户列表
    getUserList() {
      this.$get(`/systemManage/getUserList`).then((data) => {
        if (data.data.code === "0000") {
          this.userList = [];
          data.data.data.forEach((item) => {
            if (item.userState !== 1) {
              this.userList.push({
                id: item.id,
                displayName: item.displayName,
                show: true,
                key: item.id,
                label: item.displayName,
                disabled: false,
              });
            }
          });
          this.getdata();
        } else {
          this.$Message.error(data.errorInfo);
        }
      });
    },
    //筛选用户
    selectUser(data, type) {
      if (type === "in") {
        this.user.idsList.splice(
          this.user.idsList.findIndex((item) => item.id === data.id),
          1
        );
        this.userList[
          this.userList.findIndex((item) => item.id === data.id)
        ].show = true;
      } else {
        this.userList[
          this.userList.findIndex((item) => item.id === data.id)
        ].show = false;
        this.user.idsList.push(data);
      }
    },
    //获取权限列表
    getPermissionList() {
      this.$get(`/systemManage/getPermissionList`).then((data) => {
        if (data.data.code === "0000") {
          let permissionListTemp = data.data.data;
          for (let i = 0; i < permissionListTemp.length; i++) {
            permissionListTemp[i].chosen = false;
            permissionListTemp[i].expand = false;
            if (permissionListTemp[i].children) {
              for (let j = 0; j < permissionListTemp[i].children.length; j++) {
                permissionListTemp[i].children[j].chosen = false;
              }
            }
          }
          this.permissionList = permissionListTemp;
        } else {
          this.$Message.error("获取权限列表失败！");
        }
      });
    },
    //展开权限菜单
    expandPermission(index) {
      let curExpandState = this.permissionList[index].expand;
      this.$set(this.permissionList[index], "expand", !curExpandState);
    },
    //权限选择
    permissionSelect(position, findex, index) {
      if (position === "root") {
        let childrenAfter = this.permissionList[index].children;
        this.permissionList[index].indeterminate = false;
        if (childrenAfter !== undefined) {
          for (let i = 0; i < childrenAfter.length; i++) {
            childrenAfter[i].chosen = !this.permissionList[index].chosen;
          }
          this.$set(this.permissionList[index], "children", childrenAfter);
        }
      }
      if (position === "child") {
        let rootSelect = true;
        this.permissionList[findex].children[index].chosen =
          !this.permissionList[findex].children[index].chosen;
        if (!this.permissionList[findex].children[index].chosen) {
          rootSelect = false;
        } else {
          let children = this.permissionList[findex].children;
          for (let i = 0; i < children.length; i++) {
            if (!children[i].chosen) {
              rootSelect = false;
            }
          }
        }
        let num1 = 0;
        let num2 = 0;
        let arr = this.permissionList[findex].children;
        for (let j = 0; j < arr.length; j++) {
          arr[j].chosen ? ++num1 : ++num2;
          if (j === arr.length - 1) {
            if (num1 === arr.length) {
              this.permissionList[findex].chosen = true;
              this.permissionList[findex].indeterminate = false;
            }
            if (num2 === arr.length) {
              this.permissionList[findex].chosen = false;
              this.permissionList[findex].indeterminate = false;
            }
            if (num2 < arr.length && num1 < arr.length) {
              this.permissionList[findex].indeterminate = true;
            }
          }
        }
        this.$set(this.permissionList[findex], "chosen", rootSelect);
      }
    },
    edit(data, index) {
      this.modal = true;
      this.modal2 = false;
      this.user.description = data.description;
      this.user.name = data.name;
      this.user.code = data.code;
      this.user.id = data.id;
      this.user.idsList = [];
      if (data.projectCode !== undefined) {
        this.user.projectCode = data.projectCode;
      } else {
        this.user.projectCode = [];
      }
      for (let i = 0; i < this.userList.length; i++) {
        if (data.userIds.indexOf(this.userList[i].id) !== -1) {
          this.user.idsList.push(
            //     {
            //   id: this.userList[i].id,
            //   displayName: this.userList[i].displayName
            // }
            this.userList[i].id
          );
          // this.userList[i].show = false;
        } else {
          // this.userList[i].show = true;
        }
      }
      //this.user.userIds = data.userIds
      this.user.isAvailable = data.isAvailable;
      this.addNewUserFlag = false;
      this.updateFlag = true;
    },
    //改变权限确认
    ifSureChangeStatus(row) {
      // this.changeStatusModal = true
      this.permissionInfo = row;
      this.$Modal.confirm({
        title: "是否修改权限?",
        onOk: () => {
          this.changePermissionStatus();
        },
        onCancel: () => {
          this.$Message.info("取消修改");
          this.getdata();
        },
      });
    },
    //改变权限启用状态
    changePermissionStatus() {
      let idsTemp = [];
      for (let i = 0; i < this.user.idsList.length; i++) {
        idsTemp.push(this.user.idsList[i]);
      }
      let total = {
        id: this.permissionInfo.id,
        name: this.permissionInfo.name,
        code: this.permissionInfo.code,
        description: this.permissionInfo.description,
        isAvailable: this.permissionInfo.isAvailable,
        userIds: idsTemp,
      };
      this.$post(`/systemManage/updateRole`, total).then((data) => {
        if (data.code === "0000") {
          this.$Message.success("修改成功！");
          this.getdata();
        } else {
          this.$Message.error(`${data.errorInfo}`);
        }
      });
      // this.changeStatusModal = false
    },
    //取消改变权限
    cancelChangePermissionStatus() {
      // this.changeStatusModal = false
      this.getdata();
    },
    // 高亮图标
    highLight(id) {
      document.getElementById(id).style.opacity = 0.6;
    },
    // 还原
    backOrigi(id) {
      document.getElementById(id).style.opacity = 1;
    },
  },
};
</script>

<style lang="scss">
.userConfig {
  height: 96%;
  margin: 1% 2% 0 2%;
  .header {
    //height: 5%;
    .button {
      text-align: left;
    }
  }
  .body {
    margin-top: 1%;
    height: 90%;
    width: 100%;
    display: flex;
    flex-direction: row;

    .userLeft {
      width: 100%;
    }
  }

  .ivu-modal-body {
    .permisionPart {
      display: flex;
      flex-direction: column;

      .permisionPart2 {
        display: flex;
        flex-direction: row;

        .permisionChildren {
          display: flex;
          flex-direction: column;

          .permisionChildren2 {
            display: flex;
            flex-direction: row;
          }
        }
      }
    }
  }
}
.userRight {
  .ivu-form-item-content {
    font-size: 14px;
    line-height: 20px;
  }
  .ivu-list-small .ivu-list-item {
    padding: 4px 0;
  }
}
.permission-icon {
  font-size: 20px;
  margin-right: 10px;
  color: #70c0e8;
  object-fit: contain;
  cursor: pointer;
  &:hover {
    opacity: 0.6;
  }
}
.ivu-table-overflowX {
  overflow-x: hidden;
}
</style>
