<template>
  <Modal
    v-model="isVisible"
    :title="isAddStatus ? '添加对象' : '修改对象'"
    @on-ok="ok"
    @on-cancel="hide"
  >
    <Form :model="objectForm" label-position="right" :label-width="100">
      <FormItem label="类型编码">
        <Input v-model="objectForm.objType"></Input>
      </FormItem>
      <FormItem label="对象名称">
        <Input v-model="objectForm.objName"></Input>
      </FormItem>
      <FormItem label="所属分组">
        <Select v-model="objectForm.groupId">
          <Option
            v-for="item in groupList"
            :value="item.value"
            :key="item.value"
            >{{ item.label }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="关联组件">
        <Select v-model="objectForm.compClass" clearable>
          <Option
            v-for="(item, index) in compontentsList"
            :value="item.value"
            :key="item.label + index"
            >{{ item.label }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="对象图标">
        <Row>
          <input
            id="file"
            type="file"
            ref="file"
            @change="handleBeforeUpload"
            accept="image/gif,image/jpeg,image/jpg,image/png,image/svg+xml"
          />
        </Row>
      </FormItem>
      <FormItem label="图标比例">
        <Input v-model="objectForm.imgScale"></Input>
      </FormItem>
    </Form>
  </Modal>
</template>

<script>
export default {
  name: "AddCompositionModal",
  props: {
    groupList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isVisible: false,
      objectForm: {
        objType: "", //类型编码
        objName: "", //对象名称
        groupId: "", //分组id
        compClass: "", //关联组件
        objImg: "", //对象图标
        imgScale: "", //图标比例
      },
      isAddStatus: true,
      imgParam: {},
      compontentsList: [],
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      this.$get("/componentManager/getAllSysComponentList").then((res) => {
        if (res.data.code === "0000") {
          this.compontentsList = res.data.data.map(
            ({ shortName, className }) => ({
              label: shortName,
              value: className,
            })
          );
        }
      });
    },
    show(val) {
      if (val) {
        this.isAddStatus = false;
        const { objType, objName, groupId, compClass, objImg, imgScale } = val;
        this.objectForm = {
          objType,
          objName,
          groupId,
          compClass,
          objImg,
          imgScale,
        };
      } else {
        this.isAddStatus = true;
      }
      this.isVisible = true;
    },
    hide() {
      this.isVisible = false;
      this.objectForm = {
        objType: "", //类型编码
        objName: "", //对象名称
        groupId: "", //分组id
        compClass: "", //关联组件
        objImg: "", //对象图标
        imgScale: "", //图标比例
      };
    },
    ok() {
      const url = this.isAddStatus ? "insertObj" : "updateObj";
      const textSuccess = this.isAddStatus ? "添加成功" : "修改成功";
      this.objectForm.compClass = this.objectForm.compClass || "";
      this.$post(`/topoEdit/${url}`, this.objectForm).then((data) => {
        if (data.code == "0000") {
          this.hide();
          this.$Message.success(textSuccess);
          this.$emit("addSuccess");
        }
      });
    },
    handleBeforeUpload() {
      const file = this.$refs.file.files[0];
      this.imgParam = new FormData();
      this.imgParam.append("file", file);
      console.log(file.type);
      const isImg =
        file.type == "image/jpg" ||
        file.type == "image/jpeg" ||
        file.type == "image/png" ||
        file.type == "image/svg+xml";
      if (!isImg) {
        this.$Message.warning("请选择图片上传");
        return;
      }
      //判断图片的文件大小
      // if (file.size / 1024 < 2) {
      //   this.$Message.warning("图片应小于200K");
      //   return;
      // }
      var fileReader = new FileReader();
      fileReader.readAsDataURL(file);
      fileReader.onload = (e) => {
        var imgData = e.target.result; //获取图片的文件流
        //通过Image 对象去加载图片
        var image = new Image();
        image.src = imgData;
        image.onload = () => {
          // width = image.width;
          // height = image.height;
          // let scrale = width / height;
          // if (scrale <= 2 && scrale >= 1.8) {
          //   this.handleSuccess(file);
          // } else {
          //   this.$Message.warning("图片的宽高比应该在1.8-2之间");
          //   return;
          // }
          this.handleSuccess(file);
        };
      };
      return true;
    },
    handleSuccess() {
      let config = "headers: {Content-Type: multipart/form-data}";
      this.$post("/topoEdit/uploadIcon", this.imgParam, config).then((res) => {
        if (res.code === "0000") {
          this.objectForm.objImg = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
