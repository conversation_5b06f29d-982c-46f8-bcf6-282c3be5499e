<template>
  <Modal
    v-model="isVisible"
    title="图层"
    @on-ok="handleSubmit"
    @on-cancel="hide"
    :mask-closable="false"
  >
    <Input
      search
      enter-button
      placeholder="搜索"
      class="mb-5"
      @on-search="handleSearch"
    />
    <List style="height: 324px; overflow: auto">
      <ListItem
        v-for="item in mapOptions"
        :key="item.value"
        style="margin-left: 5px"
      >
        <Checkbox
          :value="item.isSelected"
          @on-change="handleMapSelect($event, item)"
          >{{ item.label }}</Checkbox
        >
      </ListItem>
    </List>
  </Modal>
</template>

<script>
import _ from "lodash";
import { mapState } from "vuex";

export default {
  props: {
    bindMap: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      isVisible: false,
      index: 1,
      bindMapList: [],
      searchValue: "",
    };
  },
  watch: {
    bindMap(val) {
      this.initData(val);
    },
  },
  computed: {
    ...mapState("topoStore", {
      mapList: "mapList",
    }),
    mapOptions() {
      const list = this.searchValue
        ? this.mapList.filter((ele) => ele.mapName.includes(this.searchValue))
        : this.mapList;
      return list.map((ele) => {
        return {
          value: ele.mapId,
          label: ele.mapName,
          isSelected: this.bindMapList.includes(ele.mapId),
        };
      });
    },
  },
  methods: {
    initData(val) {
      if (!val) return;
      this.bindMapList = Array.isArray(val.mapId)
        ? _.cloneDeep(val.mapId)
        : [val.mapId];
    },
    show() {
      this.isVisible = true;
    },
    hide() {
      this.isVisible = false;
      this.initData(this.bindMap);
    },
    handleSubmit() {
      this.$emit("on-select", _.cloneDeep(this.bindMapList));
    },
    selectALl() {
      this.bindMapList = this.mapList.map((ele) => ele.mapId);
    },
    handleMapSelect(val, { value }) {
      if (val) {
        if (this.bindMapList.includes(value)) return;
        this.bindMapList.push(value);
      } else {
        this.bindMapList = this.bindMapList.filter((ele) => ele !== value);
      }
    },
    handleSearch(val) {
      this.searchValue = val;
    },
  },
};
</script>

<style lang="scss" scoped></style>
