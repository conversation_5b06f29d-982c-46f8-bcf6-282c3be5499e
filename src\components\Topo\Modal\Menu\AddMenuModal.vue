<template>
  <Modal v-model="isVisible" title="添加目录" @on-ok="ok" @on-cancel="hide">
    <Form :model="menuForm" label-position="right" :label-width="100">
      <FormItem label="目录名称">
        <Input v-model="menuForm.menuName"></Input>
      </FormItem>
      <FormItem label="父目录ID">
        <Cascader
          :data="menuData"
          v-model="menuForm.menuList"
          change-on-select
        ></Cascader>
      </FormItem>
    </Form>
  </Modal>
</template>

<script>
export default {
  name: "AddMenuModal",
  props: {
    menuData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isVisible: false,
      menuForm: {
        menuName: "",
        menuParId: "",
        menuList: [],
      },
      isEdit: false,
      menuId: "",
    };
  },
  methods: {
    show(menuInfo) {
      this.isVisible = true;
      if (menuInfo) {
        this.isEdit = true;
        const { title, key, menuParId } = menuInfo;
        this.menuId = key;
        this.menuForm = {
          menuName: title,
          menuList: [menuParId],
        };
      }
    },
    hide() {
      this.isVisible = false;
      this.isEdit = false;
      this.menuForm = {
        menuName: "",
        menuParId: "",
      };
    },
    ok() {
      const { menuName, menuList } = this.menuForm;
      const params = {
        menuName,
        menuParId: menuList.pop() || 0,
      };
      if (this.isEdit) {
        // 修改
        params.menuId = this.menuId;
        this.$post(`/topoEdit/updateMenu`, params).then((data) => {
          if (data.code == "0000") {
            this.hide();
            this.$Message.success("修改成功");
            this.$emit("updateSuccess", params);
          }
        });
      } else {
        this.$post(`/topoEdit/insertMenu`, params).then((data) => {
          if (data.code == "0000") {
            this.hide();
            this.$Message.success("添加成功");
            this.$emit("addSuccess");
          }
        });
        this.$emit("addSuccess");
      }
    },
    cancel() {
      this.$Message.info("Clicked cancel");
    },
  },
};
</script>

<style lang="scss" scoped></style>
