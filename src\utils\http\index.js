import axios from "axios";
import config from "./config";
import Cookies from "js-cookie";
// import { message } from "@/utils/resetMessage";
import { Message } from "view-design";
import router from "@/router";

const statusDic = {
  "0100": "系统错误",
  "0108": "ID已被占用",
  "0109": "ID不存在",
  "0000": "操作成功",
  "0106": "名称已被占用",
  "0110": "名称不存在",
  "0105": "需要登录",
  "0103": "用户无权限",
  "0111": "参数格式错误",
  "0120": "场景锁获取失败",
  "0121": "场景锁释放",
  "0102": "用户被禁用",
  "0101": "用户名或密码错误",
  "0107": "用户名已被占用",
  "0113": "版本号已存在",
  "0112": "版本号不存在",
  "0114": "已被占用",
  "0116": "缺少参数",
  "0117": " scene不存在",
  "0118": "浏览器标识重复",
  "0122": "无效命令",
  "0123": "场景名称被占用",
  "0124": " 场景code被占用",
  "0125": "指标名称被占用",
  "0126": "数据源名称被占用",
  "0127": " json格式错误",
  "0129": "模式不存在",
  400: "请求错误",
  401: "未授权，请登录",
  403: "拒绝访问",
  404: "请求地址出错",
  408: "请求超时",
  500: "服务器内部错误",
  501: "服务未实现",
  502: "网关错误",
  503: "服务不可用",
  504: "网关超时",
  505: "HTTP版本不受支持",
  //数据服务
  "0130": "测试失败",
  "0131": "SQL执行错误",
  "0132": "系统变量获取失败",
  "0133": "参数含有非法脚本字符",
  "0134": "上传失败",
  "0135": "指标被使用",
};
axios.defaults.headers.post["Content-Type"] =
  "application/x-www-form-urlencoded";

const instance = axios.create({
  baseURL: config.baseURL,
  headers: config.headers,
});
// request 拦截器
instance.interceptors.request.use(
  (config) => {
    let token = Cookies.get("token");
    // 1. 请求开始的时候可以结合 vuex 开启全屏 loading 动画
    // console.log(store.state.loading)
    // console.log('准备发送请求...')
    // 2. 带上token
    if (token) {
      config.headers.Authorization = token;
    } else {
      Cookies.set("token", "");
      // 重定向到登录页面
      router.push("/login");
    }
    return config;
  },

  (error) => {
    // 请求错误时
    console.log("request:", error);
    // 1. 判断请求超时
    if (
      error.code === "ECONNABORTED" &&
      error.message.indexOf("timeout") !== -1
    ) {
      console.log("timeout请求超时");
      // return service.request(originalRequest);// 再重复请求一次
    }
    // 2. 需要重定向到错误页面
    const errorInfo = error.response;
    console.log(errorInfo);
    if (errorInfo) {
      error = errorInfo.data; // 页面那边catch的时候就能拿到详细的错误信息,看最下边的Promise.reject
      const errorStatus = errorInfo.status; // 404 403 500 ...
      router.push({
        path: `/error/${errorStatus}`,
      });
    }
    return Promise.reject(error); // 在调用的那边可以拿到(catch)你想返回的错误信息
  }
);

// response 拦截器
instance.interceptors.response.use(
  (response) => {
    let data;
    if (response.headers["content-type"] === "application/octet-stream") {
      // 二进制数据
      return response;
    }
    // IE9时response.data是undefined，因此需要使用response.request.responseText(Stringify后的字符串)
    if (response.data === undefined) {
      data = JSON.parse(response.request.responseText);
    } else {
      data = response;
    }

    // 根据返回的code值来做不同的处理
    switch (data.data.code) {
      case "0000":
        break;
      case "0103":
        router.replace("/401");
        return;
      case "0105":
      case 440:
      case 302:
        Message.error("token过期，请重新登录");
        Cookies.set("token", "");
        router.replace({ path: "/login" });
        return;
      case "0120":
        Message.error(`${data.data.data}正在编辑此场景`);
        return;
      case "0121":
        Message.error(statusDic[data.data.code]);
        router.replace({ path: "/topo-edit" });
        return;
      // break;
      // console.log('登录成功')
      default:
        if (statusDic[data.data.code]) {
          Message.error(statusDic[data.data.code]);
        } else {
          Message.error(data.data.message);
        }

        return;
    }
    // 若不是正确的返回code，且已经登录，就抛出错误
    // const err = new Error(data.desc)
    // err.data = data
    // err.response = response
    // throw err

    return data;
    // 若不是正确的返回code，且已经登录，就抛出错误
    // const err = new Error(data.desc)
    // err.data = data
    // err.response = response
    // throw err
  },
  (err) => {
    if (err && err.response) {
      err.message = statusDic[err.response.status];
      Message.error(err.message);
    }
    console.error(err);
    return Promise.reject(err); // 返回接口返回的错误信息
  }
);

/**
 * get方法，对应get请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 *
 */

export function get(url, param = "") {
  return instance.get(url, { params: param });
}

// return new Promise((resolve, reject) => {
//     axios.get(url,
//         params,
//     ).then(res => {
//         resolve(res.data)
//     }).catch(err => {
//         reject(err.data)
//     })
// })

/**
 * post方法，对应post请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */

export function post(url, params, config) {
  // let config = {headers: {'Content-Type': 'application/x-www-form-urlencoded'}};
  // return axios.get(url, qs.stringify(params), config)
  return new Promise((resolve, reject) => {
    instance
      .post(url, params, config)
      .then((res) => {
        if (res != undefined && res.data != undefined) {
          resolve(res.data);
        } else {
          reject(res && res.message);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
}
