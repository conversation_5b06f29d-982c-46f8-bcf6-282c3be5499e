import * as d3 from "d3";

export const formatId = (id) => {
  return id.split("#")[0];
};

/**
 * 将不同格式的颜色转换为#RRGGBBAA格式
 */
export const formatColor = (color) => {
  const d3Color = d3.color(color);

  if (!d3Color) {
    return "#000000";
  }
  // 提取 RGBA 组件
  const { r, g, b, opacity: a } = d3Color;

  // 将组件转换为十六进制
  const rHex = r.toString(16).padStart(2, "0").toUpperCase();
  const gHex = g.toString(16).padStart(2, "0").toUpperCase();
  const bHex = b.toString(16).padStart(2, "0").toUpperCase();

  // 如果透明度为 1，则只返回 #RRGGBB
  if (a === 1) {
    return `#${rHex}${gHex}${bHex}`;
  }

  // 否则返回 #RRGGBBAA
  const aHex = Math.round(a * 255)
    .toString(16)
    .padStart(2, "0")
    .toUpperCase();
  return `#${rHex}${gHex}${bHex}${aHex}`;
};

// 定义绿色范围的色相范围
const greenHueMin = 60;
const greenHueMax = 180;

// 判断颜色是否在绿色范围内的函数
export function isColorInGreenRange(color) {
  // 使用 D3.js 解析颜色为 HSL 格式
  const hslColor = d3.hsl(color);
  const hue = hslColor.h;

  // 判断色相是否在绿色范围内
  return hue >= greenHueMin && hue <= greenHueMax;
}
