<template>
  <Modal
    v-model="isVisible"
    title="导出设置"
    :width="1000"
    :mask-closable="false"
  >
    <div
      style="margin-bottom: 10px; display: flex; justify-content: space-between"
    >
      <div>
        <Checkbox v-model="isIncludeBg" @on-change="handleBgChange"
          >背景</Checkbox
        >
        <Checkbox v-model="isIncludeBlock" @on-change="handleBlockChange"
          >色块</Checkbox
        >
        <Checkbox v-model="isIncludeLegend" @on-change="handleLegendChange"
          >图例</Checkbox
        >
        <Checkbox v-model="isIncludeText" @on-change="handleTextChange"
          >潮流值</Checkbox
        >
        <Checkbox
          v-model="isIncludeDescription"
          @on-change="handleDescriptionChange"
          >注释</Checkbox
        >
        <!-- <Select
          style="width: 100px; margin: 0 10px"
          size="small"
          v-model="svgPostion"
          @on-change="svgPostionChange"
        >
          <Option
            v-for="item in svgPostionOptions"
            :value="item.value"
            :key="item.value"
            >{{ item.label }}</Option
          >
        </Select> -->

        <Select
          style="width: 100px; margin: 0 10px"
          size="small"
          v-model="direction"
          @on-change="changeDirection"
        >
          <Option
            v-for="item in directionOptions"
            :value="item.value"
            :key="item.value"
            >{{ item.label }}</Option
          >
        </Select>
        <Checkbox v-model="isPlanWinter" @on-change="handlePlanWinterChange"
          >计划图例(冬)</Checkbox
        >
        <Checkbox v-model="isPlanSummer" @on-change="handlePlanSummerChange"
          >计划图例(夏)</Checkbox
        >
      </div>
      <div>
        <DatePicker
          :value="dateValue"
          size="small"
          type="month"
          placeholder="Select month"
          style="width: 200px"
          @on-change="handleDateChange"
        ></DatePicker>
      </div>
    </div>
    <div id="svgPreviewLayout"></div>

    <div slot="footer"></div>
    <Modal
      v-model="isExportPDFVisible"
      title="导出PDF"
      :width="500"
      :mask-closable="false"
    >
      <div style="height: 100px">
        <RadioGroup v-model="pdfType">
          <Radio label="单个"></Radio>
          <Radio
            v-for="(val, key, index) in pdfExortData"
            :key="index"
            :label="key"
          ></Radio>
        </RadioGroup>
        <div style="margin-top: 30px">
          <Input
            v-if="pdfType === '单个' && this.isIncludeLegend"
            v-model="pdfNum"
            maxlength="4"
            placeholder="请输入编号，例如：000"
          ></Input>
          <p v-else>花费时间较长，将以zip格式的压缩包下载，请耐心等待！</p>
        </div>
      </div>
      <div slot="footer">
        <Button type="primary" @click="exportFile('pdf')">确定</Button>
        <Button type="text" @click="isExportPDFVisible = false">取消</Button>
      </div>
    </Modal>
    <div slot="footer">
      <Button type="primary" @click="previewImage" class="mr-5"
        >全屏预览</Button
      >
      <Button type="primary" v-print="'#svgPreview'" class="mr-5">打印</Button>
      <Button type="primary" class="mr-5" @click="exportFile('image')"
        >导出图片</Button
      >
      <Button type="primary" @click="handleExportPdfClick">导出PDF</Button>
    </div>
  </Modal>
</template>

<script>
import print from "vue-print-nb";
import $ from "jquery";
import axios from "axios";
import { saveAs } from "file-saver";

import {
  drawSvgPrintData,
  clipSvg,
  convertImgToBase64,
  drawSvgDescription,
  getNextMonth,
} from "@/utils/drawSvg";

import { mapState, mapMutations } from "vuex";
const areasHeight = 3300;
const riverHieght = 143;

export default {
  name: "ExportSvgConfigModal",
  directives: {
    print,
  },
  data() {
    return {
      isVisible: false,
      svgInfo: {},
      svgHtml: null,
      directionOptions: [
        {
          value: "horizontal",
          label: "横向",
        },
        {
          value: "vertical",
          label: "纵向",
        },
      ],
      direction: "horizontal",
      isIncludeBg: false,
      svgSize: {
        width: 0,
        height: 0,
      },
      svgPostion: "all",
      svgPostionOptions: [
        {
          value: "all",
          label: "全部",
        },
        {
          value: "north",
          label: "江北",
        },
        {
          value: "south",
          label: "江南",
        },
      ],
      svgVerticalScale: 1,
      isIncludeBlock: false,
      isIncludeLegend: false,
      isIncludeText: false,
      isIncludeDescription: false,
      isExportPDFVisible: false,
      isPlanWinter: false,
      isPlanSummer: false,
      pdfType: "单个",
      pdfNum: "",
      heigthBackup: 0,
      pdfExortData: {},
      dateValue: "",
    };
  },
  mounted() {
    this.initEvent();
  },
  computed: {
    ...mapState("topoStore", {
      sublayerList: "sublayerList",
      sublayerSelectedList: "sublayerSelectedList",
      mapInfo: "mapInfo",
    }),
  },
  watch: {
    //  Modal 的on-cancel不触发，所以需要监听isVisible的变化
    isVisible(val) {
      !val && this.hide();
    },
  },
  methods: {
    ...mapMutations("topoStore", [
      "setSublayerSelectedList",
      "setSpinShow",
      "setSpinContent",
    ]),
    initEvent() {
      this.$bus.on("onMapSlected", (val) => {
        this.svgInfo = val;
      });
    },
    show() {
      this.isVisible = true;
      const [nextYear, nextMonth] = getNextMonth();
      this.dateValue = `${nextYear}-${nextMonth}`;
      this.$nextTick(() => {
        this.generateSvg();
        this.handleBgChange(this.isIncludeBg);
      });
    },
    hide() {
      this.isVisible = false;
      // 数据量太大，直接赋值，不使用变量
      $("#svgPreview").attr("src", "");
      $("#svgPreview").css("display", "none");
      this.direction = "horizontal";
      this.isIncludeBg = false;
      this.isIncludeBlock = false;
      this.isIncludeLegend = false;
      $("#svgPreviewLayout").empty();
    },
    generateSvg() {
      const { mapId } = this.svgInfo;
      if (!mapId) return this.$Message.warning("未选择图层");
      let svgEl = $("#topoEdit").clone(true);
      const comp = $(svgEl).find(".topo-component-list");

      // 浏览器限制canvas大小，只能按比例缩小
      // https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/canvas
      let scale = 1;

      $(svgEl).css({
        background: "#ffffff",
        // height: areasHeight * 2 + riverHieght,
        transform: "",
      });

      let w = $(svgEl).width();
      let h = $(svgEl).height();

      this.heigthBackup = h;
      this.svgSize = {
        width: w,
        height: h,
      };

      $(svgEl).attr({
        id: "printSvg",
        viewBox: null,
      });
      $(svgEl)
        .find(".topo-component-list")
        .css({
          transform: `scale(${scale})`,
          "transform-origin": "top left",
          "font-family": '"楷体","楷体_GB2312"',
          "font-weight": "bold",
        });
      $(svgEl).empty().append(comp);
      $(svgEl).find(".topo-link-arrow").remove();
      $(svgEl).find(".custom-move-rect").remove();
      $(svgEl).find(".custom-path-move").remove();

      //   $(svgEl).find(".custom-text-border").css("display", "none");

      const texts = $(svgEl).find("text");
      const _this = this;
      texts.each(function () {
        const color = $(this).css("fill");

        const fillColorMap = {
          "rgb(255, 255, 255)": "rgb(0, 0, 0)",
          "rgb(255, 191, 0)": "rgb(0, 0, 0)",
          "rgb(255, 255, 254)": !_this.isIncludeBg ? "rgb(0, 0, 0)" : color,
          "rgb(0, 255, 0)": "green",
        };

        const newColor = fillColorMap[color];
        if (newColor) {
          if (newColor === "green") {
            $(this).attr("data-color", "green").css("display", "none");
          } else {
            $(this).css("fill", newColor);
          }
        }
      });

      const blocks = $(svgEl).find(".topo-line-block");
      blocks.each(function () {
        const fill = $(this).css("fill");
        $(this).css({
          "stroke-dasharray": "10 10",
          fill: "transparent",
        });
        $(this).attr({
          "data-fill": fill,
        });
      });

      $("#svgPreviewLayout").append(svgEl[0]);
      $(svgEl).attr("id", "svgPreview");

      this.handleBgChange(false);
      this.svgPostionChange(this.svgPostion);
    },
    // 选择江南或者江北
    svgPostionChange(val) {
      const svg = $("#svgPreview");
      let { width, height } = this.svgSize;

      let moveY = 0;

      if (val === "all") {
        height = this.heigthBackup;
      } else if (val === "south") {
        moveY = areasHeight + riverHieght;
        height = areasHeight;
      } else if (val === "north") {
        height = areasHeight;
      }

      svg.find(".topo-component-list").css({
        transform: `translate(0, -${moveY}px)`,
      });

      svg.css({ height: height });

      this.svgSize.height = height;

      clipSvg(width, height, val, moveY);

      // 切换的时候，如果有图例 则绘制
      if (this.isIncludeLegend) {
        this.handleLegendChange(this.isIncludeLegend);
      }
      if (this.isIncludeDescription) {
        this.handleDescriptionChange(this.isIncludeDescription);
      }
    },
    handleDateChange(val) {
      this.dateValue = val;
      this.isIncludeLegend && this.handleLegendChange(this.isIncludeLegend);
    },
    // 增加图例
    handleLegendChange(val) {
      drawSvgPrintData(val, this.svgSize, this.svgPostion, {
        date: this.dateValue,
      });

      this.changeDirection(this.direction);

      if (this.isIncludeDescription) {
        this.handleDescriptionChange(this.isIncludeDescription);
      }
    },
    changeDirection(val) {
      const height = $("#svgPreview").height();

      $("#svgPreview").css({
        transform:
          val === "horizontal"
            ? ""
            : `rotate(90deg) translate(0px, ${-height}px)`,
        "transform-origin": "0 0",
      });
    },
    // 色块颜色导出显示
    handleBlockChange(val) {
      //   $("#svgPreview")
      //     .find(".custom-text-border")
      //     .css("display", val ? "block" : "none");

      const fillColorMap = {
        "rgb(76, 104, 131)": "rgb(215,255,255)", // 蓝
        "rgb(90, 74, 120)": "rgb(235,233,255)", // 紫
        "rgb(118, 89, 100)": "rgb(255,233,240)", // 粉
        "rgb(116, 111, 103)": "rgb(255,229,198)", // 橙
        "rgb(73, 119, 115)": "rgb(235,255,238)", // 绿
      };

      const blocks = $("#svgPreview").find(".topo-line-block");

      blocks.each(function () {
        let fillBackup = $(this).attr("data-fill");

        let fillColor = fillColorMap[fillBackup]
          ? fillColorMap[fillBackup]
          : fillBackup;

        fillColor = val ? fillColor : "#ffffff";

        $(this).css({
          "stroke-dasharray": "10 10",
          fill: fillColor,
        });
      });
    },
    // 背景色切换
    handleBgChange(val) {
      $("#svgPreview").css({
        background: val ? "rgb(39, 39, 39)" : "#ffffff",
      });
      $("#svgPreviewLayout").css({
        background: val ? "rgb(39, 39, 39)" : "#ffffff",
      });
      const texts = $("#svgPreview").find("text");
      let textColor = "rgb(255, 255, 255)";

      if (val) {
        textColor = "rgb(255, 255, 255)";
      } else {
        textColor = "rgb(0, 0, 0)";
      }

      //   $("#svgPreview").find(".custom-text-border").attr("stroke", textColor);

      texts.each(function () {
        const color = $(this).css("fill");
        if (val) {
          color === "rgb(0, 0, 0)" && $(this).css("fill", textColor);
        } else {
          color === "rgb(255, 255, 255)" && $(this).css("fill", textColor);
        }
      });
    },
    handleTextChange(val) {
      $("#svgPreview")
        .find("text")
        .each(function () {
          if ($(this).attr("data-color") === "green") {
            $(this).css("display", val ? "block" : "none");
          }
        });
    },
    //计划图例 冬
    handlePlanWinterChange(val) {
      this.isPlanSummer = false;
      drawSvgPrintData(true, this.svgSize, this.svgPostion, {
        date: this.dateValue,
        isPlan: val,
        isPlanSummber: this.isPlanSummer,
      });
    },
    //计划图例 夏
    handlePlanSummerChange(val) {
      this.isPlanWinter = false;
      drawSvgPrintData(true, this.svgSize, this.svgPostion, {
        date: this.dateValue,
        isPlan: val,
        isPlanSummber: this.isPlanSummer,
      });
    },
    handleDescriptionChange(val) {
      const { description = {} } = this.mapInfo;
      const { width } = this.svgSize;

      drawSvgDescription(
        val,
        this.isIncludeLegend,
        this.svgSize,
        this.svgPostion === "all"
          ? `${description.north}${description.south}`
          : description[this.svgPostion]
      );
    },
    handleExportPdfClick() {
      this.isExportPDFVisible = true;
      this.generateExportPdfConfig();
    },
    generateExportPdfConfig() {
      const { pathname } = window.location;

      axios
        .get(`${pathname}static/printConfig.json`)
        .then(({ data }) => {
          this.pdfExortData = data;
        })
        .catch((err) => {
          console.error(err);
        });
    },
    async exportFile(typeExport) {
      this.isExportPDFVisible = false;
      this.setSpinShow(true);
      this.setSpinContent("开始导出...");
      this.$Loading.start();

      let svg = $("#svgPreview").clone(true);

      svg.attr("id", "svgExport").css({
        width: svg.width() + 228,
        height: svg.height() + 154,
        transform: "translate(114px,77px)",
      });

      let width = svg.width();
      let height = svg.height();
      const map = new Map();
      const imageList = svg.find(".custom-image");
      const legendList = svg.find("#meta-group image");
      const list = [...imageList, ...legendList];

      for (let i = 0; i < list.length; i++) {
        const el = list[i];
        const url = $(el).attr("href") || $(el).attr("data-url");

        if (!url) continue;
        let imgBase64 = "";
        if (map.has(url)) {
          imgBase64 = map.get(url);
        } else {
          try {
            imgBase64 = await convertImgToBase64(url);
            map.set(url, imgBase64);
          } catch (error) {
            console.error("图片获取失败", url, error);
          }
        }
        if ($(el).attr("href")) {
          $(el).attr("href", imgBase64);
        } else {
          $(el).css("background-image", `url(${imgBase64})`);
        }
      }
      map.clear();

      this.covertSVG2Image(svg[0], width, height, typeExport);
    },
    // 将svg转换成image
    covertSVG2Image(node, width, height, typeExport) {
      let serializer = new XMLSerializer();
      let source =
        '<?xml version="1.0" standalone="no"?>\r\n' +
        serializer.serializeToString(node);

      let image = new Image();
      const src =
        "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);
      image.src = src;

      let canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;
      let context = canvas.getContext("2d");
      context.scale(1, 1);

      this.setSpinContent("正在加载数据...");

      //   图片加载完成
      image.onload = () => {
        context.drawImage(image, 0, 0);
        this.$Loading.finish();

        if (typeExport === "image") {
          const { mapName } = this.mapInfo;
          let a = document.createElement("a");
          a.download = `${mapName}.png`;
          a.href = canvas.toDataURL("image/png");
          a.click();
          this.setSpinShow(false);
        } else {
          this.preExportPdf(canvas, context, width, height);
        }
      };
    },
    // 准备导出pdf
    preExportPdf(canvas, context, width, height) {
      //  1123PX 是A4纸的高 对应打印的A3纸的高
      const PDF_HEIGHT = 1123;
      const PDF_WIDTH = parseInt(width / (height / PDF_HEIGHT));

      const fileNameMap = {
        all: this.svgInfo.mapName || "电网",
        north: "JB",
        south: "JN",
      };

      const fileName = fileNameMap[this.svgPostion];

      const list =
        this.pdfType === "单个"
          ? [this.pdfNum]
          : this.pdfExortData[this.pdfType];

      this.exportPdf(
        canvas,
        context,
        PDF_WIDTH,
        PDF_HEIGHT,
        height,
        fileName,
        list
      );
    },
    // 导出PDF
    async exportPdf(
      canvas,
      context,
      PDF_WIDTH,
      PDF_HEIGHT,
      height,
      fileName,
      pdfNumList = []
    ) {
      this.$Loading.start();
      //   将名称列表排个序，防止相同名称的在不同位置，方便count自增
      pdfNumList = pdfNumList.sort((a, b) => {
        return a - b;
      });

      const textlMarginLeft = this.svgPostion === "all" ? 1374 : 1284;
      const formData = new FormData();

      const getBlob = () => {
        return new Promise((resolve) => {
          canvas.toBlob(function (blob) {
            resolve(blob);
          });
        });
      };
      //   批量生成imageData

      let pdfNum = "";
      let count = 0;
      for (let i = 0; i < pdfNumList.length; i++) {
        const ele = pdfNumList[i];

        if (pdfNum.slice(0, 3) === ele) {
          count++;
          pdfNum = `${ele}-${count}`;
        } else {
          count = 0;
          pdfNum = ele;
        }
        this.setSpinContent(`正在生成编号为${ele}的数据...`);
        this.$Loading.update(i);
        // 没有勾选图例 则不显示编号
        if (this.isIncludeLegend) {
          // 先清空编号部分
          context.clearRect(textlMarginLeft + 20, height - 144, 170, 60);

          // 清空部分会变成透明，用白底填充
          context.beginPath();
          context.fillStyle = "#ffffff";
          context.fillRect(textlMarginLeft + 19, height - 146, 174, 64);

          // 绘制编号文字
          context.beginPath();
          context.font = "bold 48px 'Microsoft YaHei'";
          context.fillStyle = "#000000";
          ele && context.fillText(`（${ele}）`, textlMarginLeft, height - 98);
        }

        const blob = await getBlob(canvas);
        formData.append("files", blob, `${fileName}-${pdfNum}.pdf`);
      }

      this.$Loading.finish();

      formData.append("size", `${PDF_WIDTH},${PDF_HEIGHT}`);

      this.downloadPDF(formData);
    },
    async downloadPDF(formData) {
      const zipName = "pdf.zip";
      let config = {
        headers: "{Content-Type: multipart/form-data}",
        responseType: "blob",
      };

      this.$Loading.start();
      this.setSpinContent("PDF生成中，请耐心等待...");

      this.$post("/file/downloadPdfPressFile", formData, config)
        .then((res) => {
          let blob = new Blob([res], { type: "application/zip" });
          //   拿到数据流进行下载
          saveAs(blob, zipName);
        })
        .finally(() => {
          this.setSpinShow(false);
          this.setSpinContent();
          this.$Loading.finish();
        });
    },
    // 预览SVG
    previewImage() {
      const el = document.querySelector("#svgPreviewLayout");
      el.requestFullscreen();
    },
  },
};
</script>

<style lang="scss">
#svgPreviewLayout {
  width: 100%;
  height: 450px;
  // display: flex;
  // justify-content: center;
  overflow: auto;
}
.editor-content-view {
  line-height: normal;
}
.editor-content-view ol {
  padding-left: 20px;
}
@media print {
  #svgPreview {
    -webkit-print-color-adjust: exact !important;
  }
}
.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.notice-spin-icon-layout {
  text-align: center;
}
</style>
