<template>
  <Modal
    :value="isMapAttributeVisible"
    draggable
    scrollable
    :closable="false"
    :mask="false"
    :footer-hide="true"
    width="380"
    :z-index="6"
    :styles="{ marginRight: '0', top: '60px' }"
    class-name="base-modal"
  >
    <div slot="header" style="text-align: center">属性</div>
    <div class="topo-right-view">
      <!-- <Tabs value="name1" style="height: 100%">
      <TabPane
        label="外观"
        name="name1"
        style="padding: 5px; text-align: right; height: 100%;font-size:12px"
      > -->
      <NodeMultAttribute
        v-show="nodeSelectedList.length > 1"
        :nodeSelectedList="nodeSelectedList"
      ></NodeMultAttribute>
      <div v-show="nodeSelectedList.length === 1">
        <LinkAttribute :compontentsList="compontentsList"></LinkAttribute>
        <NodeAttribute :compontentsList="compontentsList"></NodeAttribute>
      </div>
      <MapAttribute
        v-show="!nodeSelectedList.length"
        :sublayerList="sublayerList"
        @onDelete="getSublayerList"
      ></MapAttribute>
    </div>
  </Modal>
</template>
<script>
import { mapState, mapMutations } from "vuex";
import LinkAttribute from "./LinkAttribute.vue";
import MapAttribute from "./MapAttribute.vue";
import TopoData from "./Modal/CustomStylesModal.vue";
import NodeAttribute from "./NodeAttribute.vue";
import NodeMultAttribute from "./NodeMultAttribute.vue";
export default {
  components: {
    TopoData,
    LinkAttribute,
    NodeAttribute,
    NodeMultAttribute,
    MapAttribute,
  },
  data() {
    return {
      nodeSelectedList: [],
      compontentsList: {
        externalBindList: [],
        internalBindList: [],
      },
      mapInfo: {},
      sublayerList: [],
    };
  },
  mounted() {
    this.initEvent();
  },
  beforeDestroy() {
    this.$bus.off("onMapSlected");
    this.$bus.off("insertTopoSublayerSuccess");
    this.$bus.off("onGetSublayerList");
    this.$bus.off("onNodeLinkSelected");

    this.setSvgBgData({
      path: "",
      isOpen: false,
    });
  },
  computed: {
    ...mapState("topoStore", {
      isShowImageLine: "isShowImageLine",
      isGridBgShow: "isGridBgShow",
      is220kvShow: "is220kvShow",
      textColorList: "textColorList",
      isDrawBlock: "isDrawBlock",
      compScale: "compScale",
      colorSelected: "colorSelected",
      sublayerSelectedList: "sublayerSelectedList",
      versionSelected: "versionSelected",
    }),
    isMapAttributeVisible() {
      return !!this.mapInfo.mapId;
    },
  },
  methods: {
    ...mapMutations("topoStore", [
      "setSvgBgData",
      "setShowImageLine",
      "setGridBgShow",
      "set220kvShow",
      "setTextColorList",
      "setDrawBlock",
      "setCompScale",
      "setSublayerList",
      "setColorSelected",
      "setSublayerSelectedList",
    ]),
    initEvent() {
      this.$bus.on("onNodeLinkSelected", (val) => {
        this.nodeSelectedList = val;
      });
      this.$bus.on("insertTopoSublayerSuccess", this.getSublayerList);
      this.$bus.on("onMapSlected", this.handleMapSlected);
      // isRefresh 是否刷新画布内容
      this.$bus.on("onGetSublayerList", (isRefresh) => {
        this.getSublayerList().then(() => {
          isRefresh && this.sortSublayerSelect();
        });
      });
    },
    handleMapSlected(mapItem) {
      const { externalBind, internalBind, mapId } = mapItem;
      const externalBindList = [];
      const internalBindList = [];
      this.mapInfo = mapItem;
      for (const key in externalBind) {
        externalBindList.push({
          value: key,
          label: key,
        });
      }
      for (const key in internalBind) {
        internalBindList.push({
          value: key,
          label: key,
        });
      }
      this.compontentsList = {
        externalBindList,
        internalBindList,
      };
      this.setCompScale(1);
      this.getSublayerList();
    },
    sortSublayerSelect() {
      let list = [];
      const sublayerList = [...this.sublayerList];
      // 按照排序显示
      // listOrder越小的 放在最上面
      if (this.sublayerSelectedList.length) {
        sublayerList
          .sort((a, b) => a.listOrder - b.listOrder)
          .forEach((ele) => {
            if (this.sublayerSelectedList.includes(ele.sublayerId)) {
              list.push(ele.sublayerId);
            }
          });
        if (this.sublayerSelectedList.includes("other")) {
          !list.includes("other") && list.push("other");
        }
      }

      this.setSublayerSelectedList(list);
    },
    getSublayerList() {
      return new Promise((resolve, reject) => {
        this.$get(`/topoEdit/getExistSublayerList`, {
          mapId: this.mapInfo.mapId,
          versionId: this.versionSelected.versionId,
        }).then(({ data }) => {
          if (data.code == "0000") {
            const list = data.data.sort((a, b) => a.listOrder - b.listOrder);
            this.sublayerList = list;
            this.setSublayerList(list);
            resolve();
          }
        });
      });
    },
  },
};
</script>
<style lang="scss">
.topo-right-view {
  height: 75vh;
  overflow: auto;
  .ivu-divider-horizontal {
    margin: 12px 0;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .pr-5 {
    padding-right: 5px;
  }
  .pl-5 {
    padding-left: 5px;
  }
  .mr-5 {
    margin-right: 5px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mt-5 {
    margin-top: 5px;
  }
  .mb-5 {
    margin-bottom: 5px;
  }
  .click {
    cursor: pointer;
  }
  .ivu-collapse-header {
    border-left: none !important;
    border-right: none !important;
    border-radius: 0;
  }
}
.topo-node-property {
  text-align: left;
  padding: 0 10px;
  height: 100%;
  overflow-y: auto;
}
.topo-node-property-item {
  margin-bottom: 5px;
}
.topo-btn-active {
  color: #fff;
  background-color: $primaryColor;
  border-color: $primaryColor;
}
.topo-node-style {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin: 0 0 10px 32px;
  font-family: "Gill Sans", "Gill Sans MT", Calibri, "Trebuchet MS", sans-serif;
}
.topo-attribute-layout {
  height: calc(100% - 38px);
  width: 100%;
  overflow: auto;
}
.topo-attribute-icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}
.topo-attribute-uoload {
  a {
    position: relative;
    width: 80px;
    height: 28px;
    color: #fff;
    background-color: $primaryColor;
    line-height: 28px;
    text-align: center;
    text-decoration: none;
    text-indent: 0;
    font-size: 14px;
    padding: 5px 10px;
  }
  input {
    position: absolute;
    width: 80px;
    height: 28px;
    top: 0;
    left: 0;
    bottom: 0;
    opacity: 0;
  }
}
.topo-text-select {
  user-select: text;
}
</style>
