<template>
  <div>
    <Tabs value="name1">
      <TabPane label="图层" name="name1">
        <MapAttribute :sublayerList="sublayerList"></MapAttribute
      ></TabPane>
      <TabPane label="热区" name="name2">
        <ul class="hotspot-layout">
          <li
            v-for="item in hotspotList"
            :key="item.hotspotId"
            class="hotspot-item"
          >
            <div
              :class="{
                'hotspot-item-active':
                  hotspotSelected.hotspotId === item.hotspotId,
              }"
              @click="handleHotspotItemClick(item)"
              class="hotspot-item-name"
            >
              {{ item.hotspotName }}
            </div>
            <div>
              <Button
                type="primary"
                size="small"
                @click.stop="handleHotspotEdit(item)"
                style="margin-right: 5px"
              >
                编辑
              </Button>
              <Button
                type="primary"
                size="small"
                @click.stop="handleHotspotDelete(item)"
              >
                删除
              </Button>
            </div>
          </li>
        </ul>
        <Divider />
        <div class="hotspot-btn-group">
          <Button type="primary" size="small" @click="handleAdd"> 新增 </Button>

          <Button type="primary" size="small" @click="handleCancelSelect">
            取消
          </Button>
          <Button type="primary" size="small" @click="handleSaveHotspot">
            保存
          </Button>
        </div>
      </TabPane>
      <TabPane label="筛选" name="name3">
        <MultNodeModifyConfig></MultNodeModifyConfig>
      </TabPane>
      <TabPane label="隐藏图层文字" name="name4">
        <div style="padding: 10px 20px">
          <div class="mb-10">
            <Checkbox
              :value="isHideTextBySublayer"
              @on-change="setHideTextBySublayer"
              >是否跟据图层隐藏</Checkbox
            >
          </div>

          <CheckboxGroup
            :value="textHideBySublayerList"
            @on-change="handleHideTextChange"
          >
            <Checkbox label="other">其他</Checkbox>
            <Checkbox
              v-for="item in sublayerList"
              :key="item.sublayerId"
              :label="item.sublayerId"
              >{{ item.sublayerName }}</Checkbox
            >
          </CheckboxGroup>
        </div>
      </TabPane>
    </Tabs>

    <Modal v-model="isHotspotVisible" title="热点区域">
      <Form :model="formMap" label-position="right" :label-width="100">
        <FormItem label="名称">
          <Input v-model="formMap.hotspotName"></Input>
        </FormItem>
        <FormItem label="状态">
          <Input v-model="formMap.status"></Input>
        </FormItem>
        <FormItem label="顺序">
          <Input v-model="formMap.listOrder"></Input>
        </FormItem>
        <FormItem label="尺寸">
          <Row>
            <Col span="11">
              <Input v-model="formMap.width" placeholder="宽"></Input
            ></Col>
            <Col span="11" offset="2">
              <Input v-model="formMap.height" placeholder="高"></Input>
            </Col>
          </Row>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="closeEditHotspotModal">取消</Button>
        <Button type="primary" @click="handleSubmmitHotspot">确定</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import SublayerEditModal from "./Modal/SublayerEditModal.vue";
import CheckModelModal from "./Modal/CheckModelModal.vue";
import MultNodeModifyConfig from "./MultNodeModifyConfig.vue";
import MapAttribute from "./Attribute/Map/index.vue";
import _ from "lodash";
import { generatePathD } from "./utils";

export default {
  props: {
    sublayerList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    SublayerEditModal,
    MultNodeModifyConfig,
    CheckModelModal,
    MapAttribute,
  },
  data() {
    return {
      svgBgData: {
        path: "",
        isOpen: false,
        opacity: 0.6,
      },
      isSelectLink: false,
      isSelectNode: false,
      sublayerId: "",
      colors: ["#00ff2a", "#00a8ff", "#EBFF00", "#00FDFF"],
      activeIndex: 1,
      isHotspotVisible: false,
      hotspotSelected: {},
      hotspotList: [],
      formMap: {
        hotspotName: "",
        status: 1,
        listOrder: 1,
        width: 0,
        height: 0,
      },
      svgVerticalScale: 1,
      svgVerticalBackupScale: 1,
      sublayerSelectedList: [],
      mapInfo: {},
      nodeLinkList: [],
      versionId: "",
      isVisible: false,
      directionByScale: "vertical",
      isWriteVisible: false,
      typeByWriteGFile: "3",
      isWriteMapVisible: false,
      typeByWriteMapFile: "1",
      gFileName: "",
      viewBoxScale: 1,
    };
  },
  mounted() {
    this.initEventBus();
  },
  beforeDestroy() {
    this.$bus.off("onMapSlected");
    this.$bus.off("onNodeLinkSublayerListChange");
    this.$bus.off("updateNodeSelectedList");

    this.setSvgBgData({
      path: "",
      isOpen: false,
    });
    this.resetForm();
  },
  watch: {},
  computed: {
    ...mapState("topoStore", {
      isShowImageLine: "isShowImageLine",
      isGridBgShow: "isGridBgShow",
      is220kvShow: "is220kvShow",
      isPlanDividerShow: "isPlanDividerShow",
      textColorList: "textColorList",
      isDrawBlock: "isDrawBlock",
      compScale: "compScale",
      colorSelected: "colorSelected",
      hotspotDragInfo: "hotspotDragInfo",
      versionSelected: "versionSelected",
      textHideBySublayerList: "textHideBySublayerList",
      isHideTextBySublayer: "isHideTextBySublayer",
      mapInfoByScale: "mapInfo",
    }),
  },
  methods: {
    ...mapMutations("topoStore", [
      "setSvgBgData",
      "setShowImageLine",
      "setGridBgShow",
      "set220kvShow",
      "setPlanDividerShow",
      "setTextColorList",
      "setDrawBlock",
      "setCompScale",
      "setSublayerList",
      "setColorSelected",
      "setHotspot",
      "setSublayerSelectedList",
      "setSpinShow",
      "setTextHideBySublayerList",
      "setHideTextBySublayer",
      "setHotspotSize",
      "setViewBoxScale",
    ]),
    ...mapActions("topoStore", ["checkTopoRelationshipByMapId"]),
    initEventBus() {
      this.$bus.on("onMapSlected", (mapInfo) => {
        this.mapInfo = mapInfo;
        this.resetForm();
        this.hotspotSelected = {};
        this.viewBoxScale = 1;
        this.setHotspot(false);
        this.sublayerSelectedList = [];
        this.getHotspotList();
      });
      this.$bus.on("onNodeLinkSublayerListChange", (list) => {
        this.nodeLinkList = list;
      });
    },
    getHotspotList() {
      this.$get(`/topoEdit/getHotspotsList`, {
        mapId: this.mapInfo.mapId,
        versionId: this.versionSelected.versionId,
      }).then(({ data }) => {
        if (data.code === "0000") {
          this.hotspotList = data.data;
        }
      });
    },
    handleAdd() {
      // this.$bus.emit("addHotspot");
      this.hotspotSelected = {};
      this.isHotspotVisible = true;
    },
    handleHotspotEdit(val) {
      let width = 0;
      let height = 0;
      if (val.size) {
        const size = val.size.split("*");
        width = size[0];
        height = size[1];
      }
      this.formMap = { ...val, width, height };
      this.isHotspotVisible = true;
    },
    handleChangeColorClick(val) {
      this.linkStyles.fill = val;
      this.updateLinkInfo();
    },
    handleHotspotDelete(val) {
      this.$Modal.confirm({
        title: "警告",
        content: "是否要删除该热点区域",
        onOk: () => {
          this.$post(`/topoEdit/deleteHotspotsById`, {
            hotspotId: val.hotspotId,
          }).then(() => {
            this.$Message.success("删除热点区域成功");
            this.handleCancelSelect();
            this.getHotspotList();
          });
        },
        onCancel: () => {},
      });
    },
    handleSaveHotspot() {
      if (!this.hotspotSelected.hotspotName) {
        return this.$Message.warning("名称不能为空");
      }
      if (this.hotspotSelected.hotspotId) {
        const params = {
          ...this.hotspotSelected,
          ...this.hotspotDragInfo,
        };
        if (this.isHotspotVisible) {
          params.size = `${this.formMap.width}*${this.formMap.height}`;
          this.hotspotSelected.size = params.size;
          this.$bus.emit("hotspotItemClick", params);
        }
        // 更新
        this.$post(`/topoEdit/updateHotspots`, params).then(() => {
          this.$Message.success("修改热点区域成功");
          this.closeEditHotspotModal();
          this.getHotspotList();
        });
      } else {
        // 新增
        const params = {
          ...this.hotspotSelected,
          mapId: this.mapInfo.mapId,
          position: "0,0",
          scale: 3.33,
          status: 1,
        };
        this.$post(`/topoEdit/insertHotspots`, params).then(() => {
          this.$Message.success("新增热点区域成功");
          this.closeEditHotspotModal();
          this.getHotspotList();
        });
      }
    },
    // 列表点击
    handleHotspotItemClick(val) {
      if (val.hotspotId === this.hotspotSelected.hotspotId) {
        this.handleCancelSelect();
        return;
      }
      let width = 0;
      let height = 0;
      if (val.size) {
        let [w, h] = val.size.split("*");
        width = +w;
        height = +h;
      }
      this.setHotspotSize({
        width,
        height,
      });

      this.hotspotSelected = val;
      this.$bus.emit("hotspotItemClick", val);
      this.setHotspot(true);
    },
    handleClearClick() {
      this.$bus.emit("onClearSvg");
    },
    handleCancelSelect() {
      this.setHotspot(false);
      this.hotspotSelected = {};
    },
    handleSubmmitHotspot() {
      this.hotspotSelected = {
        ...this.formMap,
        listOrder: +this.formMap.listOrder,
        status: +this.formMap.status,
      };

      this.setHotspotSize({
        width: +this.formMap.width,
        height: +this.formMap.height,
      });

      this.handleSaveHotspot();
    },
    handleHideTextChange(val) {
      this.setTextHideBySublayerList(val);
    },
    closeEditHotspotModal() {
      this.isHotspotVisible = false;
      this.resetForm();
    },
    resetForm() {
      this.formMap = {
        hotspotName: "",
        status: 1,
        listOrder: 1,
        width: 0,
        height: 0,
      };
    },
    onKeydown(e) {
      let key = e.key;
      if (key == "e" || key == "E" || key == "+" || key == "-") {
        e.returnValue = false;
      } else {
        e.returnValue = true;
      }
    },
  },
};
</script>
<style lang="scss">
.topo-point-index-active {
  background-color: #eee;
}
.hotspot-layout {
  text-align: left;
  height: calc(80vh - 160px);
  padding: 0 10px;
  overflow: auto;
}
.hotspot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
  margin-bottom: 10px;
  list-style: none;
}
.hotspot-item-name {
  display: flex;
  width: 50%;
  background-color: $listItemBg;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding-left: 10px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  &:hover {
    background-color: rgba($color: $listItemBg, $alpha: 0.8);
  }
}
.hotspot-item-active {
  background-color: $primaryColor;
  &:hover {
    background-color: $primaryColor;
  }
}
.hotspot-btn-group {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
