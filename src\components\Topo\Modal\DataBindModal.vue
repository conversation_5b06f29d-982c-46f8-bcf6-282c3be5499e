<template>
  <Modal
    v-model="isVisible"
    title="数据"
    @on-ok="handleSubmit"
    @on-cancel="hide"
  >
    <Form ref="formDynamic" :model="formDynamic" :label-width="80">
      <div style="max-height: 227px; overflow: auto">
        <FormItem
          v-for="(item, index) in formDynamic.items"
          :key="index"
          :prop="'items.' + index + '.value'"
          class="topo-form-item"
        >
          <Row>
            <Col span="21">
              <Input type="text" v-model="item.value"></Input>
            </Col>
            <Col span="2" offset="1">
              <!-- <Button @click="handleRemove(index)">删除</Button> -->
              <Icon
                type="md-remove"
                size="20px"
                class="click"
                @click="handleRemove(index)"
              />
            </Col>
          </Row>
        </FormItem>
      </div>
      <FormItem class="topo-form-item">
        <Row>
          <Col span="6">
            <Button type="dashed" long @click="handleAdd" icon="md-add"
              >添加</Button
            >
          </Col>
        </Row>
      </FormItem>
    </Form>
  </Modal>
</template>

<script>
import _ from "lodash";

export default {
  props: {
    bindData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      isVisible: false,
      index: 1,
      formDynamic: {
        items: [],
      },
    };
  },
  watch: {
    bindData(val) {
      this.initData(val);
    },
  },
  methods: {
    initData(val) {
      if (!val) return;
      const list = _.cloneDeep(val.keys || []);
      const len = list.length > 5 ? list.length : 5;
      this.formDynamic.items = Array(len)
        .fill(0)
        .map((ele, index) => ({
          value: list[index] || "",
          index,
        }));
    },
    show() {
      this.isVisible = true;
    },
    hide() {
      this.isVisible = false;
      this.initData(this.bindData);
    },
    handleRemove(index) {
      this.formDynamic.items.splice(index, 1);
    },
    handleAdd() {
      this.formDynamic.items.push({
        value: "",
        index: this.formDynamic.items.length,
      });
    },
    handleSubmit() {
      const keys = this.formDynamic.items
        .map((ele) => ele.value)
        .filter((ele) => !!ele);
      this.$emit("on-change", keys);
    },
    cancel() {
      this.$Message.info("Clicked cancel");
    },
  },
};
</script>

<style lang="scss">
.topo-form-item {
  margin-bottom: 12px;
}
.topo-form-item .ivu-form-item-content {
  margin-left: 20px !important;
  padding: 0 30px;
}
</style>
