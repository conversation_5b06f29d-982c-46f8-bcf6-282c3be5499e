<template>
  <div id="coeded">
    <textarea ref="textarea"></textarea>
    <select
      v-model="mode"
      v-show="false"
      class="code-mode-select"
      @change="changeMode"
    >
      <option :value="mode.value" v-for="mode in modes" :key="mode.value">
        {{ mode.label }}
      </option>
    </select>
  </div>
</template>

<script>
import _CodeMirror from "codemirror";
// 核心样式
// import "codemirror/lib/codemirror.css";
// 引入主题后还需要在 options 中指定主题才会生效
// import "codemirror/theme/cobalt.css";
// import "codemirror/mode/javascript/javascript.js";
// import "codemirror/mode/css/css.js";
// import "codemirror/mode/xml/xml.js";
// import "codemirror/mode/swift/swift.js";
// import "codemirror/mode/vue/vue.js";

const CodeMirror = window.CodeMirror || _CodeMirror;

export default {
  props: ["value", "valKey"],
  data() {
    return {
      height: { height: "500px" },

      code: "",
      // 默认的语法类型
      mode: "javascript",
      options: {
        mode: "javascript",
        // 缩进格式
        tabSize: 1,
        // 主题，对应主题库 JS 需要提前引入
        theme: "cobalt",
        // 显示行号
        lineNumbers: true,
        line: true,
        lineWrapping: true, // 自动换行
        styleActiveLine: true, // 当前行背景高亮
        smartIndent: true,
        indentUnit: 4, // 缩进单位为4
        extraKeys: { Ctrl: "autocomplete" }, //避免热键冲突
        // styleActiveLine: true
      },
      modes: [
        {
          value: "javascript",
          label: "Javascript",
          // }, {
          //   value: 'html',
          //   label: 'XML/HTML'
          // }, {
          //   value: 'x-swift',
          //   label: 'Swift'
          // }, {
          //   value: 'x-vue',
          //   label: 'Vue'
        },
      ],
    };
  },
  methods: {
    _initialize() {
      // 初始化编辑器实例，传入需要被实例化的文本域对象和默认配置
      this.coder = CodeMirror.fromTextArea(this.$refs.textarea, this.options);
      // 编辑器赋值
      this.coder.setValue(this.value || this.code);

      // 支持双向绑定
      this.coder.on("change", (coder) => {
        this.code = coder.getValue();
        let str = this.code;
        // str = str.replace(/\ +/g,"").replace(/[\r\n]/g,"")
        this.$emit("changeValue", this.valKey, str);
      });

      // 尝试从父容器获取语法类型
      if (this.language) {
        // 获取具体的语法类型对象
        let modeObj = this._getLanguage(this.language);

        // 判断父容器传入的语法是否被支持
        if (modeObj) {
          this.mode = modeObj.label;
        }
      }
    },
    // 获取当前语法类型
    _getLanguage(language) {
      // 在支持的语法类型列表中寻找传入的语法类型
      return this.modes.find((mode) => {
        // 所有的值都忽略大小写，方便比较
        let currentLanguage = language.toLowerCase();
        let currentLabel = mode.label.toLowerCase();
        let currentValue = mode.value.toLowerCase();
        // 由于真实值可能不规范，例如 java 的真实值是 x-java ，所以讲 value 和 label 同时和传入语法进行比较
        return (
          currentLabel === currentLanguage || currentValue === currentLanguage
        );
      });
    },
    // 更改模式
    changeMode(val) {
      // 修改编辑器的语法配置
      this.coder.setOption("mode", `text/${val}`);
    },
  },
  mounted() {
    this.code = this.value;
    this._initialize();
    // this.height = {height:document.documentElement.clientHeight*0.6+'px'}
    // $('.CodeMirror').css('height',this.height)
  },
};
</script>
<style lang="scss">
#coeded {
  width: 100%;
  height: 100%;
  .CodeMirror {
    height: 100% !important;
  }
}
</style>
