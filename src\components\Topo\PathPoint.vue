<template>
  <g>
    <path
      :d="generatePath"
      fill="none"
      :stroke="data.locked ? '#7d8694' : colorSelected"
      stroke-width="2"
      stroke-dasharray="5,5"
    ></path>
    <g v-for="(item, index) in points" :key="index">
      <!-- 这是为了展示path的路径，更明显 -->
      <g v-if="item.q">
        <g>
          <line
            class="link-Anchor-line"
            :x1="points[index - 1].x"
            :y1="points[index - 1].y"
            :x2="item.q.x"
            :y2="item.q.y"
          ></line>
          <line
            class="link-Anchor-line"
            :x1="item.x"
            :y1="item.y"
            :x2="item.q.x"
            :y2="item.q.y"
          ></line>
          <circle
            class="link-Anchor-point"
            :cx="item.q.x"
            :cy="item.q.y"
            :r="pointCircleSize"
            fill="blue"
            @mousedown="handlePointMousedown('Q', index)"
          ></circle>
        </g>
        <circle
          :class="[{ 'is-active': index === activeIndex }, 'link-Point']"
          :cx="item.x"
          :cy="item.y"
          :r="pointCircleSize"
          fill="blue"
          @mousedown="handlePointMousedown('L', index)"
        ></circle>
      </g>
      <g v-else-if="item.c">
        <g>
          <line
            class="link-Anchor-line"
            :x1="points[index - 1].x"
            :y1="points[index - 1].y"
            :x2="item.c[0].x"
            :y2="item.c[0].y"
          ></line>
          <circle
            class="link-Anchor-point"
            :cx="item.c[0].x"
            :cy="item.c[0].y"
            :r="pointCircleSize"
            fill="blue"
            @mousedown="handlePointMousedown('C', index, 0)"
          ></circle>
          <line
            class="link-Anchor-line"
            :x1="item.x"
            :y1="item.y"
            :x2="item.c[1].x"
            :y2="item.c[1].y"
          ></line>
          <circle
            class="link-Anchor-point"
            :cx="item.c[1].x"
            :cy="item.c[1].y"
            :r="pointCircleSize"
            fill="blue"
            @mousedown="handlePointMousedown('C', index, 1)"
          ></circle>
        </g>
        <circle
          :class="[{ 'is-active': index === activeIndex }, 'link-Point']"
          :cx="item.x"
          :cy="item.y"
          :r="pointCircleSize"
          fill="blue"
          @mousedown="handlePointMousedown('L', index)"
        ></circle>
      </g>
      <circle
        v-else
        :class="[{ 'is-active': index === activeIndex }, 'link-Point']"
        :cx="item.x"
        :cy="item.y"
        :r="pointCircleSize"
        fill="blue"
        @mousedown="handlePointMousedown('L', index)"
      ></circle>
    </g>
  </g>
</template>
<script>
import { mapState } from "vuex";
import { mapMutations } from "vuex";

export default {
  name: "PathPoint",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    mousePosition: {
      type: Object,
      default: () => {},
    },
    adsorbPosition: {
      type: Object,
      default: () => {},
    },
    isShiftKeyDown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      points: [],
      closePath: false,
      activeIndex: null,
      activeType: "",
      cIndex: 0,
      el: null,
      isPointDrag: false,
      isPointMove: false,
      pointCircleSize: 4,
    };
  },

  mounted() {
    this.initEventBus();
    this.initEvent();
  },
  beforeDestroy() {
    this.el.removeEventListener("mouseup", this.handleMouseup);
    this.$bus.off("selectAttributePathPoint");
  },
  computed: {
    ...mapState("topoStore", {
      colorSelected: "colorSelected",
      roadStyles() {
        const { linkStyles } = this.data;
        return JSON.parse(linkStyles);
      },
    }),
    generatePath() {
      if (this.roadStyles.type === "隧桥" && !this.roadStyles.isRoad)
        return this.data.linkPath;
      let { points, closePath } = this;
      if (points && !points.length) return;
      let d = "";
      points.forEach((p, i) => {
        if (i === 0) {
          // first point
          d += "M";
        } else if (p.q) {
          // quadratic
          d += `Q ${p.q.x} ${p.q.y} `;
        } else if (p.c) {
          // cubic
          d += `C ${p.c[0].x} ${p.c[0].y} ${p.c[1].x} ${p.c[1].y} `;
        } else if (p.a) {
          // arc
          d += `A ${p.a.rx} ${p.a.ry} ${p.a.rot} ${p.a.laf} ${p.a.sf} `;
        } else {
          d += "L ";
        }
        d += `${p.x} ${p.y} `;
      });
      if (closePath) d += "Z";
      return d;
    },
  },
  watch: {
    mousePosition(val, oldVal) {
      if (!this.isPointDrag || JSON.stringify(val) === JSON.stringify(oldVal))
        return;
      this.isPointMove = true;
      this.movePoint(val);
    },
    data: {
      handler(val) {
        this.points = val.pathPoints || [];
        this.closePath = val.linkPath.includes("Z");
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    ...mapMutations("topoStore", ["setPointSelectedInfo"]),
    initEventBus() {
      this.$bus.on("selectAttributePathPoint", (index) => {
        this.activeIndex = index;
      });
    },
    initEvent() {
      this.el = document.querySelector("#topoEdit");
      this.el.addEventListener("mouseup", this.handleMouseup);
    },
    // SVG鼠标释放
    handleMouseup() {
      this.isPointMove = false;
      this.isPointDrag = false;
    },
    // 拐点鼠标按下
    handlePointMousedown(type, index, cIndex) {
      this.activeType = type;
      this.activeIndex = index;
      this.cIndex = cIndex;
      this.isPointDrag = true;
      this.$bus.emit("handlePointSelected", { type, index });
      this.setPointSelectedInfo({
        type,
        index,
        cIndex,
      });
    },
    // 拖拽点，
    movePoint(position) {
      position = {
        x: position.x - 8,
        y: position.y - 8,
      };
      switch (this.activeType) {
        case "L":
          // 吸附点， 遇到节点 进行吸附
          if (Object.keys(this.adsorbPosition).length) {
            this.points[this.activeIndex].x = this.adsorbPosition.x;
            this.points[this.activeIndex].y = this.adsorbPosition.y;
          } else if (this.isShiftKeyDown) {
            if (this.activeIndex === 0) {
              const nextPoint = this.points[this.activeIndex + 1];
              const tx = position.x - nextPoint.x;
              const ty = position.y - nextPoint.y;
              if (Math.abs(tx) > Math.abs(ty)) {
                this.points[this.activeIndex].x = position.x;
                this.points[this.activeIndex].y = nextPoint.y;
              } else {
                this.points[this.activeIndex].x = nextPoint.x;
                this.points[this.activeIndex].y = position.y;
              }
            } else if (this.activeIndex === this.points.length - 1) {
              const prePoint = this.points[this.activeIndex - 1];
              let tx = position.x - prePoint.x;
              let ty = position.y - prePoint.y;
              if (Math.abs(tx) > Math.abs(ty)) {
                this.points[this.activeIndex].x = position.x;
                this.points[this.activeIndex].y = prePoint.y;
              } else {
                this.points[this.activeIndex].x = prePoint.x;
                this.points[this.activeIndex].y = position.y;
              }
            } else {
              // 非首点和尾点，按住shift，进行横平竖直处理
              const { x, y } = this.points[this.activeIndex];
              const prePoint = this.points[this.activeIndex - 1];
              const nextPoint = this.points[this.activeIndex + 1];

              let PA = [],
                PB = [],
                res = 0;
              if (
                (prePoint.y < nextPoint.y && prePoint.x < nextPoint.x) ||
                (prePoint.y > nextPoint.y && prePoint.x > nextPoint.x)
              ) {
                PA = [nextPoint.x - x, nextPoint.y - y];
                PB = [prePoint.x - x, prePoint.y - y];
                res = PA[0] * PB[1] - PA[1] * PB[0];
              } else {
                PA = [prePoint.x - x, prePoint.y - y];
                PB = [nextPoint.x - x, nextPoint.y - y];
                res = PA[0] * PB[1] - PA[1] * PB[0];
              }
              // 在线段的左侧
              if (res <= 0) {
                this.points[this.activeIndex].x = prePoint.x;
                this.points[this.activeIndex].y = nextPoint.y;
              } else if (res > 0) {
                // 在线段的右侧
                this.points[this.activeIndex].x = nextPoint.x;
                this.points[this.activeIndex].y = prePoint.y;
              }
            }
          } else {
            this.points[this.activeIndex].x = position.x;
            this.points[this.activeIndex].y = position.y;
          }

          break;
        case "Q":
          this.points[this.activeIndex].q = position;
          break;
        case "C":
          this.$set(this.points[this.activeIndex].c, this.cIndex, position);
          break;
        case "A":
          this.points[this.activeIndex].a = position;
          break;
        default:
          break;
      }
      this.$emit("pointChange", {
        points: this.points,
        pointIndex: this.activeIndex,
        isPointDragMove: this.isPointDrag && this.isPointMove,
      });
    },
  },
};
</script>

<style lang="scss">
.link-Anchor-line {
  stroke: #888;
  stroke-width: 1px;
  stroke-dasharray: 5 5;
}
.link-Anchor-point {
  cursor: pointer;
  fill: #ffffffbb;
}
.link-Point {
  cursor: pointer;
  fill: #ffffffbb;
  transition: fill 0.2s;
}
.is-active {
  fill: #00e676;
}
</style>
