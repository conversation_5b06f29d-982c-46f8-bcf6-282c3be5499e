<template>
  <g
    :id="`group-${formatId(data.nodeId)}`"
    :transform="`translate(${data.tx}, ${data.ty}) 
            rotate(${data.rotate} ${data.middleRotatePoint.x} ${data.middleRotatePoint.y})`"
  >
    <g v-if="isShowImageLine && isShowNode">
      <rect
        v-if="
          data.nodeType === '220kv-p' ||
          data.nodeType === '500kv-p' ||
          data.nodeType === 'dwzj3' ||
          data.nodeType === 'gx4' ||
          data.nodeType === '电网组件3'
        "
        :width="data.w"
        :height="data.h"
        :x="data.x"
        :y="data.y"
        style="fill: none; stroke-width: 2"
        :stroke="data.nodeType === '220kv-p' ? '#00a8ff' : 'red'"
      />
      <!-- <rect
          :width="data.w"
          :height="data.h"
          :x="data.x"
          :y="data.y"
          style="fill:none;stroke:yellow;stroke-width:2"
        /> -->
      <ellipse
        v-else
        :cx="data.middleRotatePoint.x"
        :cy="data.middleRotatePoint.y"
        :rx="data.w / 2"
        :ry="data.h / 2"
        style="fill: none; stroke-width: 2"
        :stroke="data.nodeType === '220kv' ? '#00a8ff' : 'red'"
      />
      <!-- <ellipse
          :cx="data.middleRotatePoint.x"
          :cy="data.middleRotatePoint.y"
          :rx="data.w / 2 + scaleLen"
          :ry="data.h / 2 + scaleLen"
          style="fill:none;stroke-width:1"
          stroke="#f39898"
        /> -->
      <!-- <path
        :d="dPath.h"
        stroke="red"
        stroke-width="2"
        v-show="data.nodeType !== 'image'"
      ></path>
      <path
        :d="dPath.v"
        stroke="red"
        stroke-width="2"
        v-show="data.nodeType !== 'image'"
      ></path>
      <NodeText v-if="data.nodeText" :data="data"></NodeText> -->
    </g>
    <g v-if="!isShowImageLine">
      <!-- <image
        class="custom-image"
        :width="data.w"
        :height="data.h"
        :x="data.x"
        :y="data.y"
        :href="nodeStyles.image"
        :data-url="nodeStyles.image"
        pointer-events="none"
        :style="{
          filter: nodeStyles.filter,
        }"
      ></image> -->
      <foreignObject
        :width="data.w"
        :height="data.h"
        :x="data.x"
        :y="data.y"
        :transform="`scale(${
          nodeStyles.scale === undefined ? 1 : nodeStyles.scale
        })`"
        :transform-origin="`${data.middleRotatePoint.x} ${data.middleRotatePoint.y}`"
      >
        <div
          xmlns="http://www.w3.org/1999/xhtml"
          class="custom-image"
          :data-url="nodeStyles.image"
          style="
            background-color: transparent;
            width: 100%;
            height: 100%;
            background-repeat: no-repeat;
            pointer-events: none;
            background-position: center;
          "
          :style="{
            backgroundImage: `url('${nodeStyles.image}')`,
            backgroundSize: nodeStyles.isFill ? '100% 100%' : 'contain',
            filter: nodeStyles.filter,
          }"
        ></div>
      </foreignObject>
      <!-- 分排 -->
      <line
        v-if="
          isPlanDividerShow
            ? nodeStyles.line.length > 0 && !nodeStyles.isShowDivid
            : nodeStyles.isShowDivid
        "
        :transform="`
            rotate(${nodeStyles.line.rotate} ${data.middleRotatePoint.x} ${data.middleRotatePoint.y})`"
        :x1="data.middleRotatePoint.x - nodeStyles.line.length / 2"
        :y1="data.middleRotatePoint.y"
        :x2="data.middleRotatePoint.x + nodeStyles.line.length / 2"
        :y2="data.middleRotatePoint.y"
        stroke-width="3"
        :style="{
          stroke: nodeStyles.dividColor,
        }"
      ></line>
      <!-- <NodeText v-if="data.nodeText" :data="data"></NodeText> -->
    </g>

    <circle
      v-if="isCircle"
      class="custom-move-rect"
      :cx="data.middleRotatePoint.x"
      :cy="data.middleRotatePoint.y"
      :r="Math.min(data.w, data.h) / 2"
      fill="none"
      :stroke-width="isBindNode || data.selected ? 2 : 0"
      :stroke="data.locked ? '#7d8694' : colorSelected"
      style="cursor: move; pointer-events: all"
      stroke-dasharray="3 3"
      @mousedown="handleMousedown"
      @mouseup="handleMouseup"
    >
    </circle>
    <rect
      v-else
      class="custom-move-rect"
      :width="data.w"
      :height="data.h"
      :x="data.x"
      :y="data.y"
      :id="`rect-${formatId(data.nodeId)}`"
      fill="none"
      :stroke-width="isBindNode || data.selected ? 2 : 0"
      :stroke="data.locked ? '#7d8694' : colorSelected"
      stroke-dasharray="3 3"
      @mousedown="handleMousedown"
      @mouseup="handleMouseup"
      style="cursor: move; pointer-events: all"
    ></rect>
  </g>
</template>

<script>
import { mapState } from "vuex";
import NodeText from "./NodeText.vue";
import { getImageUrl } from "./utils";
import { drawStationIcon } from "../../utils/drawSvg";
import { colorList } from "../../utils/constant";
import { formatId } from "@/utils/tools/format";

const filterColorList = {
  green:
    "brightness(0) saturate(100%) invert(23%) sepia(99%) saturate(1439%) hue-rotate(100deg) brightness(107%) contrast(106%)",
  pink: "brightness(0) saturate(100%) invert(29%) sepia(37%) saturate(5527%) hue-rotate(310deg) brightness(100%) contrast(102%)",
  black:
    "brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(0%) hue-rotate(234deg) brightness(96%) contrast(107%)",
  red: "brightness(0) saturate(100%) invert(10%) sepia(81%) saturate(6148%) hue-rotate(347deg) brightness(92%) contrast(98%)",
  orange:
    "brightness(0) saturate(100%) invert(32%) sepia(79%) saturate(3277%) hue-rotate(1deg) brightness(102%) contrast(104%)",
  blue: "brightness(0) saturate(100%) invert(36%) sepia(81%) saturate(4907%) hue-rotate(223deg) brightness(99%) contrast(99%)",
};

export default {
  name: "CustomImage",
  components: { NodeText },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
    isBindNode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      scaleLen: 24,
    };
  },

  mounted() {},
  computed: {
    ...mapState("topoStore", {
      isShowImageLine: "isShowImageLine",
      is220kvShow: "is220kvShow",
      colorSelected: "colorSelected",
      isPlanDividerShow: "isPlanDividerShow",
    }),
    isCircle() {
      // let nodeType = this.data.nodeType;
      let type = this.data.metaData && this.data.metaData.type;
      // if (
      //   ["220kv-p", "500kv-p", "dwzj3", "gx4", "电网组件3"].includes(nodeType)
      // ) {
      //   return false;
      // }
      //  开关站,换流站,变电站
      return ["TransStation", "ExchStation"].includes(type);
    },
    nodeStyles() {
      let {
        nodeStyles,
        metaData = {},
        nodeType,
        extraMetaData = {
          switch: [],
        },
      } = this.data;

      let nodeStylesObj = nodeStyles ? JSON.parse(nodeStyles) : {};

      let filter;

      const {
        powerType,
        powerTypeByCustom,
        volt,
        type,
        xfmrNo,
        stationType,
        switchNo,
        switchRunningNo,
      } = metaData;

      let img = "";
      //   分排判断
      let switchNos = switchNo
        ? switchNo.split(",").map((ele) => ele.split("_")[0])
        : [];

      let isShowDivid = false;

      if (switchRunningNo) {
        const switchRunningNos = switchRunningNo.split(",");
        switchNos.forEach((ele, index) => {
          if (switchRunningNos.includes(ele)) {
            isShowDivid = !!(
              isShowDivid || +extraMetaData.switch[index]?.status
            );
          }
        });
      }

      //   显示图标
      if (type === "PowerStation" && powerType) {
        if (powerType === "WIND") {
          const type = powerTypeByCustom === "海" ? "WIND" : "LAND";
          img = `powerPlant_s_${type}.svg`;
        } else {
          img = `powerPlant_s_${powerType}.svg`;
        }
        if (!volt) {
          filter = "";
        } else {
          if (volt === "_500KV") {
            filter = filterColorList.red;
          } else {
            filter = filterColorList.black;
          }
        }
      } else if (type === "TNode") {
        img = require("../../assets/images/meta-icons/power/t.svg");
      } else {
        if (type === "ExchStation") {
          img = "powerPlant_s_ExchStation.svg";
        } else if (type === "TransStation") {
          if (volt === "_500KV" || volt === "_1000KV") {
            let xfmrMvarates = [];
            xfmrNo &&
              xfmrNo.split(",").map((ele) => {
                // 状态为RUNNING  并且xfmrMvarate有值
                const xfmrMvarate = `xfmrMvarate${ele}`;
                const xfmrRunningStatus = `xfmrRunningStatus${ele}`;
                const status = metaData[xfmrRunningStatus];

                if (status === "RUNNING") {
                  xfmrMvarates.push(metaData[xfmrMvarate]);
                }
              });
            img = drawStationIcon(xfmrMvarates);
          } else if (volt === "_220KV" || volt === "_110KV") {
            if (stationType === "小电源") {
              img = "220KVxdy.svg";
            } else {
              img = "220kV-01.svg";
            }
            //   } else if (volt === "_110KV") {
            //     img = "110KV.svg";
          } else {
            img = "220kV-01.svg";
          }
        }
      }

      if (img) {
        if (img.startsWith("data:image") || img.startsWith("img/")) {
          nodeStylesObj.image = img;
        } else {
          img = "/ftp/icon/" + img;
          nodeStylesObj.image = getImageUrl(img);
        }
      } else {
        if (nodeStylesObj.image === "/ftp/icon/分区.png") {
          nodeStylesObj.image = "/ftp/icon/fq.png";
        }
        nodeStylesObj.image = nodeStylesObj.image
          ? getImageUrl(nodeStylesObj.image)
          : require("././images/addenvironment.png");
      }

      // if (!res.image.includes("http")) {
      // }
      // res.image = `/img${res.image}`;
      if (!nodeStylesObj.line) {
        nodeStylesObj.line = {
          length: 0,
          rotate: 0,
        };
      }

      if (nodeStylesObj.dynamicColor) {
        filter = filterColorList[nodeStylesObj.dynamicColor];
      }

      return {
        ...nodeStylesObj,
        filter,
        isShowDivid,
        dividColor:
          nodeStylesObj.line.fill ||
          colorList[nodeStylesObj.dynamicColor] ||
          "#000000",
      };
    },
    isShowNode() {
      const { nodeType } = this.data;
      if (nodeType === "220kv" || nodeType === "220kv-p") {
        return this.is220kvShow;
      } else {
        return true;
      }
    },
    dPath() {
      const { x, y, w, h } = this.data;
      return {
        h: `M ${x} ${y + h / 2} L ${x + w} ${y + h / 2}`,
        v: `M ${x + w / 2} ${y} L ${x + w / 2} ${y + h}`,
      };
    },
  },
  methods: {
    formatId,
    handleMousedown(e) {
      this.$emit("cusmousedown", e);
    },
    handleMouseup(e) {
      this.$emit("cusmouseup", e);
    },
  },
};
</script>

<style lang="scss" scoped></style>
