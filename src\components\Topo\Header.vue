<template>
  <div class="topo-header-layout">
    <!-- <Icon
        type="md-arrow-back"
        size="30"
        style="margin: 0 20px"
        @click="$router.go(-1)"
      /> -->
    <div class="flex-v-center">
      <Tooltip content="图文件管理">
        <i class="iconfont icon-wenjianjia" @click="handleMapFileClick"></i>
      </Tooltip>
      <Tooltip content="图元库">
        <i class="iconfont icon-tuyuan" @click="handlemapMetaClick"></i>
      </Tooltip>
      <Tooltip content="模型数据">
        <i class="iconfont icon-moxing" @click="handleModelClick"></i>
      </Tooltip>
      <div class="flex-v-center" style="width: 450px; margin-left: 5%">
        <Tooltip content="点击">
          <i class="iconfont icon-dianji"></i>
        </Tooltip>
        <!-- <Tooltip content="框选">
          <i class="iconfont icon-kuangxuan"></i>
        </Tooltip> -->
        <Tooltip content="放大">
          <i class="iconfont icon-fangda"></i>
        </Tooltip>
        <Tooltip content="缩小">
          <i class="iconfont icon-suoxiao"></i>
        </Tooltip>
        <Checkbox v-model="isLinkDraw" @on-change="handleDrawLineChange"
          >划线</Checkbox
        >
        <Checkbox
          v-model="isLinkAlign"
          v-if="isLinkDraw"
          @on-change="handleLinkAlignChange"
          >是否对齐中心点</Checkbox
        >
      </div>
    </div>

    <div>
      <Dropdown>
        <i class="iconfont icon-duiqi"></i>
        <DropdownMenu slot="list">
          <DropdownItem v-for="(item, index) in alignTextList" :key="item">
            <div @click="handleAlignClick(index)">{{ item }}</div>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Dropdown>
        <i class="iconfont icon-shunxu"></i>
        <DropdownMenu slot="list">
          <DropdownItem>
            <div @click="handleTierClick(1)">置顶</div>
          </DropdownItem>
          <DropdownItem>
            <div @click="handleTierClick(2)">置底</div>
          </DropdownItem>
          <DropdownItem>
            <div @click="handleTierClick(3)">上移一层</div>
          </DropdownItem>
          <DropdownItem>
            <div @click="handleTierClick(4)">下移一层</div>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <!-- <Dropdown>
        <i class="iconfont icon-zuhe"></i>
        <DropdownMenu slot="list">
          <DropdownItem>
            <div @click="handleGroupClick(true)">组合</div>
          </DropdownItem>
          <DropdownItem>
            <div @click="handleGroupClick(false)">打散</div>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Dropdown>
        <i class="iconfont icon-suoding"></i>
        <DropdownMenu slot="list">
          <DropdownItem>
            <div @click="handleLockClick(true)">锁定</div>
          </DropdownItem>
          <DropdownItem>
            <div @click="handleLockClick(false)">解锁</div>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown> -->
      <!-- <Tooltip content="分组">
        <i class="iconfont icon-fenzu"></i>
      </Tooltip> -->
      <Tooltip content="适应屏幕">
        <i class="iconfont icon-shiying" @click="handleShowwholeSvgClick"></i>
      </Tooltip>
      <!-- <Tooltip content="撤销">
        <i class="iconfont icon-chexiao"></i>
      </Tooltip>
      <Tooltip content="重做">
        <i class="iconfont icon-chongzuo"></i>
      </Tooltip> -->
    </div>
    <div>
      <Tooltip content="注释">
        <i class="iconfont icon-zhushi" @click="handleDescriptionClick"></i>
      </Tooltip>
      <Tooltip content="预览">
        <i class="iconfont icon-yulan" @click="handlePreviewClick"></i>
      </Tooltip>
      <Tooltip content="全屏显示">
        <i class="iconfont icon-quanping" @click="handleFullScreenClick"></i>
      </Tooltip>
      <Tooltip content="生成SVG">
        <i class="iconfont icon-SVG" @click="isGenerateSvgVisible = true"></i>
      </Tooltip>
      <Tooltip content="导出">
        <i class="iconfont icon-daochu" @click="handleExportClick"></i>
      </Tooltip>
      <Tooltip content="导出Excel">
        <i class="iconfont icon-daochu" @click="handleExportExcelClick"></i>
      </Tooltip>
      <Tooltip content="版本">
        <i class="iconfont icon-banbenguanli" @click="handleVersionClick"></i>
      </Tooltip>
      <Tooltip content="录入">
        <i class="iconfont icon-wendang-bianji" @click="handleInputClick"></i>
      </Tooltip>
      <!-- <Dropdown>
        <i class="iconfont icon-daochu"></i>
        <DropdownMenu slot="list">
          <DropdownItem @click.native="handleExportClick('image')">
            导出为图片
          </DropdownItem>
          <DropdownItem @click.native="handleExportClick('pdf')">
            导出为PDF
          </DropdownItem>
        </DropdownMenu>
      </Dropdown> -->

      <!-- <Tooltip content="打印">
        <i class="iconfont icon-dayin" @click="handlePrintClick"></i>
      </Tooltip> -->
      <Tooltip content="G文件导出">
        <span class="iconfont" @click="exportGFile">G</span>
      </Tooltip>
    </div>
    <!-- <Icon
        type="ios-brush-outline"
        draggable="true"
        size="30"
        style="margin-left: 35px"
        @dragstart.native="handleDragStart($event, 'text')"
      />
      <Icon
        type="ios-square-outline"
        draggable="true"
        size="30"
        style="margin-left: 15px"
        @dragstart.native="handleDragStart($event, 'rect')"
      />
      <Icon
        type="ios-radio-button-off"
        draggable="true"
        size="30"
        style="margin-left: 15px"
        @dragstart.native="handleDragStart($event, 'circle')"
      />
      <Icon
        type="ios-resize"
        size="30"
        draggable="true"
        style="margin-left: 15px"
        @dragstart.native="handleDragStart($event, 'path')"
      />
      <Icon
        type="ios-image-outline"
        draggable="true"
        size="30"
        style="margin: 0 15px"
        @dragstart.native="handleDragStart($event, 'image')"
      /> -->
    <SvgPrintConfigModal ref="svgPrintConfig"></SvgPrintConfigModal>
    <ExportSvgConfigModal
      :mapInfo="mapInfo"
      ref="exportSvgConfigModal"
    ></ExportSvgConfigModal>
    <VersionPublishModal
      :mapInfo="mapInfo"
      ref="versionPublishModal"
    ></VersionPublishModal>
    <Modal
      v-model="isVisible"
      title="导出配置"
      :width="1000"
      @on-ok="exportSvg"
      @on-cancel="handleExportImageCancel"
    >
      <div>
        <Checkbox v-model="isIncludeBlock" @on-change="handleBlockChange"
          >是否包含色块</Checkbox
        >
        <Checkbox v-model="isIncludeBg" @on-change="handleBgChange"
          >是否包含背景</Checkbox
        >

        <Select
          style="width: 100px; margin: 0 10px"
          size="small"
          v-model="svgPostion"
          @on-change="svgPostionChange"
        >
          <Option
            v-for="item in svgPostionOptions"
            :value="item.value"
            :key="item.value"
            >{{ item.label }}</Option
          >
        </Select>

        <Checkbox v-model="isIncludeLegend" @on-change="handleLegendChange"
          >是否包含图例</Checkbox
        >
      </div>

      <div id="printContent1">
        <!-- <img id="printImg" alt="" /> -->
      </div>
      <div slot="footer">
        <Button type="info" @click="exportSvg"> 确定 </Button>
        <Button type="primary" @click="previewImage">全屏预览</Button>
        <Button @click="handleExportImageCancel"> 取消 </Button>
      </div>
    </Modal>

    <Modal
      v-model="isGenerateSvgVisible"
      title="导出SVG配置"
      :mask-closable="false"
    >
      <div style="display: flex; margin-bottom: 10px; align-items: center">
        <span>子图层:</span>
        <Select
          v-model="sublayerExportList"
          multiple
          style="flex: 1; margin-left: 10px"
        >
          <Option
            v-for="item in sublayerList"
            :value="item.sublayerId"
            :key="item.sublayerId"
            >{{ item.sublayerName }}</Option
          >
        </Select>
      </div>
      <div>
        <Button
          type="primary"
          @click="handleGenerateSvgClick('1', '1')"
          style="margin-right: 10px"
          :loading="loading"
          >大图</Button
        >
        <Dropdown
          style="margin-right: 10px"
          @on-click="handleGenerateSvgClick($event, '2')"
        >
          <Button type="primary" :loading="loading500">500KV小图</Button>
          <DropdownMenu slot="list" v-show="!loading500">
            <DropdownItem>上传svg</DropdownItem>
            <DropdownItem name="svg">下载svg</DropdownItem>
          </DropdownMenu>
        </Dropdown>

        <Dropdown
          style="margin-right: 10px"
          @on-click="handleGenerateSvgClick($event, '3')"
        >
          <Button type="primary" :loading="loading220">220KV小图</Button>
          <DropdownMenu slot="list" v-show="!loading220">
            <DropdownItem>上传svg</DropdownItem>
            <DropdownItem name="svg">下载svg</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>

      <div slot="footer"></div>
    </Modal>
    <MapDescriptionModifyModal
      :mapInfo="mapInfo"
      ref="mapDescriptionModifyModal"
    ></MapDescriptionModifyModal>
    <DataInputModal :mapInfo="mapInfo" ref="dataInputModal"></DataInputModal>
    <ModelBindModal ref="MmodelBindModalRef"></ModelBindModal>
    <LeftView></LeftView>
    <RightView></RightView>
    <MapMetaModal></MapMetaModal>
    <div
      id="svg_panel"
      style="display: none; position: fixed; left: 0; top: 0"
    ></div>
  </div>
  <!-- <div class="topo-node-control">
      <Dropdown>
        <Button> 对齐 </Button>
        <DropdownMenu slot="list">
          <DropdownItem v-for="(item, index) in alignTextList" :key="item">
            <div @click="handleAlignClick(index)">{{ item }}</div>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Dropdown>
        <Button style="margin-left: 15px"> 层级 </Button>
        <DropdownMenu slot="list">
          <DropdownItem>
            <div @click="handleTierClick(1)">置顶</div>
          </DropdownItem>
          <DropdownItem>
            <div @click="handleTierClick(2)">置底</div>
          </DropdownItem>
          <DropdownItem>
            <div @click="handleTierClick(3)">上移一层</div>
          </DropdownItem>
          <DropdownItem>
            <div @click="handleTierClick(4)">下移一层</div>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Button style="margin-left: 15px" @click="handleGroupClick(true)">
        组合
      </Button>
      <Button style="margin-left: 15px" @click="handleGroupClick(false)">
        打散
      </Button>
      <Button style="margin-left: 15px" @click="handleLockClick(true)">
        锁定
      </Button>
      <Button style="margin-left: 15px" @click="handleLockClick(false)">
        解锁
      </Button>
      <Button style="margin-left: 15px" @click="handleShowwholeSvgClick(false)">
        全图显示
      </Button>
    </div> -->
  <!-- <div class="topo-svg-control">
      <Icon
        type="md-cloud-download"
        size="30"
        @click="downloadSvg"
        title="保存为png"
      />
      <Icon type="md-aperture" size="30" style="margin-left: 15px" />
      <Icon type="md-aperture" size="30" style="margin-left: 15px" />
    </div> -->
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import $ from "jquery";
import _ from "lodash";
import { jsPDF } from "jspdf";
import SvgPrintConfigModal from "@/components/Topo/Modal/SvgPrintConfigModal.vue";
import ExportSvgConfigModal from "@/components/Topo/Modal/ExportSvgConfigModal.vue";
import VersionPublishModal from "@/components/Topo/Modal/VersionPublishModal.vue";
import MapDescriptionModifyModal from "@/components/Topo/Modal/MapDescriptionModifyModal.vue";
import DataInputModal from "@/components/Topo/Modal/DataInput/index.vue";
import ModelBindModal from "@/components/Topo/ModelBind.vue";
import RightView from "@/components/Topo/RightView";
import LeftView from "@/components/Topo/LeftView/index.vue";
import MapMetaModal from "@/components/Topo/MapMeta.vue";

import { drawSvgPrintData, clipSvg } from "@/utils/drawSvg";
import { generatePathD } from "./utils";
import { getGFile, downloadFile } from "@/utils/assistant";
import { getSubLayerList } from "@/utils/constant/index";
import ExcelExporter from "@/utils/excelExport";

export default {
  components: {
    MapMetaModal,
    LeftView,
    RightView,
    ModelBindModal,
    SvgPrintConfigModal,
    ExportSvgConfigModal,
    VersionPublishModal,
    MapDescriptionModifyModal,
    DataInputModal,
  },
  data() {
    return {
      isLinkDraw: false,
      isLinkAlign: false,
      alignTextList: [
        "左对齐",
        "右对齐",
        "顶对齐",
        "底对齐",
        "水平居中",
        "垂直居中",
        "水平等间距",
        "垂直等间距",
      ],
      mapInfo: {},
      isVisible: false,
      isUseBg: false,
      isIncludeBg: false,
      isIncludeBlock: false,
      typeExport: "image",
      distance: 40,
      svgPostion: "all",
      svgPostionOptions: [
        {
          value: "all",
          label: "全部",
        },
        {
          value: "up",
          label: "江北",
        },
        {
          value: "down",
          label: "江南",
        },
      ],
      isIncludeLegend: false,
      svgSize: {
        width: 0,
        height: 0,
      },
      svgVerticalScale: 1,
      svgEl: null,
      direction: "horizontal",
      isGenerateSvgVisible: false,
      allNodeLinkList: [],
      nodeLinkList: [],
      loading: false,
      loading500: false,
      loading220: false,
      sublayerExportList: [],
    };
  },
  mounted() {
    this.initEvent();
    this.initKeypressEvent();
  },
  beforeDestroy() {
    // 防止突然退出页面，但是还是划线状态
    this.isLinkDraw = false;
    this.$bus.off("endDrawLink");
    this.$bus.off("onMapSlected");
    this.$bus.off("onAllNodeLinkList");
    this.$bus.off("onMapSlected");
    this.$bus.off("onNodeLinkSublayerListChange");
  },
  computed: {
    ...mapState("topoStore", {
      isMapFileVisible: "isMapFileVisible",
      ismapMetaVisible: "ismapMetaVisible",
      isModelVisible: "isModelVisible",
      versionSelected: "versionSelected",
      sublayerList: "sublayerList",
      compScale: "compScale",
      sublayerSelectedList: "sublayerSelectedList",
    }),
  },
  watch: {
    isLinkDraw(val) {
      this.setPointIsLinkDraw(val);
    },
  },
  methods: {
    ...mapMutations("topoStore", [
      "setPointIsLinkDraw",
      "setLinkAlign",
      "setMapFileVisible",
      "setmapMetaVisible",
      "setModelVisible",
    ]),
    initEvent() {
      this.$bus.on("endDrawLink", () => {
        this.isLinkDraw = false;
      });
      this.$bus.on("onMapSlected", (val) => {
        this.mapInfo = val;
      });
      this.$bus.on("onAllNodeLinkList", (val) => {
        this.allNodeLinkList = val;
      });
      this.$bus.on("onNodeLinkSublayerListChange", (list) => {
        this.nodeLinkList = list;
      });
    },
    initKeypressEvent() {
      const el = document.getElementById("topoEdit");
      // 顶部菜单操作快捷键
      el.addEventListener("keydown", (e) => {
        const { ctrlKey, altKey, shiftKey, code } = e;
        const keyList = [
          "KeyL",
          "KeyR",
          "KeyT",
          "KeyB",
          "KeyC",
          "KeyM",
          "KeyH",
          "KeyV",
        ];
        if (altKey) {
          if (ctrlKey && keyList.includes(code)) {
            e.preventDefault();
            const index = keyList.findIndex((ele) => ele === code);
            this.handleAlignClick(index);
          }
        } else {
          if (ctrlKey && code === "KeyL") {
            this.handleLockClick(true);
          } else if (ctrlKey && code === "KeyG") {
            this.handleGroupClick(true);
          }
        }
      });
    },
    handleMapFileClick() {
      this.setMapFileVisible(!this.isMapFileVisible);
    },
    handlemapMetaClick() {
      this.setmapMetaVisible(!this.ismapMetaVisible);
    },
    handleModelClick() {
      this.$refs.MmodelBindModalRef.showModelModal();
    },
    handleDragStart(e, type) {
      this.isLinkDraw = false;
      e.dataTransfer.setData("type", type);
    },
    handleAlignClick(index) {
      this.$bus.emit("alignClick", index + 1);
    },
    handleGroupClick(isGroup) {
      console.log(this.sle);
      // this.$bus.emit("groupClick", isGroup);
    },
    handleLockClick(isLocked) {
      this.$bus.emit("lockClick", isLocked);
    },
    handleShowwholeSvgClick() {
      this.$bus.emit("showwholeSvgClick");
    },
    // 层级调整
    handleTierClick(index) {
      this.$bus.emit("tierClick", index);
    },
    handleDrawLineChange(val) {
      if (!val) {
        this.isLinkAlign = false;
        this.setLinkAlign(false);
      }
    },
    handleLinkAlignChange(val) {
      this.setLinkAlign(val);
    },
    handleDescriptionClick() {
      if (!this.mapInfo.mapId) {
        this.$Message.warning("请先选择图层");
        return;
      }
      this.$refs.mapDescriptionModifyModal.show();
    },
    handlePreviewClick() {
      if (!this.mapInfo.mapId) {
        this.$Message.warning("请先选择图层");
        return;
      }
      //   window.open(
      //     `http://**************:6818/wisComponents/nariTopoView/test.html?mapId=${this.mapInfo.mapId}`
      //   );
      //   this.$router.push("/preview");
      //   let routeUrl = this.$router.resolve({
      //     path: "/preview",
      //   });
      //   window.open(routeUrl.href, "_blank");
      //   window.open(`static//nariTopoView/test.html?mapId=${this.mapInfo.mapId}`);
      window.open("//************:6818/huadong-map/");
    },
    handleFullScreenClick() {
      const el = document.querySelector("body");
      el.requestFullscreen();
    },
    // 选择图层或者选择子图层打印
    handlePrintClick() {
      if (!this.mapInfo.mapId) {
        this.$Message.warning("请先选择图层");
        return;
      }
      this.$refs.svgPrintConfig.show();
    },
    handleExportImageCancel() {
      this.isVisible = false;
      $("#printContent1").empty();
      this.isIncludeBg = false;
      this.isIncludeBlock = false;
      this.isIncludeLegend = false;
    },
    uploadSvg(svg, type) {
      this.$post("/data/createSVG", {
        svg,
        type,
      }).then((data) => {
        if (data.code === "0000") {
          this.$Message.success("上传成功");
        } else {
          this.$Message.error(data.msg);
        }
      });
    },
    getAllNodeLinkList(type) {
      const links = [];
      const nodes = [];
      if (type === "1" || this.compScale === 1) {
        this.allNodeLinkList.forEach((ele) => {
          if (ele.linkId) {
            links.push(ele);
          } else if (ele.nodeId) {
            nodes.push(ele);
          }
        });
      } else {
        _.cloneDeep(this.allNodeLinkList).forEach((ele) => {
          if (ele.linkId) {
            ele.pathPoints = ele.pathPoints.map((elePoint) => {
              elePoint.x = elePoint.x * this.compScale;
              elePoint.y = elePoint.y * this.compScale;
              return elePoint;
            });
            const { linkStyles } = ele;
            const styles = linkStyles ? JSON.parse(linkStyles) : {};

            ele.linkPath = generatePathD(ele, styles.isBlock);
            ele.linkWidth = parseFloat(
              (ele.linkWidth * this.compScale).toFixed(2)
            );

            links.push(ele);
          } else if (ele.nodeId) {
            const { w, h, x, y, fontSize } = ele;
            ele.w = w * this.compScale;
            ele.h = h * this.compScale;
            ele.x = x * this.compScale;
            ele.y = y * this.compScale;
            ele.middleRotatePoint.x = ele.x + ele.w / 2;
            ele.middleRotatePoint.y = ele.y + ele.h / 2;
            ele.nodePosition = `${ele.x},${ele.y}`;
            ele.nodeSize = `${ele.w}*${ele.h}`;
            ele.fontSize = (+fontSize || 16) * this.compScale + "";
            nodes.push(ele);
          }
        });
      }

      return {
        nodes,
        links,
      };
    },
    handleGenerateSvgClick(name, type) {
      if (type === "1") {
        this.loading = true;
      } else if (type === "2") {
        this.loading500 = true;
      } else {
        this.loading220 = true;
      }
      this.generateSvgClick(type, name === "svg");
    },
    // 生成svg
    async generateSvgClick(type, isExportScg) {
      if (!this.mapInfo.mapId) {
        this.$Message.warning("请先选择图层");
        return;
      }
      // if (!this.sublayerSelectedList.length) {
      //   this.$Message.warning("请先选择子图层");
      //   return;
      // }
      //   let width = 7348.75;
      //   let height = 6800;

      const [width, height] = this.mapInfo.mapSize
        .split("*")
        .map((ele) => +ele);

      const _this = this;
      let topoComp = null;
      const isSmall = type === "2" || type === "3";
      // const isSmall = false;
      const sublayerList = this.sublayerExportList.length
        ? this.sublayerExportList
        : getSubLayerList(this.mapInfo.mapName, type === "2");
      topoComp = new NariTopoView(
        "858",
        "123",
        document.getElementById("svg_panel"),
        0,
        {
          property: {
            basic: {
              frame: [0, 0, width, height],
              needSync: false,
            },
            viewSetting: {
              loadMapId: "",
              w: width,
              h: height,
              minS: 1,
              isExportAll: true,
              isSmall: isSmall,
              is500: type === "2",
              compScale: this.compScale,
              sublayerList,
            },
          },
        }
      );

      //   this.$get(`/topoEdit/getNodeLinkListByMapId`, {
      //     mapId: this.mapInfo.mapId,
      //     versionId: this.versionSelected.versionId,
      //   }).then(({ data }) => {

      const { nodes, links } = this.getAllNodeLinkList(type);
      topoComp.initTopo(
        {
          map: this.mapInfo,
          nodes: nodes,
          links: links,
        },
        JSON.parse(JSON.stringify(this.sublayerList))
      );
      topoComp.drawEnd = function () {
        setTimeout(() => {
          const svg = topoComp._downloadSvg();
          if (isExportScg) {
            let aTag = document.createElement("a");
            let blob = new Blob([svg]);
            aTag.download = "test.svg";
            aTag.href = URL.createObjectURL(blob);
            aTag.click();
            URL.revokeObjectURL(blob);
          } else {
            _this.uploadSvg(svg, type);
          }

          if (type === "1") {
            _this.loading = false;
          } else if (type === "2") {
            _this.loading500 = false;
          } else {
            _this.loading220 = false;
          }
        }, 3000);
      };
      //   });
    },
    // 导出
    handleExportClick() {
      if (!this.mapInfo.mapId) {
        this.$Message.warning("请先选择图层");
        return;
      }
      this.$refs.exportSvgConfigModal.show();
    },
    handleVersionClick() {
      if (!this.mapInfo.mapId) {
        return this.$Message.warning("请先选择图层");
      }
      this.$refs.versionPublishModal.show();
    },
    handleInputClick() {
      //   if (!this.mapInfo.mapId) {
      //     return this.$Message.warning("请先选择图层");
      //   }
      this.$refs.dataInputModal.show();
    },
    handleExport1Click(type) {
      //   const { mapId, mapSize } = this.mapInfo;
      //   if (!mapId) return this.$Message.warning("未选择图层");
      //   this.typeExport = type;
      //   this.isVisible = true;
      //   this.$nextTick(() => {
      //     this.generateSvg();
      //     this.handleBgChange(this.isIncludeBg);
      //   });
      //   let [w, h] = mapSize.split("*").map((ele) => +ele);
      //   const scale = 1 - this.distance / h;
      //   this.$bus.emit("onSvgVerticalScaleChange", {
      //     svgVerticalScale: scale,
      //     isKeepSvgSize: true,
      //   });
    },
    convertImgToBase64(url) {
      return new Promise((resolve) => {
        let canvas = document.createElement("canvas"),
          ctx = canvas.getContext("2d"),
          img = new Image();
        // 服务端也要设置允许跨域
        img.crossOrigin = "Anonymous";
        img.src = url;
        img.onload = function () {
          canvas.height = img.height;
          canvas.width = img.width;
          ctx.drawImage(img, 0, 0);
          let dataURL = canvas.toDataURL("image/png");
          canvas = null;
          resolve(dataURL);
        };
      });
    },
    async generateSvg() {
      const _this = this;
      const { mapId, mapSize, mapName } = this.mapInfo;
      if (!mapId) return this.$Message.warning("未选择图层");
      let svg = $("#topoEdit").clone(true);
      const comp = $(svg).find(".topo-component-list");
      //   let [w, h] = mapSize.split("*").map((ele) => +ele);

      let w = $(svg).width();
      let h = $(svg).height() + 5;
      // 浏览器限制canvas大小，只能按比例缩小
      // https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/canvas
      let scale = 1;

      this.svgSize = {
        width: w,
        height: h,
      };

      if (w > 16384 || h > 16384) {
        scale = 16384 / Math.max(w, h);
        width = w * scale;
        height = h * scale;
      }

      $(svg).attr("id", "printImg1");
      $(svg).css({ transform: "" });
      // 不包含背景
      //   if (!this.isIncludeBg) {
      //     $(svg).css({ background: "transparent" });
      //     $("#printContent1").css({
      //       background: "#ffffff",
      //     });
      //   }

      $("#printImg1").css({
        background: "#ffffff",
      });

      $(svg)
        .find(".topo-component-list")
        .css({
          transform: `scale(${scale})`,
          "transform-origin": "top left",
        });
      $(svg).empty().append(comp);
      $(svg).find(".topo-link-arrow").remove();
      $(svg).find(".custom-move-rect").remove();
      $(svg).find(".custom-path-move").remove();

      const blocks = $(svg).find(".topo-line-block");
      blocks.each(function () {
        const fill = $(this).css("fill");
        $(this).css({
          "stroke-dasharray": "10 10",
          fill: "transparent",
        });
        $(this).attr({
          "data-fill": fill,
        });
      });

      const texts = $(svg).find("text");
      console.log("🚀 ~ generateSvg ~ texts:", texts);
      texts.each(function () {
        const color = $(this).css("fill");
        console.log("🚀 ~ color:", color);
        if (color === "rgb(255, 255, 255)" && !_this.isIncludeBg) {
          $(this).css("fill", "rgb(0, 0, 0)");
        } else if (color === "rgb(0, 255, 0)") {
          $(this).remove();
        }
      });

      const map = new Map();
      const list = $(svg).find(".custom-image");
      for (let i = 0; i < list.length; i++) {
        const el = list[i];
        const url = $(el).attr("href");

        let imgBase64 = "";
        if (map.has(url)) {
          imgBase64 = map.get(url);
        } else {
          imgBase64 = await this.convertImgToBase64(url);
          map.set(url, imgBase64);
        }
        // $(el).css("background-image", `url(${imgBase64})`);
        $(el).attr("href", imgBase64);
      }
      map.clear();

      //   this.$bus.emit("onSvgVerticalScaleChange", {
      //     svgVerticalScale: 1,
      //   });
      $("#printContent1").append(svg[0]);
    },

    async exportSvg() {
      const svg = $("#printImg1");
      const { mapName } = this.mapInfo;

      let width = $(svg).width();
      let height = $(svg).height();

      const list = $(svg).find("#meta-group image");

      for (let i = 0; i < list.length; i++) {
        const el = list[i];
        const url = $(el).attr("href");

        const imgBase64 = await this.convertImgToBase64(url);

        $(el).attr("href", imgBase64);
      }

      this.covertSVG2Image(svg[0], mapName, width, height);
      this.handleExportImageCancel();
    },

    handleBlockChange(val) {
      const blocks = $("#printImg1").find(".topo-line-block");
      const fillColorMap = {
        "rgb(76, 104, 131)": "rgb(215,255,255)",
        "rgb(90, 74, 120)": "rgb(235,233,255)",
        "rgb(118, 89, 100)": "rgb(255,233,240)",
        "rgb(116, 111, 103)": "rgb(252,255,235)",
        "rgb(73, 119, 115)": "rgb(235,255,239)",
      };

      blocks.each(function () {
        let fillBackup = $(this).attr("data-fill");

        $(this).css({
          "stroke-dasharray": "10 10",
          fill: val ? fillColorMap[fillBackup] : "transparent",
        });
      });
    },
    handleBgChange(val) {
      $("#printImg1").css({
        background: val ? "rgb(39, 39, 39)" : "#ffffff",
      });
      const texts = $("#printImg1").find("text");
      texts.each(function () {
        const color = $(this).css("fill");
        if (val) {
          color === "rgb(0, 0, 0)" && $(this).css("fill", "rgb(255, 255, 255)");
        } else {
          color === "rgb(255, 255, 255)" && $(this).css("fill", "rgb(0, 0, 0)");
        }
      });
    },

    handleLegendChange(val) {
      if (val) {
        this.svgPostionChange(this.svgPostion);
      } else {
        $("#meta-group").remove();
      }
    },
    svgPostionChange(val) {
      const { width, height } = this.svgSize;
      let hByScale = height * this.svgVerticalScale;
      const svg = $("#printImg1");
      let moveY = 0;

      if (val === "up") {
        hByScale = hByScale * 0.524;
      } else if (val === "down") {
        moveY = hByScale * 0.53;

        hByScale = hByScale * 0.47;
        // 江南的Y起始点计算
      }

      svg.find(".topo-component-list").css({
        transform: `translate(0, -${moveY}px)`,
      });

      this.isIncludeLegend &&
        drawSvgPrintData(
          svg[0],
          width,
          hByScale,
          this.distance,
          this.svgPostion
        );
      clipSvg("printImg1", width, hByScale, val, moveY);
    },

    covertSVG2Image(node, name, width, height, type = "png") {
      const _this = this;
      let serializer = new XMLSerializer();
      let source =
        '<?xml version="1.0" standalone="no"?>\r\n' +
        serializer.serializeToString(node);
      let image = new Image();
      image.src =
        "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);
      let canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;
      let context = canvas.getContext("2d");
      context.scale(1, 1);
      image.onload = function () {
        context.drawImage(image, 0, 0);
        if (_this.typeExport === "image") {
          let a = document.createElement("a");
          a.download = `${name}.${type}`;
          a.href = canvas.toDataURL(`image/${type}`);
          a.click();
        } else {
          //  A3纸大小 841.89*1190.55
          // 宽大于高的情况
          const pdf = new jsPDF("landscape", "pt", "a3", true);
          const pageData = canvas.toDataURL(`image/${type}`);
          const A3_WIDTH = 1190.55;
          const A3_HEIGHT = 841.89;
          const scale = width / A3_WIDTH;
          const h = canvas.height / scale;
          const marginY = (A3_HEIGHT - h) / 2;
          pdf.addImage(
            pageData,
            "JPEG",
            0,
            marginY,
            1190.55,
            canvas.height / scale
          );
          pdf.save(`${name}.pdf`);
        }
      };
    },
    previewImage() {
      const el = document.querySelector("#printContent1");
      el.requestFullscreen();
    },
    exportGFile() {
      const gFile = getGFile(this.nodeLinkList, this.mapInfo, this.compScale);
      downloadFile(JSON.stringify(gFile), this.mapInfo.mapName + ".g");
    },
    // Excel导出
    handleExportExcelClick() {
      if (!this.mapInfo.mapId) {
        this.$Message.warning("请先选择图层");
        return;
      }

      if (!this.allNodeLinkList || this.allNodeLinkList.length === 0) {
        this.$Message.warning("暂无数据可导出");
        return;
      }

      try {
        ExcelExporter.exportTopoDetailList(this.allNodeLinkList, this.mapInfo);
        this.$Message.success("Excel导出成功");
      } catch (error) {
        console.error("Excel导出失败:", error);
        this.$Message.error("Excel导出失败: " + error.message);
      }
    },
  },
};
</script>
<style lang="scss">
.topo-header-layout {
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;
  // background-color: rgb(148, 166, 204);
  border-bottom: 1px solid $borderColor;
  .topo-node-control {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }
  .topo-svg-control {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 20%;
    padding-right: 30px;
    .ivu-icon {
      cursor: pointer;
    }
  }
  .iconfont {
    font-size: 24px;
    cursor: pointer;
  }
  .ivu-tooltip,
  .ivu-dropdown {
    margin-right: 40px;
  }
  .flex-v-center {
    display: flex;
    height: 100%;
    align-items: center;
  }
}
#printContent1 {
  width: 100%;
  height: 450px;
  // display: flex;
  // justify-content: center;
  overflow: auto;
  margin-top: 10px;
}
</style>
