<template>
  <Modal v-model="isVisible" title="新增子图层">
    <Form
      ref="formValidate"
      :model="sublayerForm"
      :rules="ruleValidate"
      label-position="right"
      :label-width="100"
      :footer-hide="false"
    >
      <FormItem label="子图层名称" prop="sublayerName">
        <AutoComplete
          v-model="sublayerForm.sublayerName"
          @on-change="handleInputChange"
          @on-select="handleInputSelect"
        >
          <Option
            v-for="item in sublayerFilterList"
            :value="item.sublayerId"
            :key="item.sublayerId"
          >
            <span>
              {{ item.sublayerName }}
            </span>
          </Option>
        </AutoComplete>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="hide">取消</Button>
      <Button type="primary" @click="ok">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "SublayeAddrModal",
  props: {
    mapId: {
      type: String,
      default: "",
    },
    nodeSelectedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isVisible: false,
      sublayerForm: {
        sublayerName: "",
      },
      sublayerList: [],
      sublayerSelected: {},
      ruleValidate: {
        sublayerName: [
          {
            required: true,
            message: "名称不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    ...mapState("topoStore", {
      versionSelected: "versionSelected",
    }),
    sublayerFilterList() {
      const list = this.sublayerList.filter((ele) =>
        ele.sublayerName.includes(this.sublayerForm.sublayerName)
      );
      return list;
    },
  },
  methods: {
    show() {
      this.isVisible = true;
      this.getSublayerList();
    },
    getSublayerList() {
      this.$get(`/topoEdit/getExistSublayerList`, {
        mapId: this.mapId,
        versionId: this.versionSelected.versionId,
      }).then(({ data }) => {
        if (data.code == "0000") {
          this.sublayerList = data.data;
        }
      });
    },
    handleInputChange(val) {
      this.sublayerSelected = {
        sublayerName: val,
      };
    },
    handleInputSelect(val) {
      this.sublayerSelected = this.sublayerList.find(
        (ele) => ele.sublayerId === val
      );
      this.sublayerForm.sublayerName = this.sublayerSelected.sublayerName;
      // 点击选择
      this.$refs.formValidate.validate();
    },
    hide() {
      this.isVisible = false;
      this.sublayerForm = {
        sublayerName: "",
      };
      this.sublayerSelected = {};
    },
    generateParams() {
      const { sublayerId, sublayerName } = this.sublayerSelected;
      const sublayerList = this.nodeSelectedList.map((ele) => {
        return {
          sublayerId,
          objType: ele.nodeId ? 1 : 2,
          objId: ele.nodeId || ele.linkId,
        };
      });
      return {
        sublayerId,
        sublayerName,
        mapId: this.mapId,
        sublayerList,
      };
    },
    ok() {
      this.$refs.formValidate.validate((valid) => {
        if (valid) {
          const params = this.generateParams();
          this.$post(`/topoEdit/insertTopoSublayerList`, params)
            .then((data) => {
              if (data.code == "0000") {
                this.$Message.success("添加成功");
                this.$bus.emit("insertTopoSublayerSuccess");
              }
            })
            .finally(() => {
              this.hide();
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
