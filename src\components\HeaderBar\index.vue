<template>
  <div class="head-bar">
    <div class="custom-content-con">
      <span class="user-display-name">{{ displayName }}</span>
      <div class="user-avatar-dropdown">
        <Dropdown>
          <Badge>
            <Avatar size="large">
              <Icon
                style="
                  font-size: 30px;
                  position: absolute;
                  top: 3px;
                  left: -15px;
                "
                type="md-person"
              />
            </Avatar>
          </Badge>
          <DropdownMenu slot="list">
            <DropdownItem><div @click="loginout">注销</div></DropdownItem>
            <!-- <DropdownItem>退出</DropdownItem> -->
          </DropdownMenu>
        </Dropdown>
      </div>
    </div>
  </div>
</template>
<script>
import Cookies from "js-cookie";
export default {
  components: {},
  data() {
    return {
      user: {
        name: "用户",
        avatar: "",
        role: "管理员",
        registeInfo: "注册时间：2019-6-23",
      },
      activeIndex: "1",
      langVisible: false,
      displayName: "",
    };
  },
  methods: {
    loginout() {
      this.$post("/logout", {}).then((res) => {
        if (res.code === "0000") {
          this.$Message.success("注销成功！");
          Cookies.remove("token"); // 放置token到Cookie
          this.$router.replace({ path: "/login" });
        }
      });
    },
  },
  mounted() {
    this.displayName = Cookies.get("displayName");
  },
  computed: {},
};
</script>

<style lang="scss" scoped>
.user-display-name {
  color: #fff;
  margin-right: 10px;
}
.custom-content-con {
  float: right;
  height: auto;
  padding-right: 20px;
  display: flex;
}
</style>
