<template>
  <g
    :id="`group-${formatId(data.nodeId)}`"
    v-if="isTextShow"
    :transform="`translate(${data.tx}, ${data.ty}) 
            rotate(${data.rotate} ${data.middleRotatePoint.x} ${data.middleRotatePoint.y})`"
  >
    <!-- <defs>
      <linearGradient
        :id="`gradient-${formatId(data.nodeId)}`"
        :x1="textStyles.gradientX1 || 0"
        :y1="textStyles.gradientY1 || 0"
        :x2="textStyles.gradientX2 || 0"
        :y2="textStyles.gradientY2 || 0"
      >
        <stop
          :offset="textStyles.gradientOffset1 || 0"
          :style="`stop-color: ${
            textStyles.gradientColor1 || '#ffffff'
          }; stop-opacity: 1`"
        />
        <stop
          :offset="textStyles.gradientOffset2 || 0"
          :style="`stop-color: ${
            textStyles.gradientColor2 || '#ffffff'
          }; stop-opacity: 1`"
        />
        <stop
          :offset="textStyles.gradientOffset3 || 0"
          :style="`stop-color: ${
            textStyles.gradientColor3 || '#ffffff'
          }; stop-opacity: 1`"
        />
      </linearGradient>
    </defs> -->
    <rect
      v-if="nodeStyles.hasBorder"
      class="custom-text-border"
      :width="data.w + 10"
      :height="data.h + 10"
      :x="data.x - 5"
      :y="data.y - 5"
      fill="none"
      stroke-width="4"
      stroke="rgb(3, 110, 184)"
    ></rect>
    <text
      :x="data.x"
      :y="data.y + +data.fontSize"
      :transform="
        textStyles.duration === '竖向'
          ? `rotate(90 ${data.x} ${data.y + +data.fontSize})`
          : ''
      "
      :style="{
        fontSize: data.fontSize + 'px',
        filter: +textStyles.dropShadowBlurRadius
          ? `drop-shadow(0 0 ${textStyles.dropShadowBlurRadius}px ${textStyles.dropShadowColor}) drop-shadow(0 0 ${textStyles.dropShadowBlurRadius}px ${textStyles.dropShadowColor})`
          : 'none',
        ...nodeStyles,
      }"
      class="custom-text"
    >
      <!-- fontFamily: textStyles.fontFamily, -->

      <tspan
        :x="data.x"
        v-for="(item, index) in textCcontent"
        :dy="index === 0 ? 0 : data.fontSize"
        :key="index"
        >{{ item }}</tspan
      >
    </text>
    <!-- 
    <foreignObject :width="data.w" :height="data.h" :x="data.x" :y="data.y">
      <body
        xmlns="http://www.w3.org/1999/xhtml"
        style="background: transparent; weight: 100; height: 100%"
      >
        <div
          :style="{
            fontFamily: textStyles.fontFamily,
            width: '100%',
            height: '100%',
            textAlign: textStyles.align || 'left',
            fontSize: `${data.fontSize}px`,
            color: data.fontColor,
            lineHeight: 1,
          }"
        >
          <span
            v-text="data.nodeText"
            :style="{
              letterSpacing: '0px',
              whiteSpace: 'pre-line',
              textOrientation: 'upright',
              whiteSpace: 'nowrap',
              writingMode:
                textStyles.duration === '竖向' ? 'vertical-lr' : 'unset',
            }"
          ></span>
        </div>
      </body>
    </foreignObject> -->
    <rect
      class="custom-move-rect"
      :width="textStyles.duration === '竖向' ? textStyles.h : textStyles.w"
      :height="textStyles.duration === '竖向' ? textStyles.w : textStyles.h"
      :x="data.x"
      :y="data.y"
      :id="`rect-${formatId(data.nodeId)}`"
      fill="none"
      :stroke-width="isBindNode || data.selected ? 2 : 0"
      :stroke="data.locked ? '#7d8694' : colorSelected"
      stroke-dasharray="3 3"
      @mousedown="handleMousedown"
      @mouseup="handleMouseup"
      @dblclick="handleDbclick"
      style="cursor: move; pointer-events: all"
    ></rect>
  </g>
</template>

<script>
import { mapState } from "vuex";
import { formatId } from "@/utils/tools/format";

const colorList = {
  green: "green",
  pink: "#ff269c",
  orange: "#ff4400",
  blue: "#3360fb",
};

export default {
  name: "CustomText",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
    isBindNode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      text: 3,
    };
  },

  mounted() {},
  computed: {
    ...mapState("topoStore", {
      textColorList: "textColorList",
      colorSelected: "colorSelected",
      textHideBySublayerList: "textHideBySublayerList",
      isHideTextBySublayer: "isHideTextBySublayer",
    }),
    nodeStyles() {
      const { nodeStyles, textStyles, nodeText } = this.data;
      const nodeStylesObj = JSON.parse(nodeStyles);

      if (nodeStylesObj.dynamicColor) {
        nodeStylesObj.fill = colorList[nodeStylesObj.dynamicColor];
      } else {
        const textStylesObj = JSON.parse(textStyles);
        nodeStylesObj.fill = textStylesObj.isGradient
          ? `url(#gradient-${this.formatId(data.nodeId)})`
          : this.data.fontColor;
      }

      const pattern = new RegExp("[\u4E00-\u9FA5]+");
      nodeStylesObj["letter-spacing"] = pattern.test(nodeText) ? "0" : "0";
      nodeStylesObj.fontFamily = "AR PL UKai CN,楷体,楷体_GB2312";
      nodeStylesObj["white-space"] = "pre";
      nodeStylesObj["text-orientation"] = "upright";

      return nodeStylesObj;
    },
    textStyles() {
      const { textStyles, fontSize, nodeText, w, h } = this.data;
      const style = JSON.parse(textStyles);

      if (!w || !h) {
        const textEl = document.getElementById("textWidth");
        textEl.style.fontSize = `${fontSize}px`;
        textEl.textContent = nodeText;
        style.w = textEl.clientWidth;
        style.h = textEl.clientHeight;
      } else {
        style.w = w;
        style.h = h;
      }

      return style;
    },
    textCcontent() {
      const { nodeText } = this.data;
      const list = nodeText.split(/[\r\n|↵]+/);
      return list;
    },
    textPosition() {
      const { textPosition } = this.data;
      const [x, y] = textPosition.split(",");
      return { x: +x, y: +y };
    },
    isTextShow() {
      let { fontColor, sublayerList = [] } = this.data;
      //   属于这个图层的，不展示
      const isInclude = sublayerList.some((ele) =>
        this.textHideBySublayerList.includes(ele.sublayerId)
      );
      if (fontColor.includes("#")) {
        fontColor = this.hexToRgb(fontColor);
      }
      const list = [
        "rgb(255,0,0)",
        "rgb(0,255,0)",
        "rgb(255,255,255)",
        "rgb(51,96,251)",
      ];
      if (this.isHideTextBySublayer) {
        return !isInclude;
      } else if (isInclude && list.includes(fontColor)) {
        return this.textColorList.includes(fontColor);
      } else {
        return true;
      }
    },
    fontSize() {
      const fontSize = +this.data.fontSize;
      const len = this.data.nodeText.length;
      return {
        x: fontSize + len,
        y: fontSize,
      };
    },
    offset() {
      const fontSize = +this.data.fontSize;
      let scale = 6;
      return fontSize / scale;
    },
  },
  methods: {
    formatId,
    hexToRgb(hex) {
      return (
        "rgb(" +
        parseInt("0x" + hex.slice(1, 3)) +
        "," +
        parseInt("0x" + hex.slice(3, 5)) +
        "," +
        parseInt("0x" + hex.slice(5, 7)) +
        ")"
      );
    },
    handleMousedown(e) {
      this.$emit("cusmousedown", e);
    },
    handleMouseup(e) {
      this.$emit("cusmouseup", e);
    },
    handleDbclick(e) {
      this.$emit("cusdbclick", e);
    },
    getText() {
      return `<tspan x="280" dy="26" fill="red">
          SVG 2
        </tspan>`;
    },
  },
};
</script>

<style lang="scss" scoped>
// .custom-text {
//   white-space: pre;
//   text-orientation: upright;
//   user-select: none;
//   letter-spacing: 0;
// }
</style>
