module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: [
    "plugin:vue/essential",
    "eslint:recommended",
    "plugin:prettier/recommended",
  ],
  parserOptions: {
    parser: "@babel/eslint-parser",
  },
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "vue/multi-word-component-names": "off",
    "vue/component-name-in-template-casing": "off",
    "no-unused-vars": "off",
    "vue/return-in-computed-property": "off",
    "vue/no-unused-components": "off",
    "no-undef": "off",
    "vue/no-side-effects-in-computed-properties": "off",
    "no-useless-escape": "off",
    "no-unreachable": "off",
    "vue/no-use-v-if-with-v-for": "off",
  },
};
